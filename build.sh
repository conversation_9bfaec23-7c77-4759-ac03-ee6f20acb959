#!/bin/bash
source  /tmp/evn
sed -i "s@ENV@"${ENV}"@" Makefile

echo 'monitorPort=9902' >>/tmp/evn

serverName=ms-supplier-item
#开发
if [[ "${ENV}" == "dev" ]];then
  backendService=*************:11800
  sed -i "/CMD.*/d" Dockerfile
  #echo 'CMD ["java","-javaagent:/app/skywalking-agent/skywalking-agent.jar", "-Dskywalking.agent.service_name='$serverName'-dev", "-Dskywalking.collector.backend_service='$backendService'","-jar","server.jar"]' >>Dockerfile
  echo 'CMD ["java", "-jar", "server.jar"]' >>Dockerfile
fi
#测试
if [[ "${ENV}" == "test" ]];then
  backendService=*************:11800
  sed -i "/ADD/aADD jacocoagent.jar \/tmp\/jacocoagent.jar" Dockerfile
  sed -i "/CMD.*/d" Dockerfile
  #echo 'CMD ["java","-javaagent:/tmp/jacocoagent.jar=includes=com.daddylab.supplier.item.*,excludes=com.daddylab.supplier.item.infrastructure.*,output=tcpserver,port=8086,address=*","-javaagent:/app/skywalking-agent/skywalking-agent.jar","-Dskywalking.agent.service_name='$serverName'-test", "-Dskywalking.collector.backend_service='$backendService'","-jar","server.jar"]' >>Dockerfile
  echo 'CMD ["java","-javaagent:/tmp/jacocoagent.jar=includes=com.daddylab.supplier.item.*,excludes=com.daddylab.supplier.item.infrastructure.*,output=tcpserver,port=7777,address=cov.dlab.cn", "-jar","server.jar"]' >>Dockerfile
#  echo 'CMD["java", "-jar", "server.jar"]' >>Dockerfile
fi
#灰度
if [[ "${ENV}" == "gray" ]];then
  backendService=************:11800
  sed -i "/CMD.*/d" Dockerfile
  #echo 'CMD ["java","-javaagent:/app/skywalking-agent/skywalking-agent.jar", "-Dskywalking.agent.service_name='$serverName'-gray", "-Dskywalking.collector.backend_service='$backendService'","-jar","server.jar"]' >>Dockerfile
  echo 'CMD ["java", "-Xmx3072m", "-jar", "server.jar"]' >>Dockerfile
fi
#生产
if [[ "${ENV}" == "prod" ]];then
  backendService=************:11800
  sed -i "/CMD.*/d" Dockerfile
  #echo 'CMD ["java","-javaagent:/app/skywalking-agent/skywalking-agent.jar", "-Dskywalking.agent.service_name='$serverName'-prod", "-Dskywalking.collector.backend_service='$backendService'","-jar","server.jar"]' >>Dockerfile
  echo 'CMD ["java", "-Xmx3072m", "-jar", "server.jar"]' >>Dockerfile
fi
