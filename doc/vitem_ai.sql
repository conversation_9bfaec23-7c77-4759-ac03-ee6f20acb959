-- 文件上传记录表
CREATE TABLE `item_market_file_record`
(
    `id`          bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`  bigint        DEFAULT 0  NOT NULL COMMENT '创建时间',
    `created_uid` bigint        DEFAULT 0  NOT NULL COMMENT '创建人',
    `updated_at`  bigint        DEFAULT 0  NOT NULL COMMENT '更新时间',
    `updated_uid` bigint        DEFAULT 0  NOT NULL COMMENT '更新人',
    `is_del`      bigint        DEFAULT 0  NOT NULL COMMENT '是否已删除',
    `deleted_at`  bigint        DEFAULT 0  NOT NULL COMMENT '删除时间',
    `item_id`     bigint(30)    DEFAULT 0  NOT NULL COMMENT '商品ID',
    `item_code`   varchar(50)   DEFAULT '' NOT NULL COMMENT '商品code',
    `file_name`   varchar(255)  DEFAULT '' NOT NULL COMMENT '文件名',
    `file_url`    varchar(1024) DEFAULT '' NOT NULL COMMENT '文件 URL',
    INDEX `idx_item_id` (`item_id`),
    INDEX `idx_item_code` (`item_code`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品营销文件记录';


CREATE TABLE `item_market_analysis_record`
(
    `id`                              bigint AUTO_INCREMENT COMMENT 'id'
        PRIMARY KEY,
    `created_at`                      bigint      DEFAULT 0  NOT NULL COMMENT '创建时间',
    `created_uid`                     bigint      DEFAULT 0  NOT NULL COMMENT '创建人',
    `updated_at`                      bigint      DEFAULT 0  NOT NULL COMMENT '更新时间',
    `updated_uid`                     bigint      DEFAULT 0  NOT NULL COMMENT '更新人',
    `is_del`                          bigint      DEFAULT 0  NOT NULL COMMENT '是否已删除',
    `deleted_at`                      bigint      DEFAULT 0  NOT NULL COMMENT '删除时间',
    `item_id`                         bigint(30)  DEFAULT 0  NOT NULL COMMENT '商品ID',
    `item_code`                       varchar(50) DEFAULT '' NOT NULL COMMENT '商品code',
    `input_tokens`                    int         DEFAULT 0  NOT NULL COMMENT 'token输入',
    `output_tokens`                   int         DEFAULT 0  NOT NULL COMMENT 'token输出',
    `total_tokens`                    int         DEFAULT 0  NOT NULL COMMENT 'token总数',
    `res`                             text                   NULL COMMENT 'LLM响应',
    `product_titles`                  text                   NULL COMMENT '商品长标题',
    `short_product_titles`            text                   NULL COMMENT '商品短标题',
    `search_keywords`                 text                   NULL COMMENT '搜索关键词',
    `target_audience_analysis`        text                   NULL COMMENT '目标用户分析',
    `product_selling_points`          text                   NULL COMMENT '商品卖点',
    `customer_service_bot_scripts`    text                   NULL COMMENT '客服机器人话术',
    `general_product_attributes`      text                   NULL COMMENT '通用商品属性',
    `general_usage_suggestions`       text                   NULL COMMENT '通用使用建议',
    `summary_and_content_suggestions` text                   NULL COMMENT '总结与内容建议',
    `status`                          varchar(50) default 'PENDING' comment 'MarketAnalysisStatus',
    `error_msg`                       text                   NULL COMMENT '异常失败信息',
    `title_key_confirm`               tinyint     default 0 comment '商品标题/短标题/搜索关键词。确认',
    `target_sell_confirm`             tinyint     default 0 comment '适用人群/商品卖点。确认',
    `bot_scripts_confirm`             tinyint     default 0 comment '机器人话术。确认',
    `general_confirm`                 tinyint     default 0 comment '通用商品属性/通用使用建议。确认',
    `optimize_count`                  int         default 0 comment '手动更新纠正次数',
    `qc_advice`                       text                   NULL COMMENT '修改建议',
    `qc_advice_status`                tinyint     default 0  not null comment '修改建议状态。0待确认。1确认',
    `legal_advice`                    text                   NULL COMMENT '修改建议',
    `legal_advice_status`             tinyint     default 0  not null comment '修改建议状态。0待确认。1确认',
    `tech_file_id`                    bigint      default null comment 'TECH fileId',
    INDEX `idx_item_id` (`item_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='商品 AI 分析记录';











