-- 库存告警系统数据库表创建脚本
-- 版本: v4.2.1
-- 创建时间: 2025-01-29

-- 1. 类目警戒库存表
CREATE TABLE `category_warn_stock` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `category_id` bigint(20) NOT NULL COMMENT '类目ID',
  `warn_stock` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '警戒库存数量',
  `created_at` bigint(20) NOT NULL COMMENT '创建时间',
  `created_uid` bigint(20) NOT NULL COMMENT '创建人',
  `updated_at` bigint(20) NOT NULL COMMENT '更新时间',
  `updated_uid` bigint(20) NOT NULL COMMENT '更新人',
  `is_del` int(11) NOT NULL DEFAULT '0' COMMENT '是否删除 0:未删除 1:已删除',
  `deleted_at` bigint(20) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_id` (`category_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='类目警戒库存配置表';

-- 2. 库存告警记录表
CREATE TABLE `stock_warn_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `target_user_id` bigint(20) NOT NULL COMMENT '发送目标用户ID',
  `target_user_name` varchar(100) NOT NULL COMMENT '发送目标用户花名',
  `email_address` varchar(255) NOT NULL COMMENT '邮件地址',
  `send_status` int(11) NOT NULL DEFAULT '0' COMMENT '发送状态 0:待发送 1:发送成功 2:发送失败',
  `attachment_oss_url` varchar(500) DEFAULT NULL COMMENT '附件OSS链接',
  `created_at` bigint(20) NOT NULL COMMENT '创建时间',
  `created_uid` bigint(20) NOT NULL COMMENT '创建人',
  `updated_at` bigint(20) NOT NULL COMMENT '更新时间',
  `updated_uid` bigint(20) NOT NULL COMMENT '更新人',
  `is_del` int(11) NOT NULL DEFAULT '0' COMMENT '是否删除 0:未删除 1:已删除',
  `deleted_at` bigint(20) DEFAULT NULL COMMENT '删除时间',
  `send_time` bigint(20) DEFAULT NULL COMMENT '发送时间',
  `error_message` text COMMENT '发送失败时的错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_target_user_id` (`target_user_id`),
  KEY `idx_send_status` (`send_status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存告警记录表';

-- 3. 库存告警商品明细表
CREATE TABLE `stock_warn_item_detail` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `warn_record_id` bigint(20) NOT NULL COMMENT '告警记录ID',
  `item_code` varchar(100) NOT NULL COMMENT '商品编号',
  `item_name` varchar(255) NOT NULL COMMENT '商品名称',
  `category_id` bigint(20) NOT NULL COMMENT '类目ID',
  `category_name` varchar(255) NOT NULL COMMENT '类目名称',
  `current_stock` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '实时库存',
  `warn_stock` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '警戒库存',
  `warehouse_id` bigint(20) NOT NULL COMMENT '仓库ID',
  `warehouse_name` varchar(255) NOT NULL COMMENT '仓库名称',
  `created_at` bigint(20) NOT NULL COMMENT '创建时间',
  `created_uid` bigint(20) NOT NULL COMMENT '创建人',
  `updated_at` bigint(20) NOT NULL COMMENT '更新时间',
  `updated_uid` bigint(20) NOT NULL COMMENT '更新人',
  `is_del` int(11) NOT NULL DEFAULT '0' COMMENT '是否删除 0:未删除 1:已删除',
  `deleted_at` bigint(20) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_warn_record_id` (`warn_record_id`),
  KEY `idx_item_code` (`item_code`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_warehouse_id` (`warehouse_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='库存告警商品明细表';
