-- Add new SKU dimension fields to item_sku table
-- 重量(kg)、长(cm)、宽(cm)、高(cm)、体积(m³)

ALTER TABLE `item_sku`
    ADD COLUMN `weight` DECIMAL(6, 2) DEFAULT '0.00' COMMENT '重量(kg)',
    ADD COLUMN `length` DECIMAL(6, 2) DEFAULT '0.00' COMMENT '长度(cm)',
    ADD COLUMN `width`  DECIMAL(6, 2) DEFAULT '0.00' COMMENT '宽度(cm)',
    ADD COLUMN `height` DECIMAL(6, 2) DEFAULT '0.00' COMMENT '高度(cm)',
    ADD COLUMN `volume` DECIMAL(9, 6) DEFAULT '0.00' COMMENT '体积(m³)';

-- 为平台商品表添加合同状态字段
ALTER TABLE `platform_item`
    ADD COLUMN `contract_status` TINYINT NULL DEFAULT NULL COMMENT '合同状态 0:未关联合同 1:生效中 2:临期 3:已失效',
    ADD INDEX `idx_contract_status` (`contract_status`) COMMENT '索引（合同状态）';

-- 邮件发送记录表
DROP TABLE IF EXISTS `email_send_record`;
CREATE TABLE `email_send_record`
(
    `id`            BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
    `recipient`     VARCHAR(255) NOT NULL COMMENT '收件人邮箱地址',
    `subject`       VARCHAR(500) NOT NULL COMMENT '邮件主题',
    `content`       TEXT COMMENT '邮件内容',
    `sender`        VARCHAR(255) NOT NULL COMMENT '发件人邮箱地址',
    `status`        TINYINT      NOT NULL DEFAULT 0 COMMENT '发送状态 0:待发送 1:发送成功 2:发送失败',
    `error_message` TEXT COMMENT '发送失败时的错误信息',
    `send_time`     BIGINT       NOT NULL DEFAULT 0 COMMENT '发送时间戳',
    `created_at`    BIGINT       NOT NULL DEFAULT 0 COMMENT '创建时间',
    `created_uid`   BIGINT       NOT NULL DEFAULT 0 COMMENT '创建人ID',
    `updated_at`    BIGINT       NOT NULL DEFAULT 0 COMMENT '更新时间',
    `updated_uid`   BIGINT       NOT NULL DEFAULT 0 COMMENT '更新人ID',
    `is_del`        TINYINT      NOT NULL DEFAULT 0 COMMENT '是否删除 0:未删除 1:已删除',
    INDEX `idx_recipient` (`recipient`) COMMENT '收件人索引',
    INDEX `idx_status` (`status`) COMMENT '状态索引',
    INDEX `idx_send_time` (`send_time`) COMMENT '发送时间索引',
    INDEX `idx_created_at` (`created_at`) COMMENT '创建时间索引'
) COMMENT '邮件发送记录表';

-- 新建其他出库单模块表结构
-- 其他出库单主表
drop table if exists `other_outbound_order`;
CREATE TABLE `other_outbound_order`
(
    `id`                         BIGINT AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    `order_no`                   VARCHAR(50)  NOT NULL COMMENT '出库单号',
    `type`                       TINYINT      NOT NULL DEFAULT 0 COMMENT '类型 1:样品、2:办公领用、3:礼品赠送、4:线下内购、5:员工福利、6:捐赠出库、7:供应商索赔、8:抽样质检、9:报废、10:盘亏出库、11:活动奖品',
    `business_type`              TINYINT      NOT NULL DEFAULT 0 COMMENT '业务类型 1:商城首检、2:商城抽检、3:绿色家装、4:评测科普、5:老爸抽检、6:老爸抽检-抽检、7:产品研究、8:其他',
    `is_marketing_demand`        TINYINT      NOT NULL DEFAULT 0 COMMENT '运营/直播/营销需求 0:否 1:是',
    `applicant_department_id`    BIGINT       NOT NULL COMMENT '申请人部门',
    `sample_purpose`             TINYINT      NOT NULL DEFAULT '0' COMMENT '样品用途 1:试用/检测样品、2:拍摄使用（含拍摄道具）、3:直播间使用、4:其他',
    `channel`                    VARCHAR(100) NOT NULL DEFAULT '' COMMENT '渠道',
    `cost_bearing_department_id` BIGINT       NOT NULL DEFAULT '0' COMMENT '费用承担部门',
    `has_gift`                   TINYINT      NOT NULL DEFAULT '0' COMMENT '是否有赠品 0:否 1:是',
    `purpose`                    VARCHAR(500) NOT NULL DEFAULT '' COMMENT '用途',
    `consignee`                  VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '收货人',
    `province_code`              VARCHAR(10)  NOT NULL DEFAULT '' COMMENT '省市区代码',
    `city_code`                  VARCHAR(10)  NOT NULL DEFAULT '' COMMENT '省市区代码',
    `district_code`              VARCHAR(10)  NOT NULL DEFAULT '' COMMENT '省市区代码',
    `contact_mobile`             VARCHAR(20)  NOT NULL DEFAULT '' COMMENT '联系手机号',
    `detailed_address`           VARCHAR(500) NOT NULL DEFAULT '' COMMENT '详细地址',
    `status`                     TINYINT      NOT NULL DEFAULT 0 COMMENT '状态 -1:撤回审核 0:待提交 1:待审核 2:已审核 3:审核拒绝 4:已关闭',
    `compensation_amount`        DECIMAL(10, 2)        DEFAULT 0.00 COMMENT '索赔金额',
    `workbench_process_id`       VARCHAR(100)          DEFAULT '' COMMENT '工作台流程ID',
    `remark`                     VARCHAR(500)          DEFAULT NULL COMMENT '备注',
    `submit_time`                BIGINT       NOT NULL DEFAULT 0 COMMENT '提交时间',
    `submit_uid`                 BIGINT       NOT NULL DEFAULT 0 COMMENT '提交人',
    `created_at`                 BIGINT       NOT NULL DEFAULT 0 COMMENT '创建时间',
    `created_uid`                BIGINT       NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_at`                 BIGINT       NOT NULL DEFAULT 0 COMMENT '更新时间',
    `updated_uid`                BIGINT       NOT NULL DEFAULT 0 COMMENT '更新人',
    `is_del`                     BIGINT       NOT NULL DEFAULT 0 COMMENT '是否删除',
    UNIQUE KEY `uk_order_no` (`order_no`, `is_del`) COMMENT '出库单号唯一索引',
    INDEX `idx_status` (`status`) COMMENT '状态索引',
    INDEX `idx_created_at` (`created_at`) COMMENT '创建时间索引'
) COMMENT '其他出库单主表';

-- 其他出库单明细表
drop table if exists `other_outbound_order_item`;
CREATE TABLE `other_outbound_order_item`
(
    `id`                BIGINT AUTO_INCREMENT COMMENT '主键ID'
        PRIMARY KEY,
    `created_at`        BIGINT       NOT NULL DEFAULT 0 COMMENT '创建时间',
    `created_uid`       BIGINT       NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_at`        BIGINT       NOT NULL DEFAULT 0 COMMENT '更新时间',
    `updated_uid`       BIGINT       NOT NULL DEFAULT 0 COMMENT '更新人',
    `is_del`            BIGINT       NOT NULL DEFAULT 0 COMMENT '是否删除',
    `order_id`          BIGINT       NOT NULL COMMENT '出库单ID',
    `project_name`      VARCHAR(200) NOT NULL DEFAULT '' COMMENT '项目名称',
    `sku_code`          VARCHAR(50)  NOT NULL DEFAULT '' COMMENT '商品SKU编码',
    `item_id`           BIGINT       NOT NULL DEFAULT '0' COMMENT '商品ID',
    `sku_id`            BIGINT       NOT NULL DEFAULT '0' COMMENT '商品SKUID',
    `quantity`          INT          NOT NULL DEFAULT 1 COMMENT '数量',
    `actual_amount`     DECIMAL(10, 2)        DEFAULT 0.00 COMMENT '实际金额（成本价）',
    `remark`            VARCHAR(500)          DEFAULT NULL COMMENT '备注',
    `wdt_no`            VARCHAR(50)           DEFAULT NULL COMMENT 'WDT出库单号',
    `complete_quantity` INT          NOT NULL DEFAULT 0 COMMENT '完好数量',
    `broken_quantity`   INT          NOT NULL DEFAULT 0 COMMENT '损坏数量',
    `give_quantity`     INT          NOT NULL DEFAULT 0 COMMENT '交给综合部数量',
    INDEX `idx_order_id` (`order_id`) COMMENT '出库单ID索引',
    INDEX `idx_sku_code` (`sku_code`) COMMENT 'SKU编码索引'
) COMMENT '其他出库单明细表';


-- 其他出库单赠品明细表
drop table if exists `other_outbound_order_gift_item`;
CREATE TABLE `other_outbound_order_gift_item`
(
    `id`             BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
    `created_at`     BIGINT       NOT NULL DEFAULT 0 COMMENT '创建时间',
    `created_uid`    BIGINT       NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_at`     BIGINT       NOT NULL DEFAULT 0 COMMENT '更新时间',
    `updated_uid`    BIGINT       NOT NULL DEFAULT 0 COMMENT '更新人',
    `is_del`         BIGINT       NOT NULL DEFAULT 0 COMMENT '是否删除',
    `deleted_at`     BIGINT       NOT NULL DEFAULT 0 COMMENT '删除时间',
    `order_id`       BIGINT       NOT NULL COMMENT '出库单ID',
    `item_name`      VARCHAR(200) NOT NULL COMMENT '赠品名称',
    `specifications` VARCHAR(200) NOT NULL COMMENT '规格',
    `quantity`       INT          NOT NULL DEFAULT 1 COMMENT '数量',
    INDEX `idx_order_id` (`order_id`) COMMENT '出库单ID索引'
) COMMENT '其他出库单赠品明细表';

-- 直播管理模块表结构
-- 直播管理主表
DROP TABLE IF EXISTS `live_manage`;
CREATE TABLE `live_manage`
(
    `id`               BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
    `live_no`          VARCHAR(50)  NOT NULL COMMENT '直播编号',
    `oa_process_no`    VARCHAR(100)          DEFAULT NULL COMMENT 'OA流程编号',
    `contract_type1`   VARCHAR(50)           DEFAULT NULL COMMENT '合同类型（1级）',
    `contract_type2`   VARCHAR(50)           DEFAULT NULL COMMENT '合同类型（2级）- 对应二级类目',
    `contract_type3`   VARCHAR(50)           DEFAULT NULL COMMENT '合同类型（3级）- 对应合作类型',
    `application_time` BIGINT       NOT NULL DEFAULT 0 COMMENT '申请时间',
    `pass_time`        BIGINT       NOT NULL DEFAULT 0 COMMENT '审核通过时间',
    `company_name`     VARCHAR(200) NOT NULL DEFAULT '' COMMENT '公司名称',
    `apply_oa_id`      VARCHAR(100) NOT NULL DEFAULT '' COMMENT '申请人OAID',
    `apply_user_id`    BIGINT       NOT NULL DEFAULT 0 COMMENT '申请人ID',
    `created_at`       BIGINT       NOT NULL DEFAULT 0 COMMENT '创建时间',
    `created_uid`      BIGINT       NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_at`       BIGINT       NOT NULL DEFAULT 0 COMMENT '更新时间',
    `updated_uid`      BIGINT       NOT NULL DEFAULT 0 COMMENT '更新人',
    `is_del`           TINYINT      NOT NULL DEFAULT 0 COMMENT '是否删除 0:未删除 1:已删除',
    UNIQUE KEY `uk_live_no` (`live_no`, `is_del`) COMMENT '直播编号唯一索引',
    INDEX `idx_cooperation_type` (`cooperation_type`) COMMENT '合作类型索引',
    INDEX `idx_company_name` (`company_name`) COMMENT '公司名称索引',
    INDEX `idx_apply_oa_id` (`apply_oa_id`) COMMENT '申请人OAID索引',
    INDEX `idx_apply_user_id` (`apply_user_id`) COMMENT '申请人ID索引',
    INDEX `idx_created_at` (`created_at`) COMMENT '创建时间索引'
) COMMENT '直播管理主表';


-- 直播管理商品明细表
# DROP TABLE IF EXISTS `live_manage_item`;
CREATE TABLE `live_manage_item`
(
    `id`                       BIGINT AUTO_INCREMENT COMMENT '主键ID' PRIMARY KEY,
    `live_manage_id`           BIGINT         NOT NULL COMMENT '直播管理ID',
    `shop_name`                VARCHAR(200)   NOT NULL DEFAULT '' COMMENT '店铺名称',
    `shop_link`                VARCHAR(500)            DEFAULT NULL COMMENT '店铺链接',
    `item_no`                  VARCHAR(50)             DEFAULT NULL COMMENT 'P系统商品编码',
    `item_name`                VARCHAR(200)   NOT NULL COMMENT '商品名称',
    `contract_start_time`      BIGINT         NOT NULL DEFAULT 0 COMMENT '合同开始时间',
    `contract_end_time`        BIGINT         NOT NULL DEFAULT 0 COMMENT '合同结束时间',
    `contract_amount`          DECIMAL(10, 2) NOT NULL DEFAULT '0.00' COMMENT '合同金额',
    `online_commission`        VARCHAR(20)             DEFAULT NULL COMMENT '线上佣金',
    `offline_commission`       VARCHAR(20)             DEFAULT NULL COMMENT '线下佣金',
    `live_price`               VARCHAR(20)             DEFAULT NULL COMMENT '直播价',
    `flow_fee_agreement`       TEXT                    DEFAULT NULL COMMENT '投流费约定',
    `live_start_time`          BIGINT         NOT NULL COMMENT '直播日期开始时间',
    `live_end_time`            BIGINT         NOT NULL COMMENT '直播日期结束时间',
    `dou_product_id`           VARCHAR(200)            DEFAULT NULL COMMENT '抖音商品ID（多选）',
    `dou_product_name`         VARCHAR(200)            DEFAULT NULL COMMENT '抖音商品名称',
    `dou_product_data`         TEXT                    DEFAULT NULL COMMENT '抖音商品数据',
    `dou_cos_ratio`            DECIMAL(10, 2)          DEFAULT NULL COMMENT '抖音当前佣金率（多个商品平均佣金率）',
    `dou_advertiser_id`        VARCHAR(200)            DEFAULT NULL COMMENT '抖音广告主ID',
    `dou_aweme_id`             VARCHAR(200)            DEFAULT NULL COMMENT '抖音账号ID',
    `dou_live_room_id`         VARCHAR(200)            DEFAULT NULL COMMENT '抖音直播间ID（多选）',
    `dou_live_room_name`       VARCHAR(200)            DEFAULT NULL COMMENT '抖音直播间名称',
    `dou_live_room_start_time` BIGINT         NOT NULL DEFAULT 0 COMMENT '直播间开始时间',
    `dou_live_room_end_time`   BIGINT         NOT NULL DEFAULT 0 COMMENT '直播间结束时间',
    `dou_live_room_flow_ratio` DECIMAL(10, 2)          DEFAULT NULL COMMENT '抖音直播间当前投流比例（多个直播间平均投流比例）',
    `dou_live_room_data`       TEXT                    DEFAULT NULL COMMENT '抖音直播间数据',
    `created_at`               BIGINT         NOT NULL DEFAULT 0 COMMENT '创建时间',
    `created_uid`              BIGINT         NOT NULL DEFAULT 0 COMMENT '创建人',
    `updated_at`               BIGINT         NOT NULL DEFAULT 0 COMMENT '更新时间',
    `updated_uid`              BIGINT         NOT NULL DEFAULT 0 COMMENT '更新人',
    `is_del`                   TINYINT        NOT NULL DEFAULT 0 COMMENT '是否删除 0:未删除 1:已删除',
    INDEX `idx_live_manage_id` (`live_manage_id`) COMMENT '直播管理ID索引',
    INDEX `idx_item_no` (`item_no`) COMMENT 'P系统商品编码索引'
) COMMENT '直播管理商品明细表';



# alter table live_manage_item
#     change live_date `live_start_time` BIGINT NOT NULL COMMENT '直播日期开始时间',
#     add `live_end_time` BIGINT       NOT NULL COMMENT '直播日期结束时间' after live_start_time,
#     add `shop_name`     VARCHAR(200) NOT NULL COMMENT '店铺名称' after live_manage_id
# ;

alter table open_app
    modify platform tinyint default 1 not null comment '平台 1:淘宝 2:有赞 3:抖音 4:快手小店 5:小红书 6:口袋通 7:自研商城 8:拼多多',
    add type   varchar(32) default '' not null comment '类型，某些平台需要进一步区分',
    add remark varchar(255) comment '备注'
;

update open_app
set type = 'DOUDIAN'
where platform = 3;

INSERT INTO open_app (id, platform, type, uri, app_id, app_name, app_key, app_secret, created_at, updated_at, remark)
VALUES (3, 3, 'DOUYIN', 'https://open.douyinec.com', 'aw0jg3bbq983mh5p', '老爸评测精选联盟应用', 'aw0jg3bbq983mh5p',
        '0ea41a3a48a8b10466638d66fc067a7b', unix_timestamp(),
        unix_timestamp(), '抖音开放平台-精选联盟应用'),
       (4, 3, 'QIANCHUAN', 'https://open.douyinec.com', '1843491054448052', '老爸ERP',
        '1843491054448052',
        '73aa3ef36e1d0188dc3dbcefbbc72115caad593c', unix_timestamp(), unix_timestamp(), '巨量千川-自研投放系统-广告主');
create table oauth_token
(
    id                       bigint auto_increment comment 'id'
        primary key,
    created_at               bigint       default 0  not null comment '创建时间createAt',
    created_uid              bigint       default 0  not null comment '创建人updateUser',
    updated_at               bigint       default 0  not null comment '更新时间updateAt',
    updated_uid              bigint       default 0  not null comment '更新人updateUser',
    is_del                   bigint       default 0  not null comment '是否已删除',
    deleted_at               bigint       default 0  not null comment '删除时间',
    subject_id               varchar(64)  default '' not null comment '开放平台授权主体ID（如店铺编号、抖音账号openid...）',
    open_app_id              bigint                  not null comment '关联了获取此令牌的应用信息',
    platform                 tinyint      default 1  not null comment '平台 1:淘宝 2:有赞 3:抖店 4:快手小店 5:小红书 6:口袋通 7:自研商城',
    type                     varchar(32)  default '' not null comment '类型，某些平台需要进一步区分，与 open_app.type 一致',
    access_token             varchar(512)            not null comment '店铺授权访问令牌',
    expired_at               bigint       default 0  not null comment '令牌失效时间',
    refresh_token            varchar(512) default '' not null comment '令牌刷新凭证',
    refresh_token_expired_at bigint       default 0  not null comment '刷新凭证失效时间',
    scopes                   varchar(512) default '' not null comment '授权域',
    token_data               text                    null comment '平台响应数据',
    index subject_id__index (subject_id)
) comment '开放平台授权';

INSERT INTO supplier.oauth_token (id, created_at, created_uid, updated_at, updated_uid, is_del, deleted_at, subject_id, open_app_id, platform, type, access_token, expired_at, refresh_token, refresh_token_expired_at, scopes, token_data) VALUES (1, 1758592670, 0, 1758592670, 0, 0, 0, '_000ev5N-GS16HSppU6OaK0cyl8bp2AOCwCX', 3, 3, 'DOUYIN', 'act.3.V1B6zMOzRaqDbBfPNB3hSQDyEV21jA6KvHSpMETTbmXEWsaBpsFmupeYvJ8tzCRquxR6lS2KuK4zX3OrSSBjiEeyuPzq0TfWk699IcgVoIM0enfoMjD9fy2fQxatGKsC8zSlVhWpDob-WKmcKH8Qpf9qcsp8HeExWy5b2g==_lf', 1759394341, 'rft.65a2f94047ad685fb9492ba4a7fdc38cMmSA35FjDYhd0wcy336KwD30wlN2_lf', 1760690341, 'alliance.kol.orders,alliance.kol.store_manage', '{"data":{"access_token":"act.3.V1B6zMOzRaqDbBfPNB3hSQDyEV21jA6KvHSpMETTbmXEWsaBpsFmupeYvJ8tzCRquxR6lS2KuK4zX3OrSSBjiEeyuPzq0TfWk699IcgVoIM0enfoMjD9fy2fQxatGKsC8zSlVhWpDob-WKmcKH8Qpf9qcsp8HeExWy5b2g==_lf","captcha":"","desc_url":"","description":"","error_code":0,"expires_in":1296000,"log_id":"202509171639016EFA8638EE899A1E8A36","open_id":"_000ev5N-GS16HSppU6OaK0cyl8bp2AOCwCX","refresh_expires_in":2592000,"refresh_token":"rft.65a2f94047ad685fb9492ba4a7fdc38cMmSA35FjDYhd0wcy336KwD30wlN2_lf","scope":"alliance.kol.orders,alliance.kol.store_manage"},"message":"success"}');
INSERT INTO supplier.oauth_token (id, created_at, created_uid, updated_at, updated_uid, is_del, deleted_at, subject_id, open_app_id, platform, type, access_token, expired_at, refresh_token, refresh_token_expired_at, scopes, token_data) VALUES (2, 1758592670, 0, 1758858153, 0, 0, 0, '4064232375333911', 4, 3, 'QIANCHUAN', '0c4bc9ec4fb12ab6a53cf2663327aa42d5c9ebe1', 1758943253, '4ce4c7467cd2dc104fddc932d26034c7e84035d4', 1761448853, '', '{"code":0,"data":{"accessToken":"0c4bc9ec4fb12ab6a53cf2663327aa42d5c9ebe1","expiresIn":86230,"refreshToken":"4ce4c7467cd2dc104fddc932d26034c7e84035d4","refreshTokenExpiresIn":2591830},"message":"OK","requestId":"202509261123430FB242FB1897365C11B2"}');
