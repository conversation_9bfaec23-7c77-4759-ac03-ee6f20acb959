-- auto-generated definition
-- auto-generated definition
create table purchase_cost_change_record
(
    id                   bigint auto_increment comment 'id'
        primary key,
    created_at           bigint        default 0  not null comment '创建时间',
    created_uid          bigint        default 0  not null comment '创建人',
    updated_at           bigint        default 0  not null comment '更新时间',
    updated_uid          bigint        default 0  not null comment '更新人',
    is_del               tinyint       default 0  not null comment '是否删除，不能做业务。这是软删除标记',
    no                   varchar(50)              not null comment '变更单编码',
    provider_id          bigint        default 0  null comment '合作方 ID',
    change_type          tinyint       default 1  null comment '变更类型 1.日常成本，2.活动成本，3.阶梯供价',
    buyer_id             varchar(200)  default '' null comment '采购员信息',
    change_reason        varchar(1024) default '' null comment '变更原因',
    start_time           bigint                   not null comment '开始时间',
    proof_url            text                     null comment '上传凭证',
    is_mall              tinyint       default 0  null comment '是否是电商合作方',
    is_decoration        tinyint       default 0  null comment '是否是绿色家装合作方',
    cost_price_list      text                     null comment '商品价格明细JSON',
    activity_price_list  text                     null comment '商品活动价格明细 JSON',
    multi_price_list     text                     null comment '商品阶梯供价价格明细 JSON',
    sku_count            int           default 0  null comment 'SKU累计去重',
    sku_code             text                     null comment 'skuCode',
    status               int           default 0  null comment '-1.审核失败。0待审核。1审核通过',
    workbench_process_id varchar(200)  default '' null comment '工作台流程 ID'
)
    comment '采购成本变更单';



create table purchase_cost_change_record_ref
(
    id          bigint auto_increment comment 'id'
        primary key,
    created_at  bigint default 0 not null comment '创建时间',
    created_uid bigint default 0 not null comment '创建人',
    updated_at  bigint default 0 not null comment '更新时间',
    updated_uid bigint default 0 not null comment '更新人',
    is_del      bigint default 0 not null comment '是否删除，不能做业务。这是软删除标记',
    no          varchar(50)      not null comment '变更单编码',
    record_id   bigint           not null comment '变更单 ID',
    sku_code    varchar(50)      not null comment 'sku_code',
    item_id     bigint           not null comment 'item_id'
)
    comment '采购成本变更单关联 SKU';




