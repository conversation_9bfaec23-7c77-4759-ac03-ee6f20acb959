package com.daddylab.supplier.item.application.liveManage;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.conditions.query.QueryChainWrapper;
import com.bytedance.ads.api.CustomerCenterAdvertiserListV2Api;
import com.bytedance.ads.api.QianchuanAwemeAuthorizedGetV10Api;
import com.bytedance.ads.api.QianchuanTodayLiveRoomDetailGetV10Api;
import com.bytedance.ads.model.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.bytedance.ads.ApiClient;
import com.bytedance.ads.api.QianchuanTodayLiveRoomGetV10Api;
import com.bytedance.ads.model.QianchuanTodayLiveRoomGetV10Response;
import com.daddylab.supplier.item.application.liveManage.types.*;
import com.daddylab.supplier.item.application.oa.LiveManageConfig;
import com.daddylab.supplier.item.application.oa.OaLiveContractParser;
import com.daddylab.supplier.item.application.oa.types.OaLiveContractCallbackRequest;
import com.daddylab.supplier.item.application.oa.types.ParsedLiveContractData;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.bytedance.douyin.DouyinClientFactory;
import com.daddylab.supplier.item.domain.bytedance.qianchuan.models.BaseResponse;
import com.daddylab.supplier.item.domain.bytedance.qianchuan.models.KolStoreListResults;
import com.daddylab.supplier.item.domain.bytedance.qianchuan.models.StoreGoodDetail;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOauthTokenService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.LiveManage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.LiveManageItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OauthToken;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ILiveManageItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ILiveManageService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOpenAppService;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.Logs;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.douyin.openapi.client.Client;
import com.douyin.openapi.client.models.CommonRequest;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.function.Function;
import java.util.Calendar;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.util.Assert;

@Slf4j
@Service
public class LiveManageBizServiceImpl implements LiveManageBizService {

  @Resource private ILiveManageService liveManageService;

  @Resource private ILiveManageItemService liveManageItemService;

  @Resource private OaLiveContractParser oaLiveContractParser;

  @Resource private StaffService staffService;
  @Resource private LiveManageConfig liveManageConfig;

  @Resource private DouyinClientFactory douyinClientFactory;

  @Resource private IOpenAppService openAppService;

  @Resource private IOauthTokenService oauthTokenService;

  @Resource private OperateLogDomainService operateLogDomainService;

  @Override
  public PageResponse<LiveManageListItemVO> page(LiveManageListQuery query) {
    log.info("分页查询直播管理列表，查询条件：{}", query);

    // 构建查询条件
    QueryWrapper<LiveManage> queryWrapper = buildQueryWrapper(query);
    if (queryWrapper == null) {
      return ResponseFactory.emptyPage();
    }

    // 执行分页查询
    Page<LiveManage> page = new Page<>(query.getPageIndex(), query.getPageSize());
    Page<LiveManage> result = liveManageService.page(page, queryWrapper);

    if (CollUtil.isEmpty(result.getRecords())) {
      return ResponseFactory.emptyPage();
    }

    List<Long> liveManageIds =
        result.getRecords().stream().map(LiveManage::getId).collect(Collectors.toList());
    List<LiveManageItem> items =
        liveManageItemService
            .lambdaQuery()
            .in(LiveManageItem::getLiveManageId, liveManageIds)
            .list();
    Map<Long, List<LiveManageItem>> liveManageItemMap =
        items.stream().collect(Collectors.groupingBy(LiveManageItem::getLiveManageId));

    // 转换为VO对象
    List<LiveManageListItemVO> list =
        result.getRecords().stream().map(this::convertToListItem).collect(Collectors.toList());

    // 设置直播日期
    list.forEach(
        item -> {
          List<LiveManageItem> liveManageItems = liveManageItemMap.get(item.getId());
          if (CollUtil.isNotEmpty(liveManageItems)) {
            item.setLiveDate(
                liveManageItems.stream()
                    .map(
                        v -> {
                          if (v.getLiveStartTime() != null && v.getLiveEndTime() != null) {
                            return DateUtil.formatDate(v.getLiveStartTime())
                                + " - "
                                + DateUtil.formatDate(v.getLiveEndTime());
                          }
                          return "";
                        })
                    .filter(StringUtil::isNotBlank)
                    .collect(Collectors.toList()));
          }
        });

    // 批量设置员工姓名
    setStaffNames(list);

    return PageResponse.of(
        list, (int) result.getTotal(), query.getPageSize(), query.getPageIndex());
  }

  @Override
  public SingleResponse<LiveManageDetailVO> detail(Long id) {
    log.info("查询直播档案详情，ID：{}", id);

    // 查询主表信息
    LiveManage liveManage = liveManageService.getById(id);
    if (Objects.isNull(liveManage)) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "直播管理记录不存在");
    }

    // 转换为VO
    LiveManageDetailVO detailVO = convertToDetailVO(liveManage);

    // 查询明细表信息
    List<LiveManageItem> items =
        liveManageItemService.lambdaQuery().eq(LiveManageItem::getLiveManageId, id).list();

    // 转换明细信息
    List<LiveManageDetailItemVO> itemVOs =
        items.stream().map(this::convertToDetailItemVO).collect(Collectors.toList());

    detailVO.setItems(itemVOs);

    // 设置申请人姓名
    if (Objects.nonNull(detailVO.getApplyUserId())) {
      StaffBrief staff = staffService.getStaffBrief(detailVO.getApplyUserId()).orElse(null);
      if (Objects.nonNull(staff)) {
        detailVO.setApplyUserName(staff.getNickname());
      }
    }

    return SingleResponse.of(detailVO);
  }

  @Override
  public PageResponse<DouStoreGoodDetailVO> douStoreGoodQuery(DouStoreGoodQuery query) {
    log.info("查询橱窗商品，查询条件：{}", query);
    OauthToken oauthToken = oauthTokenService.getById(liveManageConfig.getDouyinOauthTokenId());
    if (oauthToken == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "抖音授权未配置");
    }
    Client client = douyinClientFactory.createClient(liveManageConfig.getDouyinOpenAppId());

    // 先获取第一页数据，确定总页数
    KolStoreListResults firstPageResult = allianceKolStoreList(oauthToken, client, 1, 20);
    if (firstPageResult == null
        || firstPageResult.getResults() == null
        || firstPageResult.getResults().isEmpty()) {
      return PageResponse.of(0, 1);
    }

    int totalCount = firstPageResult.getTotal().intValue();
    int pageSize = 20;
    int totalPages = (totalCount + pageSize - 1) / pageSize;

    log.info("橱窗商品总数：{}，总页数：{}", totalCount, totalPages);

    // 并行查询所有页面
    List<StoreGoodDetail> storeGoodDetails =
        parallelQueryAllPages(oauthToken, client, totalPages, pageSize);

    // 使用并行流进行数据转换
    List<DouStoreGoodDetailVO> resultList =
        storeGoodDetails.parallelStream()
            .map(this::convertToDouStoreGoodDetailVO)
            .collect(Collectors.toList());

    PageResponse<DouStoreGoodDetailVO> pageResponse = PageResponse.of(resultList.size(), 1);
    pageResponse.setData(resultList);
    return pageResponse;
  }

  /** 并行查询所有页面的橱窗商品数据 */
  private List<StoreGoodDetail> parallelQueryAllPages(
      OauthToken oauthToken, Client client, int totalPages, int pageSize) {
    // 创建线程池，限制并发数避免对第三方API造成过大压力
    ThreadPoolTaskExecutor executor = ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL);

    try {
      // 创建所有页面的查询任务
      List<CompletableFuture<List<StoreGoodDetail>>> futures = new ArrayList<>();

      for (int page = 1; page <= totalPages; page++) {
        final int currentPage = page;
        CompletableFuture<List<StoreGoodDetail>> future =
            CompletableFuture.supplyAsync(
                () -> {
                  try {
                    KolStoreListResults result =
                        allianceKolStoreList(oauthToken, client, currentPage, pageSize);
                    if (result != null && result.getResults() != null) {
                      log.debug("[橱窗商品]查询第{}页成功，获取到{}条数据", currentPage, result.getResults().size());
                      return result.getResults();
                    }
                    return new ArrayList<>();
                  } catch (Exception e) {
                    log.error("[橱窗商品]查询第{}页失败：{}", currentPage, e.getMessage(), e);
                    return new ArrayList<>();
                  }
                },
                executor);
        futures.add(future);
      }

      // 等待所有查询完成并合并结果
      List<StoreGoodDetail> allResults = new ArrayList<>();
      for (CompletableFuture<List<StoreGoodDetail>> future : futures) {
        try {
          List<StoreGoodDetail> pageResults = future.get(30, TimeUnit.SECONDS);
          if (pageResults != null) {
            allResults.addAll(pageResults);
          }
        } catch (Exception e) {
          log.error("[橱窗商品]获取页面数据失败：{}", e.getMessage(), e);
        }
      }

      log.info("[橱窗商品]并行查询完成，总共获取到{}条橱窗商品数据", allResults.size());
      return allResults;

    } catch (Exception e) {
      log.error("[橱窗商品]并行查询所有页面失败：{}", e.getMessage(), e);
      return new ArrayList<>();
    }
  }

  /** 转换StoreGoodDetail为DouStoreGoodDetailVO */
  private DouStoreGoodDetailVO convertToDouStoreGoodDetailVO(StoreGoodDetail v) {
    DouStoreGoodDetailVO douStoreGoodDetailVO = new DouStoreGoodDetailVO();
    douStoreGoodDetailVO.setProductId(v.getProductId().toString());
    douStoreGoodDetailVO.setPromotionId(v.getPromotionId());
    douStoreGoodDetailVO.setTitle(v.getTitle());
    douStoreGoodDetailVO.setCover(v.getCover());
    douStoreGoodDetailVO.setPromotionType(v.getPromotionType());
    douStoreGoodDetailVO.setPrice(v.getPrice());
    douStoreGoodDetailVO.setCosType(v.getCosType());
    douStoreGoodDetailVO.setCosRatio(v.getCosRatio());
    douStoreGoodDetailVO.setColonelActivityId(v.getColonelActivityId());
    douStoreGoodDetailVO.setHideStatus(v.getHideStatus());
    return douStoreGoodDetailVO;
  }

  @SneakyThrows
  private KolStoreListResults allianceKolStoreList(
      OauthToken oauthToken, Client client, int page, int pageSize) {
    CommonRequest request = new CommonRequest();
    request.setHost("open.douyinec.com");
    request.setPath("/alliance/kol/store/list/");
    request.setMethod("POST");
    HashMap<String, String> header = new HashMap<>();
    header.put("access-token", oauthToken.getAccessToken());
    header.put("content-type", "application/json");
    request.setHeader(header);
    HashMap<String, String> query1 = new HashMap<>();
    query1.put("open_id", oauthToken.getSubjectId());
    request.setQuery(query1);
    HashMap<Object, Object> body = new HashMap<>();
    body.put("page", page);
    body.put("page_size", pageSize);
    request.setBody(body);
    Map<String, ?> response = client.commonOpenAPI(request);
    BaseResponse<KolStoreListResults> responseModel =
        JsonUtil.mapToBean(response, new TypeReference<BaseResponse<KolStoreListResults>>() {});
    return responseModel.getData();
  }

  @Override
  public Response selectDouStoreGood(DouStoreGoodSelectCmd cmd) {
    log.info("关联橱窗商品，命令：{}", cmd);
    Long liveManageItemId = cmd.getLiveManageItemId();
    LiveManageItem liveManageItem = liveManageItemService.getById(liveManageItemId);
    if (liveManageItem == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "直播管理商品不存在");
    }
    if (cmd.getGood() == null) {
      liveManageItem.setDouProductId("");
      liveManageItem.setDouProductName("");
      liveManageItem.setDouCosRatio(BigDecimal.ZERO);
      liveManageItem.setDouProductData(null);
      liveManageItemService.updateById(liveManageItem);
      
      operateLogDomainService.addOperatorLog(
              0L, OperateLogTarget.LIVE_MANAGE_COMMISSION, liveManageItemId, "取消橱窗商品关联", null);
      
      log.info("[橱窗商品]取消关联成功，直播管理商品ID：{}", liveManageItemId);
      return Response.buildSuccess();
    }
    DouStoreGoodDetailVO good = cmd.getGood();

    // 更新直播管理商品明细表
    liveManageItem.setDouProductId(good.getProductId());
    liveManageItem.setDouProductName(good.getTitle());
    BigDecimal newCosRatio =
        new BigDecimal(String.valueOf(good.getCosRatio())).setScale(2, RoundingMode.HALF_UP);
    liveManageItem.setDouCosRatio(newCosRatio);
    liveManageItem.setDouProductData(JsonUtil.toJson(good));
    liveManageItemService.updateById(liveManageItem);

    String msg = "关联橱窗商品:" + good.getTitle() + "，初始化佣金率为“" + newCosRatio.toString() + "%”";
    operateLogDomainService.addOperatorLog(
        0L, OperateLogTarget.LIVE_MANAGE_COMMISSION, liveManageItemId, msg, good);

    log.info("[橱窗商品]关联成功，直播管理商品ID：{}，抖音商品ID：{}，{}", liveManageItemId, good.getProductId(), msg);
    return Response.buildSuccess();
  }

  @Override
  @SneakyThrows
  public PageResponse<DouLiveRoomVO> douLiveRoomQuery(DouLiveRoomQuery query) {
    log.info("查询直播间，查询条件：{}", query);
    QianchuanTodayLiveRoomGetV10Api qianchuanTodayLiveRoomGetV10Api =
        new QianchuanTodayLiveRoomGetV10Api();
    ApiClient apiClient = new ApiClient();
    apiClient.setDebugging(true);
    OauthToken oauthToken = oauthTokenService.getById(liveManageConfig.getQianchuanOauthTokenId());
    apiClient.addDefaultHeader("Access-Token", oauthToken.getAccessToken());
    qianchuanTodayLiveRoomGetV10Api.setApiClient(apiClient);
    QianchuanTodayLiveRoomGetV10Response qianchuanTodayLiveRoomGetV10Response =
        qianchuanTodayLiveRoomGetV10Api.openApiV10QianchuanTodayLiveRoomGetGet(
            query.getAdvertiserId(),
            query.getAwemeId(),
            DateUtil.formatDate(query.getDate()),
            Arrays.asList(
                "stat_cost",
                "cpm_platform",
                "click_cnt",
                "ctr",
                "total_live_pay_order_gpm",
                "luban_live_pay_order_gpm",
                "cpc_platform",
                "ecp_convert_rate",
                "ecp_convert_cnt",
                "ecp_cpa_platform",
                "live_pay_order_gmv_alias",
                "luban_live_pay_order_gmv",
                "ad_live_prepay_and_pay_order_gmv_roi",
                "live_create_order_count_alias",
                "luban_live_order_count",
                "ad_live_create_order_rate",
                "live_pay_order_count_alias",
                "luban_live_pay_order_count",
                "ad_live_pay_order_rate",
                "live_pay_order_gmv_avg",
                "ad_live_pay_order_gmv_avg",
                "luban_live_prepay_order_count",
                "luban_live_prepay_order_gmv",
                "live_prepay_order_count_alias",
                "live_prepay_order_gmv_alias",
                "live_order_pay_coupon_amount",
                "total_live_watch_cnt",
                "total_live_follow_cnt",
                "live_watch_one_minute_count",
                "total_live_fans_club_join_cnt",
                "live_click_cart_count_alias",
                "live_click_product_count_alias",
                "total_live_comment_cnt",
                "total_live_share_cnt",
                "total_live_gift_cnt",
                "total_live_gift_amount"),
            null,
            null,
            query.getPageIndex(),
            query.getPageSize());
    if (qianchuanTodayLiveRoomGetV10Response == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "请求千川接口异常");
    }
    QianchuanTodayLiveRoomGetV10ResponseData data = qianchuanTodayLiveRoomGetV10Response.getData();
    Assert.notNull(data, "请求千川接口异常，数据返回空");
    Assert.notNull(data.getRoomList(), "请求千川接口异常，数据返回空");
    QianchuanTodayLiveRoomGetV10ResponseDataPageInfo pageInfo = data.getPageInfo();
    return PageResponse.of(
        data.getRoomList().stream()
            .map(
                v -> {
                  return convertToDouLiveRoomVO(v);
                })
            .collect(Collectors.toList()),
        pageInfo.getTotalNum(),
        pageInfo.getPage(),
        pageInfo.getPageSize());
  }

  private DouLiveRoomVO convertToDouLiveRoomVO(
      QianchuanTodayLiveRoomGetV10ResponseDataRoomListInner v) {
    DouLiveRoomVO douLiveRoomVO = new DouLiveRoomVO();
    douLiveRoomVO.setRoomId(String.valueOf(v.getRoomId()));
    if (v.getRoomStatus() != null) {
      douLiveRoomVO.setRoomStatus(v.getRoomStatus().getValue());
    }
    douLiveRoomVO.setRoomTitle(v.getRoomTitle());
    douLiveRoomVO.setStartTime(DateUtil.parseToTimestampCompatibility(v.getStartTime()));
    douLiveRoomVO.setEndTime(DateUtil.parseToTimestampCompatibility(v.getEndTime()));
    douLiveRoomVO.setAwemeId(v.getAwemeId());
    douLiveRoomVO.setAwemeName(v.getAwemeName());
    if (v.getRoomCover() != null && !v.getRoomCover().isEmpty()) {
      douLiveRoomVO.setRoomCover(v.getRoomCover().get(0));
    }
    douLiveRoomVO.setAwemeAvatar(v.getAwemeAvatar());
    douLiveRoomVO.setRoomDelivery(v.getRoomDelivery());
    douLiveRoomVO.setStatCost(v.getStatCost());
    douLiveRoomVO.setLivePayOrderGmvAlias(v.getLivePayOrderGmvAlias());
    douLiveRoomVO.setData(BeanUtil.beanToMap(v));
    return douLiveRoomVO;
  }

  @Override
  public Response selectDouLiveRoom(DouLiveRoomSelectCmd cmd) {
    log.info("关联直播间，命令：{}", cmd);
    Long liveManageItemId = cmd.getLiveManageItemId();
    LiveManageItem liveManageItem = liveManageItemService.getById(liveManageItemId);
    if (liveManageItem == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "直播管理商品不存在");
    }
    if (cmd.getRoom() == null) {
      liveManageItem.setDouLiveRoomStartTime(0L);
      liveManageItem.setDouLiveRoomEndTime(0L);
      liveManageItem.setDouAdvertiserId(0L);
      liveManageItem.setDouAwemeId(0L);
      liveManageItem.setDouLiveRoomId("");
      liveManageItem.setDouLiveRoomName("");
      liveManageItem.setDouLiveRoomFlowRatio(BigDecimal.ZERO);
      liveManageItem.setDouLiveRoomData(null);
      liveManageItemService.updateById(liveManageItem);
      operateLogDomainService.addOperatorLog(
          0L, OperateLogTarget.LIVE_MANAGE_FLOW_RADIO, liveManageItemId, "取消直播间关联", null);
      log.info("[直播间]取消关联成功，直播管理商品ID：{}", liveManageItemId);
      return Response.buildSuccess();
    }
    DouLiveRoomVO room = cmd.getRoom();
    // 计算投流比例：stat_cost / live_pay_order_gmv_alias
    BigDecimal roomFlowRatio = calculateRoomFlowRatio(room);
    liveManageItem.setDouLiveRoomStartTime(room.getStartTime());
    liveManageItem.setDouLiveRoomEndTime(room.getEndTime());
    liveManageItem.setDouAdvertiserId(room.getAwemeId());
    liveManageItem.setDouAwemeId(room.getAwemeId());
    liveManageItem.setDouLiveRoomId(String.valueOf(room.getRoomId()));
    liveManageItem.setDouLiveRoomName(room.getRoomTitle());
    liveManageItem.setDouLiveRoomFlowRatio(roomFlowRatio);
    liveManageItem.setDouLiveRoomData(JsonUtil.toJson(room.getData()));

    liveManageItemService.updateById(liveManageItem);

    // 记录操作日志
    String msg = "关联直播间，投流比例为“" + roomFlowRatio.setScale(2, RoundingMode.HALF_UP).toString() + "%”";
    operateLogDomainService.addOperatorLog(
        0L, OperateLogTarget.LIVE_MANAGE_FLOW_RADIO, liveManageItemId, msg, room);

    log.info("[直播间]关联成功，直播管理商品ID：{}，直播间ID：{}，{}", liveManageItemId, room.getRoomId(), msg);
    return Response.buildSuccess();
  }

  /** 计算直播间投流比例：stat_cost / live_pay_order_gmv_alias */
  private BigDecimal calculateRoomFlowRatio(DouLiveRoomVO room) {
    if (room.getStatCost() == null || room.getLivePayOrderGmvAlias() == null) {
      return BigDecimal.ZERO;
    }

    if (room.getLivePayOrderGmvAlias() == 0) {
      return BigDecimal.ZERO;
    }

    // 计算比例并转换为整数（保留2位小数后乘以100）
    return BigDecimal.valueOf(room.getStatCost())
        .divide(BigDecimal.valueOf(room.getLivePayOrderGmvAlias()), 6, RoundingMode.HALF_UP)
        .multiply(BigDecimal.valueOf(100))
        .setScale(6, RoundingMode.HALF_UP);
  }

  @Override
  @SneakyThrows
  public PageResponse<DouAccountVO> douAccountQuery(DouAccountQuery query) {
    QianchuanAwemeAuthorizedGetV10Api qianchuanAwemeAuthorizedGetV10Api =
        new QianchuanAwemeAuthorizedGetV10Api();
    ApiClient apiClient = new ApiClient();
    OauthToken oauthToken = oauthTokenService.getById(liveManageConfig.getQianchuanOauthTokenId());
    apiClient.addDefaultHeader("Access-Token", oauthToken.getAccessToken());
    qianchuanAwemeAuthorizedGetV10Api.setApiClient(apiClient);
    QianchuanAwemeAuthorizedGetV10Response response =
        qianchuanAwemeAuthorizedGetV10Api.openApiV10QianchuanAwemeAuthorizedGetGet(
            query.getAdvertiserId(), null, query.getPageIndex(), query.getPageSize());
    log.info("请求千川接口返回：{}", response);
    QianchuanAwemeAuthorizedGetV10ResponseData data = response.getData();
    QianchuanAwemeAuthorizedGetV10ResponseDataPageInfo pageInfo = data.getPageInfo();
    return PageResponse.of(
        data.getAwemeIdList().stream()
            .map(
                v -> {
                  DouAccountVO douAccountVO = new DouAccountVO();
                  douAccountVO.setAwemeAvatar(v.getAwemeAvatar());
                  douAccountVO.setAwemeHasLivePermission(v.getAwemeHasLivePermission());
                  douAccountVO.setAwemeHasPublishPermission(v.getAwemeHasPublishPermission());
                  douAccountVO.setAwemeHasUniProm(v.getAwemeHasUniProm());
                  douAccountVO.setAwemeHasVideoPermission(v.getAwemeHasVideoPermission());
                  douAccountVO.setAwemeId(v.getAwemeId());
                  douAccountVO.setAwemeName(v.getAwemeName());
                  douAccountVO.setAwemeShowId(v.getAwemeShowId());
                  douAccountVO.setAwemeStatus(v.getAwemeAvatar());
                  douAccountVO.setBindType(
                      v.getBindType().stream()
                          .map(QianchuanAwemeAuthorizedGetV10DataAwemeIdListBindType::getValue)
                          .collect(Collectors.toList()));

                  return douAccountVO;
                })
            .collect(Collectors.toList()),
        pageInfo.getTotalNumber().intValue(),
        pageInfo.getPage().intValue(),
        pageInfo.getPageSize().intValue());
  }

  @Override
  @SneakyThrows
  public PageResponse<DouAdvertiserVO> douAdvertiserQuery(DouAdvertiserQuery query) {
    CustomerCenterAdvertiserListV2Api api = new CustomerCenterAdvertiserListV2Api();
    CustomerCenterAdvertiserListV2AccountSource accountSource =
        CustomerCenterAdvertiserListV2AccountSource.QIANCHUAN;
    Long ccAccountId = liveManageConfig.getQianchuanAccountId();
    CustomerCenterAdvertiserListV2Filtering filtering =
        new CustomerCenterAdvertiserListV2Filtering();
    Long page = 1L;
    Long pageSize = 100L;
    ApiClient apiClient = new ApiClient();
    OauthToken oauthToken = oauthTokenService.getById(liveManageConfig.getQianchuanOauthTokenId());
    apiClient.addDefaultHeader("Access-Token", oauthToken.getAccessToken());
    api.setApiClient(apiClient);
    apiClient.setDebugging(true);
    CustomerCenterAdvertiserListV2Response response =
        api.openApi2CustomerCenterAdvertiserListGet(
            accountSource, ccAccountId, filtering, page, pageSize);
    if (response == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.GATEWAY_ERROR, "请求千川接口异常");
    }
    CustomerCenterAdvertiserListV2ResponseDataPageInfo pageInfo = response.getData().getPageInfo();
    return PageResponse.of(
        response.getData().getList().stream()
            .map(
                v -> {
                  DouAdvertiserVO douAdvertiserVO = new DouAdvertiserVO();
                  douAdvertiserVO.setAdvertiserId(v.getAdvertiserId());
                  douAdvertiserVO.setAdvertiserType(v.getAdvertiserType());
                  douAdvertiserVO.setAdvertiserName(v.getAdvertiserName());

                  return douAdvertiserVO;
                })
            .collect(Collectors.toList()),
        pageInfo.getTotalNumber().intValue(),
        pageInfo.getPage().intValue(),
        pageInfo.getPageSize().intValue());
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response processOaLiveContractCallback(OaLiveContractCallbackRequest request) {
    String oaProcessNo = request.getId();
    Logs.info(
        log,
        "[OA直播合同回调]开始处理",
        Logs.var("oaProcessNo", oaProcessNo),
        Logs.var("status", request.getStatus()),
        Logs.var("company", request.getCompany()));

    try {
      Map<String, Map<String, String>> constDict = liveManageConfig.getConstDict();
      String contractType2 = request.getContractType2();
      String contractType2Name = constDict.get("contractType2").get(contractType2);
      if (!"推广服务合同".equals(contractType2Name)) {
        Logs.info(
            log,
            "[OA直播合同回调]非推广服务合同，跳过处理",
            Logs.var("oaProcessNo", oaProcessNo),
            Logs.var("contractType2", contractType2));
        return Response.buildSuccess();
      }

      // 只处理审核通过的回调
      if (!oaLiveContractParser.isApproved(request.getStatus())) {
        Logs.info(
            log,
            "[OA直播合同回调]非审核通过状态，跳过处理",
            Logs.var("oaProcessNo", oaProcessNo),
            Logs.var("status", request.getStatus()));
        return Response.buildSuccess();
      }

      // 检查单号是否已存在
      if (isOaProcessNoExists(oaProcessNo)) {
        Logs.info(
            log,
            "[OA直播合同回调]OA流程编号已存在，跳过处理",
            Logs.var("oaProcessNo", oaProcessNo),
            Logs.var("status", request.getStatus()));
        return Response.buildSuccess();
      }

      // 解析回调数据
      ParsedLiveContractData parsedData = oaLiveContractParser.parseCallbackData(request);

      // 验证解析结果
      if (!parsedData.isValid()) {
        Logs.error(
            log,
            "[OA直播合同回调]解析数据无效，跳过处理",
            Logs.var("oaProcessNo", oaProcessNo),
            Logs.var("parsedData", parsedData));
        return Response.buildSuccess();
      }

      // 生成直播编号
      String liveNo = generateNextLiveNo();
      LiveManage liveManage = parsedData.getLiveManage();
      liveManage.setLiveNo(liveNo);

      // 查询申请人用户信息
      StaffBrief userInfo = staffService.getStaffByOaId(liveManage.getApplyOaId());
      if (userInfo != null) {
        liveManage.setApplyUserId(userInfo.getUserId());
      }

      // 保存数据
      saveLiveContractData(parsedData);

      Logs.info(
          log,
          "[OA直播合同回调]处理完成",
          Logs.var("liveManageId", parsedData.getLiveManage().getId()),
          Logs.var("liveNo", liveNo),
          Logs.var("itemCount", parsedData.getItemCount()));

      return Response.buildSuccess();

    } catch (Exception e) {
      log.error("[OA直播合同回调]处理失败: {}", JSON.toJSONString(request), e);
      throw e;
    }
  }

  @Override
  public SingleResponse<Map<String, Map<String, String>>> constDict() {
    return SingleResponse.of(liveManageConfig.getConstDict());
  }

  /** 检查OA流程编号是否已存在 */
  private boolean isOaProcessNoExists(String oaProcessNo) {
    if (StrUtil.isBlank(oaProcessNo)) {
      return false;
    }

    QueryWrapper<LiveManage> queryWrapper = new QueryWrapper<>();
    queryWrapper.eq("oa_process_no", oaProcessNo);
    return liveManageService.count(queryWrapper) > 0;
  }

  /**
   * 保存直播合同数据
   *
   * @param parsedData 解析后的数据
   */
  private void saveLiveContractData(ParsedLiveContractData parsedData) {
    // 保存主表数据
    LiveManage liveManage = parsedData.getLiveManage();
    liveManageService.save(liveManage);

    // 保存明细表数据
    List<LiveManageItem> items = parsedData.getItems();
    if (CollUtil.isNotEmpty(items)) {
      // 设置主表ID
      items.forEach(item -> item.setLiveManageId(liveManage.getId()));
      liveManageItemService.saveBatch(items);
    }
  }

  /**
   * 生成下一个直播编号
   *
   * @return 直播编号
   */
  public String generateNextLiveNo() {
    String dateStr = DateUtil.formatNow("yyyyMMdd");

    // 查询今天已存在的最大编号
    QueryWrapper<LiveManage> queryWrapper = new QueryWrapper<>();
    queryWrapper.likeRight("live_no", "ZB" + dateStr).orderByDesc("live_no").last("limit 1");

    LiveManage lastLiveManage = liveManageService.getOne(queryWrapper);

    int nextSequence = 1;
    if (lastLiveManage != null && StrUtil.isNotBlank(lastLiveManage.getLiveNo())) {
      String lastLiveNo = lastLiveManage.getLiveNo();
      try {
        // 提取序号部分
        String sequenceStr = lastLiveNo.substring(lastLiveNo.length() - 4);
        nextSequence = Integer.parseInt(sequenceStr) + 1;
      } catch (Exception e) {
        log.warn("解析现有直播编号失败: {}, 使用默认序号", lastLiveNo);
      }
    }

    // 生成新编号
    return String.format("ZB%s%04d", dateStr, nextSequence);
  }

  /** 构建查询条件 */
  private QueryWrapper<LiveManage> buildQueryWrapper(LiveManageListQuery query) {
    QueryWrapper<LiveManage> queryWrapper = new QueryWrapper<>();

    // 直播编号模糊查询
    if (StrUtil.isNotBlank(query.getLiveNo())) {
      queryWrapper.eq("live_no", query.getLiveNo());
    }

    // 直播日期范围查询
    if (Objects.nonNull(query.getLiveDateStart()) && Objects.nonNull(query.getLiveDateEnd())) {
      // 筛选，live_start_time <= 直播日期开始时间 <= live_end_time OR live_start_time <= 直播日期结束时间 <=
      // live_end_time
      QueryChainWrapper<LiveManageItem> liveManageItemQueryWrapper = liveManageItemService.query();
      liveManageItemQueryWrapper.select("id", "live_manage_id");
      liveManageItemQueryWrapper
          .le("live_start_time", query.getLiveDateStart())
          .ge("live_end_time", query.getLiveDateStart())
          .or()
          .le("live_start_time", query.getLiveDateEnd())
          .ge("live_end_time", query.getLiveDateEnd());
      List<Long> liveManageIds =
          liveManageItemQueryWrapper.list().stream()
              .map(LiveManageItem::getLiveManageId)
              .distinct()
              .collect(Collectors.toList());
      if (liveManageIds.isEmpty()) {
        return null;
      }
      queryWrapper.in("id", liveManageIds);
    }

    // 合同类型查询
    if (StrUtil.isNotBlank(query.getContractType1())) {
      queryWrapper.eq("contract_type1", query.getContractType1());
    }
    if (StrUtil.isNotBlank(query.getContractType2())) {
      queryWrapper.eq("contract_type2", query.getContractType2());
    }
    if (Objects.nonNull(query.getContractType3())) {
      queryWrapper.eq("contract_type3", query.getContractType3());
    }

    // 对方名称模糊查询
    if (StrUtil.isNotBlank(query.getOtherName())) {
      queryWrapper.like("company_name", query.getOtherName());
    }

    // 申请时间范围查询
    if (Objects.nonNull(query.getApplicationTimeStart())) {
      queryWrapper.ge("application_time", query.getApplicationTimeStart());
    }
    if (Objects.nonNull(query.getApplicationTimeEnd())) {
      queryWrapper.le("application_time", query.getApplicationTimeEnd());
    }

    // 审核通过时间范围查询
    if (Objects.nonNull(query.getPassTimeStart())) {
      queryWrapper.ge("pass_time", query.getPassTimeStart());
    }
    if (Objects.nonNull(query.getPassTimeEnd())) {
      queryWrapper.le("pass_time", query.getPassTimeEnd());
    }

    // 申请人ID查询
    if (Objects.nonNull(query.getApplicantUid())) {
      queryWrapper.eq("apply_user_id", query.getApplicantUid());
    }

    // 按创建时间倒序
    queryWrapper.orderByDesc("created_at");

    return queryWrapper;
  }

  /** 转换为列表项VO */
  private LiveManageListItemVO convertToListItem(LiveManage liveManage) {
    LiveManageListItemVO vo = new LiveManageListItemVO();
    vo.setId(liveManage.getId());
    vo.setLiveNo(liveManage.getLiveNo());
    vo.setOtherName(liveManage.getCompanyName());
    vo.setCreateTime(liveManage.getCreatedAt());
    vo.setApplicationTime(liveManage.getApplicationTime());
    vo.setPassTime(liveManage.getPassTime());
    vo.setApplyUserId(liveManage.getApplyUserId());
    vo.setContractType1(liveManage.getContractType1());
    vo.setContractType2(liveManage.getContractType2());
    vo.setContractType3(liveManage.getContractType3());
    return vo;
  }

  /** 转换为详情VO */
  private LiveManageDetailVO convertToDetailVO(LiveManage liveManage) {
    LiveManageDetailVO vo = new LiveManageDetailVO();
    vo.setLiveNo(liveManage.getLiveNo());
    vo.setOaProcessNo(liveManage.getOaProcessNo());
    vo.setContractType1(liveManage.getContractType1());
    vo.setContractType2(liveManage.getContractType2());
    vo.setContractType3(liveManage.getContractType3());
    vo.setOtherName(liveManage.getCompanyName());
    vo.setApplicationTime(liveManage.getApplicationTime());
    vo.setPassTime(liveManage.getPassTime());
    vo.setApplyUserId(liveManage.getApplyUserId());

    return vo;
  }

  /** 转换为详情明细VO */
  private LiveManageDetailItemVO convertToDetailItemVO(LiveManageItem item) {
    LiveManageDetailItemVO vo = new LiveManageDetailItemVO();
    vo.setId(item.getId());
    vo.setShopName(item.getShopName());
    vo.setShopLink(item.getShopLink());
    vo.setPSysItemNo(item.getItemNo());
    vo.setItemName(item.getItemName());
    if (item.getOnlineCommission() != null) {
      vo.setCosRadioOnline(
          new BigDecimal(item.getOnlineCommission())
              .multiply(new BigDecimal(100))
              .setScale(2, RoundingMode.HALF_UP)
              .toString());
    }
    if (item.getOfflineCommission() != null) {
      vo.setCosRadioOffline(
          new BigDecimal(item.getOfflineCommission())
              .multiply(new BigDecimal(100))
              .setScale(2, RoundingMode.HALF_UP)
              .toString());
    }
    vo.setLivePrice(item.getLivePrice());
    vo.setFlowFeeAgreement(item.getFlowFeeAgreement());
    vo.setLiveStartTime(item.getLiveStartTime());
    vo.setLiveEndTime(item.getLiveEndTime());
    vo.setDouCosRatio(
        item.getDouCosRatio() != null
            ? item.getDouCosRatio().setScale(2, RoundingMode.HALF_UP)
            : null);
    vo.setDouLiveRoomFlowRatio(
        item.getDouLiveRoomFlowRatio() != null
            ? item.getDouLiveRoomFlowRatio().setScale(2, RoundingMode.HALF_UP)
            : null);
    vo.setDouProductId(item.getDouProductId());
    vo.setDouProductName(item.getDouProductName());
    vo.setDouLiveRoomId(item.getDouLiveRoomId());
    vo.setDouLiveRoomName(item.getDouLiveRoomName());

    return vo;
  }

  /** 批量设置员工姓名 */
  private void setStaffNames(List<LiveManageListItemVO> list) {
    if (CollUtil.isEmpty(list)) {
      return;
    }

    // 收集所有申请人ID
    Set<Long> userIds =
        list.stream()
            .map(LiveManageListItemVO::getApplyUserId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());

    if (CollUtil.isEmpty(userIds)) {
      return;
    }

    // 批量查询员工信息
    List<Long> userIdList = new ArrayList<>(userIds);
    List<StaffBrief> staffBriefs = staffService.getStaffBriefList(userIdList);

    // 转换为Map便于查找
    Map<Long, StaffBrief> staffMap =
        staffBriefs.stream().collect(Collectors.toMap(StaffBrief::getUserId, Function.identity()));

    // 设置员工姓名
    list.forEach(
        item -> {
          if (Objects.nonNull(item.getApplyUserId())) {
            StaffBrief staff = staffMap.get(item.getApplyUserId());
            if (Objects.nonNull(staff)) {
              item.setApplyUserName(staff.getNickname());
            }
          }
        });
  }

  @Override
  public void batchSyncDouyinProductData(List<LiveManageItem> items) {
    if (CollUtil.isEmpty(items)) {
      log.debug("[批量同步抖音商品]没有需要同步的商品");
      return;
    }

    try {
      // 获取抖音授权token
      OauthToken oauthToken = oauthTokenService.getById(liveManageConfig.getDouyinOauthTokenId());
      if (oauthToken == null) {
        log.warn("[批量同步抖音商品]抖音授权未配置，跳过商品数据同步");
        return;
      }

      // 创建抖音客户端
      Client client = douyinClientFactory.createClient(liveManageConfig.getDouyinOpenAppId());

      // 调用抖音API获取全部橱窗商品（一次性获取所有数据）
      KolStoreListResults result = allianceKolStoreList(oauthToken, client, 1, 1000);
      if (result == null || result.getResults() == null || result.getResults().isEmpty()) {
        log.warn("[批量同步抖音商品]未获取到商品数据");
        return;
      }

      // 构建商品ID到商品详情的映射
      Map<String, StoreGoodDetail> productMap =
          result.getResults().stream()
              .collect(
                  Collectors.toMap(
                      good -> good.getProductId().toString(),
                      Function.identity(),
                      (existing, replacement) -> existing));

      int successCount = 0;
      int failCount = 0;

      // 遍历需要同步的商品
      for (LiveManageItem item : items) {
        if (item.getDouProductId() == null) {
          continue;
        }

        try {
          StoreGoodDetail targetGood = productMap.get(item.getDouProductId());
          if (targetGood == null) {
            log.warn(
                "[批量同步抖音商品]未找到对应的商品数据，商品ID：{}，抖音商品ID：{}", item.getId(), item.getDouProductId());
            failCount++;
            continue;
          }

          // 更新商品信息
          boolean needUpdate = false;
          String logMessage = "";

          // 检查佣金率是否有变化
          if (targetGood.getCosRatio() != null) {
            BigDecimal newCosRatio = BigDecimal.valueOf(targetGood.getCosRatio());
            if (!newCosRatio.equals(item.getDouCosRatio())) {
              String oldCosRatio =
                  item.getDouCosRatio() != null ? item.getDouCosRatio().toString() : "null";
              item.setDouCosRatio(newCosRatio);
              needUpdate = true;
              logMessage += "佣金率从“" + oldCosRatio + "” 改为 “" + newCosRatio + "”; ";
            }
          }

          // 检查商品名称是否有变化
          if (targetGood.getTitle() != null
              && !targetGood.getTitle().equals(item.getDouProductName())) {
            String oldName = item.getDouProductName();
            item.setDouProductName(targetGood.getTitle());
            needUpdate = true;
            logMessage += "商品名称从“" + oldName + "” 改为 “" + targetGood.getTitle() + "”; ";
          }

          // 更新商品数据JSON
          DouStoreGoodDetailVO goodVO = convertToDouStoreGoodDetailVO(targetGood);
          item.setDouProductData(JsonUtil.toJson(goodVO));

          if (needUpdate) {
            liveManageItemService.updateById(item);

            // 记录操作日志
            operateLogDomainService.addOperatorLog(
                0L, OperateLogTarget.LIVE_MANAGE_COMMISSION, item.getId(), logMessage, goodVO);

            successCount++;
            log.debug("[批量同步抖音商品]同步成功，商品ID：{}，{}", item.getId(), logMessage);
          } else {
            log.debug("[批量同步抖音商品]数据无变化，商品ID：{}", item.getId());
          }

        } catch (Exception e) {
          log.error("[批量同步抖音商品]同步失败，商品ID：{}，错误：{}", item.getId(), e.getMessage(), e);
          failCount++;
        }
      }

      log.info("[批量同步抖音商品]同步完成，成功：{}，失败：{}", successCount, failCount);

    } catch (Exception e) {
      log.error("[批量同步抖音商品]批量同步失败，错误：{}", e.getMessage(), e);
    }
  }

  @Override
  public void batchSyncDouyinLiveRoomData(List<LiveManageItem> items) {
    if (CollUtil.isEmpty(items)) {
      log.debug("[批量同步直播间]没有需要同步的商品");
      return;
    }

    try {
      // 获取千川授权token
      OauthToken oauthToken =
          oauthTokenService.getById(liveManageConfig.getQianchuanOauthTokenId());
      if (oauthToken == null) {
        log.warn("[批量同步直播间]千川授权未配置，跳过直播间数据同步");
        return;
      }

      // 创建千川客户端
      ApiClient apiClient = new ApiClient();
      apiClient.addDefaultHeader("Access-Token", oauthToken.getAccessToken());

      // 按广告主ID分组
      Map<Long, Map<Long, List<LiveManageItem>>> advertiserGroups =
          items.stream()
              .filter(item -> item.getDouAdvertiserId() != null)
              .collect(
                  Collectors.groupingBy(
                      LiveManageItem::getDouAdvertiserId,
                      Collectors.groupingBy(LiveManageItem::getDouAwemeId)));

      int successCount = 0;
      int failCount = 0;

      // 按广告主ID和日期进行同步
      for (Map.Entry<Long, Map<Long, List<LiveManageItem>>> entry : advertiserGroups.entrySet()) {
        Long advertiserId = entry.getKey();
        Map<Long, List<LiveManageItem>> advertiserItems = entry.getValue();
        for (Map.Entry<Long, List<LiveManageItem>> itemEntry : advertiserItems.entrySet()) {
          Long awemeId = itemEntry.getKey();
          List<LiveManageItem> awemeItems = itemEntry.getValue();
          // 获取日期列表
          Set<String> dateList = new HashSet<String>();
          awemeItems.forEach(
              item -> {
                if (item.getDouLiveRoomStartTime() != null) {
                  dateList.add(DateUtil.formatDate(item.getDouLiveRoomStartTime()));
                }
                if (item.getDouLiveRoomEndTime() != null) {
                  dateList.add(DateUtil.formatDate(item.getDouLiveRoomEndTime()));
                }
              });

          // 按日期进行同步
          for (String date : dateList) {
            try {
              // 调用千川API获取直播间数据
              QianchuanTodayLiveRoomGetV10Api api = new QianchuanTodayLiveRoomGetV10Api();
              api.setApiClient(apiClient);

              QianchuanTodayLiveRoomGetV10Response response =
                  api.openApiV10QianchuanTodayLiveRoomGetGet(
                      advertiserId, // 广告主ID
                      awemeId, // 抖音账号ID
                      date, // 日期
                      Arrays.asList(
                          "stat_cost",
                          "cpm_platform",
                          "click_cnt",
                          "ctr",
                          "total_live_pay_order_gpm",
                          "luban_live_pay_order_gpm",
                          "cpc_platform",
                          "ecp_convert_rate",
                          "ecp_convert_cnt",
                          "ecp_cpa_platform",
                          "live_pay_order_gmv_alias",
                          "luban_live_pay_order_gmv",
                          "ad_live_prepay_and_pay_order_gmv_roi",
                          "live_create_order_count_alias",
                          "luban_live_order_count",
                          "ad_live_create_order_rate",
                          "live_pay_order_count_alias",
                          "luban_live_pay_order_count",
                          "ad_live_pay_order_rate",
                          "live_pay_order_gmv_avg",
                          "ad_live_pay_order_gmv_avg",
                          "luban_live_prepay_order_count",
                          "luban_live_prepay_order_gmv",
                          "live_prepay_order_count_alias",
                          "live_prepay_order_gmv_alias",
                          "live_order_pay_coupon_amount",
                          "total_live_watch_cnt",
                          "total_live_follow_cnt",
                          "live_watch_one_minute_count",
                          "total_live_fans_club_join_cnt",
                          "live_click_cart_count_alias",
                          "live_click_product_count_alias",
                          "total_live_comment_cnt",
                          "total_live_share_cnt",
                          "total_live_gift_cnt",
                          "total_live_gift_amount"), // 需要的字段
                      null,
                      null,
                      1,
                      100 // 分页参数
                      );

              if (response == null
                  || response.getData() == null
                  || response.getData().getRoomList() == null) {
                log.debug("[批量同步直播间]未获取到直播间数据，广告主ID：{}，日期：{}", advertiserId, date);
                continue;
              }

              // 构建直播间ID到直播间详情的映射
              Map<String, DouLiveRoomVO> roomMap =
                  response.getData().getRoomList().stream()
                      .map(this::convertToDouLiveRoomVO)
                      .collect(
                          Collectors.toMap(
                              room -> String.valueOf(room.getRoomId()),
                              Function.identity(),
                              (existing, replacement) -> existing));

              // 更新该广告主下的商品直播间数据
              for (LiveManageItem item : awemeItems) {
                if (item.getDouLiveRoomId() == null) {
                  continue;
                }

                try {
                  DouLiveRoomVO targetRoom = roomMap.get(item.getDouLiveRoomId());
                  if (targetRoom == null) {
                    continue;
                  }

                  String logMessage = "";

                  // 更新直播间信息（这里需要根据实际的API返回类型来实现）
                  log.info(
                      "[批量同步直播间]找到直播间数据，商品ID：{}，直播间ID：{}", item.getId(), item.getDouLiveRoomId());
                  // 计算投流比例：stat_cost / live_pay_order_gmv_alias
                  BigDecimal roomFlowRatio = calculateRoomFlowRatio(targetRoom);
                  item.setDouLiveRoomStartTime(targetRoom.getStartTime());
                  item.setDouLiveRoomEndTime(targetRoom.getEndTime());
                  item.setDouAdvertiserId(targetRoom.getAwemeId());
                  item.setDouAwemeId(targetRoom.getAwemeId());
                  item.setDouLiveRoomId(String.valueOf(targetRoom.getRoomId()));
                  item.setDouLiveRoomName(targetRoom.getRoomTitle());
                  if (roomFlowRatio.compareTo(item.getDouLiveRoomFlowRatio()) != 0) {
                    item.setDouLiveRoomFlowRatio(roomFlowRatio);
                    logMessage +=
                        "投流比例从“"
                            + item.getDouLiveRoomFlowRatio()
                            + "” 改为 “"
                            + roomFlowRatio
                            + "”; ";
                  }
                  item.setDouLiveRoomData(JsonUtil.toJson(targetRoom.getData()));
                  liveManageItemService.updateById(item);

                  if (!logMessage.isEmpty()) {
                    // 记录操作日志
                    operateLogDomainService.addOperatorLog(
                        0L,
                        OperateLogTarget.LIVE_MANAGE_COMMISSION,
                        item.getId(),
                        logMessage,
                        targetRoom);
                  }

                  successCount++;

                  log.debug(
                      "[批量同步直播间]同步成功，商品ID：{}，直播间ID：{}, {}",
                      item.getId(),
                      item.getDouLiveRoomId(),
                      logMessage);

                } catch (Exception e) {
                  log.error("[批量同步直播间]更新失败，商品ID：{}，错误：{}", item.getId(), e.getMessage(), e);
                  failCount++;
                }
              }

            } catch (Exception e) {
              log.error(
                  "[批量同步直播间]API调用失败，广告主ID：{}，日期：{}，错误：{}", advertiserId, date, e.getMessage(), e);
              failCount++;
            }
          }
        }
      }

      log.info("[批量同步直播间]同步完成，成功：{}，失败：{}", successCount, failCount);

    } catch (Exception e) {
      log.error("[批量同步直播间]批量同步失败，错误：{}", e.getMessage(), e);
    }
  }

}
