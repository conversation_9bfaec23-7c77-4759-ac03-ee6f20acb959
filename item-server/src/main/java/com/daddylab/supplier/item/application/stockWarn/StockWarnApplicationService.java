package com.daddylab.supplier.item.application.stockWarn;

import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnInfo;
import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnSummary;

import java.util.List;

/**
 * 库存告警应用服务
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
public interface StockWarnApplicationService {

    /**
     * 执行库存告警检查任务
     * 每天14:30执行，检查库存并生成告警记录
     */
    void executeStockWarnCheck();

    /**
     * 执行库存告警邮件发送任务
     * 每天15:00执行，发送告警邮件
     */
    void executeStockWarnEmailSend();

    /**
     * 手动触发库存告警检查
     *
     * @return 告警商品数量
     */
    Integer manualTriggerStockWarnCheck();

    /**
     * 手动触发库存告警邮件发送
     *
     * @return 发送邮件数量
     */
    Integer manualTriggerStockWarnEmailSend();
}
