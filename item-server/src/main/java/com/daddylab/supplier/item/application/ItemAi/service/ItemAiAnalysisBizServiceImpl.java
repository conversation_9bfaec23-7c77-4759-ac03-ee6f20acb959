package com.daddylab.supplier.item.application.ItemAi.service;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.tokenizer.engine.analysis.AnalysisResult;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.ItemAi.config.ConfirmAuthorityConfig;
import com.daddylab.supplier.item.application.ItemAi.dto.*;
import com.daddylab.supplier.item.application.ItemAi.service.moonshot.*;
import com.daddylab.supplier.item.application.ItemAi.service.moonshot.Message;
import com.daddylab.supplier.item.application.drawer.CheckInfoService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.application.drawer.converter.ItemDrawerConverter;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsGroupVO;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsVo;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.item.dto.ItemSkuListDto;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailPriceVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailVo;
import com.daddylab.supplier.item.controller.itemAi.ItemAiAnalysisController;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.drawer.vo.CheckDetailVO;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerVO;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.BizUnionTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.DivisionLevelValueEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.CommonCheckInfo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.CheckDetailDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.CheckInfoDTO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemLaunchProcessNodeId;
import com.google.gson.Gson;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import liquibase.pro.packaged.S;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.javers.core.diff.Diff;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2025年09月03日 15:05
 */
@Service
@Slf4j
public class ItemAiAnalysisBizServiceImpl implements ItemAiAnalysisBizService {

  @Resource private MoonshotService moonshotService;
  @Resource private OperateLogGateway operateLogGateway;
  @Resource private IItemMarketAnalysisRecordService analysisRecordService;
  @Resource private IItemMarketFileRecordService fileRecordService;
  @Resource private FileDownloadService fileDownloadService;
  @Resource private UserGateway userGateway;
  @Resource private ConfirmAuthorityConfig confirmAuthorityConfig;
  @Resource private ItemBizService itemBizService;

  @Resource private IItemDrawerService iItemDrawerService;

  @Resource IItemLaunchModuleAdviceService itemLaunchModuleAdviceService;

  @Resource IItemService itemService;

  @Resource ItemDrawerService itemDrawerService;

  @Resource INewGoodsService iNewGoodsService;

  @Resource IBizLevelDivisionService iBizLevelDivisionService;

  @Resource NewGoodsBizService newGoodsBizService;

  @Resource IItemDrawerPlatformLinkService itemDrawerPlatformLinkService;

  @Resource PartnerFeignClient partnerFeignClient;

  @Resource CheckInfoService checkInfoService;

  @Resource IItemPriceService iItemPriceService;

  private final ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();

  @Value("${tech-server.url}")
  private String REPORT_TECH_URL;

  private final Gson gson = new Gson();

  @Override
  public void uploadFile(MarketFileUploadCmd cmd) {

    Assert.notNull(cmd.getItemId(), "商品 ID 不能为空");
    final Item item = itemService.getById(cmd.getItemId());
    Assert.notNull(item, "itemID 非法");
    Assert.hasText(item.getCode(), "itemCode不得为空");
    Assert.state(CollUtil.isNotEmpty(cmd.getFileDtoList()), "素材文件不得为空");
    Assert.state(cmd.getFileDtoList().size() <= 30, "素材文件最多 30份");
    fileRecordService
        .lambdaUpdate()
        .eq(ItemMarketFileRecord::getItemId, cmd.getItemId())
        .remove();

    final List<ItemMarketFileRecord> records =
        cmd.getFileDtoList().stream()
            .map(
                val -> {
                  ItemMarketFileRecord fileRecord = new ItemMarketFileRecord();
                  fileRecord.setItemId(cmd.getItemId());
                  fileRecord.setItemCode(item.getCode());
                  fileRecord.setFileName(val.getName());
                  fileRecord.setFileUrl(val.getUrl());
                  return fileRecord;
                })
            .collect(Collectors.toList());

    fileRecordService.saveBatch(records);

    final String fileListStr =
        cmd.getFileDtoList().stream().map(FileDto::getName).collect(Collectors.joining("；"));

    String logStr = "上传商品营销素材文件。文件列表：" + fileListStr;
    operateLogGateway.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.ITEM, cmd.getItemId(), logStr, null);
  }

  @Override
  public Boolean deleteFile(Long id) {
    final ItemMarketFileRecord record = fileRecordService.getById(id);
    Assert.notNull(record, "营销素材文件，ID 非法");
    String logStr = "商品营销素材文件删除，文件名：" + record.getFileName();
    operateLogGateway.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.ITEM, record.getItemId(), logStr, null);
    return fileRecordService.removeById(id);
  }

  private void logAndAlert(String messageTemplate, Object... args) {
    // 记录日志
    log.info(messageTemplate, args);

    // 发送告警
    Alert.text(MessageRobotCode.NOTICE, StringUtil.format(messageTemplate, args));
  }

  @Builder
  @Getter
  private static class RecordErrorCmd {
    Long userId;
    Long itemId;
    String itemCode;
    String errorPrefix;
    Throwable throwable;
    Boolean sendWeChatMsg;
  }

  private void recordError(RecordErrorCmd errorCmd) {
    try {
      log.error(
          errorCmd.getErrorPrefix() + "，itemId: {}, itemCode: {}",
          errorCmd.getItemId(),
          errorCmd.getItemCode(),
          errorCmd.getThrowable());

      // 记录操作日志
      String errorLog =
          StrUtil.format(
              errorCmd.getErrorPrefix() + "，itemId: {}, itemCode: {}，报错：{}",
              errorCmd.getItemId(),
              errorCmd.getItemCode(),
              errorCmd.getThrowable().getMessage());

      operateLogGateway.addOperatorLog(
          errorCmd.getUserId(), OperateLogTarget.ITEM_DRAWER, errorCmd.getItemId(), errorLog, null);

      // 是否发送微信告警
      if (Boolean.TRUE.equals(errorCmd.getSendWeChatMsg())) {
        Alert.text(MessageRobotCode.NOTICE, StringUtil.format(errorLog));
      }
    } catch (Exception e) {
      // 防止错误记录本身出现异常
      log.error("记录错误日志失败", e);
    }
  }

  @Data
  @AllArgsConstructor
  @NoArgsConstructor
  public static class AnalysisResult {
    private ChatCompletion chatCompletion;
    private AnalysisDto analysisDto;
    private ChatCompletion.Usage usage;
  }

  @Override
  public void analysisFile(Long itemId) {
    Long userId = UserContext.getUserId();

    final List<ItemMarketFileRecord> fileRecords =
        fileRecordService.lambdaQuery().eq(ItemMarketFileRecord::getItemId, itemId).list();
    Assert.state(CollUtil.isNotEmpty(fileRecords), "此商品没有营销素材文件，无法进行解析流程");
    String itemCode = fileRecords.get(0).getItemCode();

    final ItemMarketAnalysisRecord record = buildInitRecord(itemId, itemCode, userId);

    CompletableFuture.runAsync(
            () -> {
              log.info("营销素材文件解析流程开始，商品 ID: {}，商品 Code:{}", itemId, itemCode);

              // 文件下载
              final List<FileDto> fileDtoList = downloadFile(itemId, itemCode, userId, fileRecords);
              // 文件分析
              AnalysisResult analysisResult =
                  analyzeMarketFile(itemId, itemCode, userId, fileDtoList);
              // 保存分析记录
              saveAnalysisRecord(itemId, itemCode, userId, analysisResult, record);

              log.info("营销素材文件解析流程全部成功完成，商品 ID: {}，商品 Code:{}", itemId, itemCode);

              // 更新抽屉状态改变。
              log.info("营销素材文件解析流程全部成功完成，更新抽屉状态为【待设计】，商品ID:{}", itemId);
              itemBizService.updateLaunchStatus(itemId, ItemLaunchStatus.TO_BE_DESIGNED);
              logAndAlert("营销素材文件解析流程全部成功完成，更新抽屉状态为【待设计】，商品ID: {}，商品 Code:{}", itemId, itemCode);
            },
            singleThreadExecutor)
        .exceptionally(
            ex -> {
              log.error("营销素材文件解析异步任务最终失败，商品 ID: {}，商品 Code：{}", itemId, itemCode, ex);

              record.setStatus(MarketAnalysisStatus.ERROR.getCode());
              record.setErrorMsg(ex.getMessage());
              analysisRecordService.updateById(record);

              recordError(
                  RecordErrorCmd.builder()
                      .userId(userId)
                      .itemId(itemId)
                      .itemCode(itemCode)
                      .errorPrefix("营销素材文件解析异步任务最终失败")
                      .throwable(new RuntimeException("营销素材文件解析异步任务最终失败"))
                      .sendWeChatMsg(true)
                      .build());

              return null;
            });

    // 提交商品
    //    iItemDrawerService
    //        .getByItemId(itemId)
    //        .ifPresent(
    //            v -> {
    //              log.info("营销素材文件解析流程全部成功完成，自动提交审核，商品ID:{}", itemId);
    //              itemDrawerService.submit(v.getId(), userId, "");
    //            });
  }

  private List<FileDto> downloadFile(
      Long itemId, String itemCode, Long userId, List<ItemMarketFileRecord> fileRecords) {
    final FileDownloadResult result =
        fileDownloadService.downloadFiles(
            fileRecords.stream()
                .map(val -> new FileDto(val.getId(), val.getFileName(), val.getFileUrl(), null))
                .collect(Collectors.toList()));
    if (StrUtil.isNotBlank(result.getError())) {
      recordError(
          RecordErrorCmd.builder()
              .userId(userId)
              .itemId(itemId)
              .itemCode(itemCode)
              .errorPrefix("营销素材文件下载异常")
              .throwable(new RuntimeException(result.getError()))
              .sendWeChatMsg(true)
              .build());
    }

    if (CollUtil.isNotEmpty(result.getFileList())) {
      return result.getFileList();
    } else {
      recordError(
          RecordErrorCmd.builder()
              .userId(userId)
              .itemId(itemId)
              .itemCode(itemCode)
              .errorPrefix("营销素材文件下载异常")
              .throwable(new RuntimeException(result.getError()))
              .sendWeChatMsg(true)
              .build());
      throw new RuntimeException("营销素材文件下载异常");
    }
  }

  private AnalysisResult analyzeMarketFile(
      Long itemId, String itemCode, Long userId, List<FileDto> fileList) {
    try {
      ChatCompletion chatCompletion = moonshotService.processFilesAndChat(fileList);

      if (chatCompletion == null
          || chatCompletion.getChoices() == null
          || chatCompletion.getChoices().isEmpty()) {
        throw new IllegalArgumentException("ChatCompletion 无效");
      }
      String content = chatCompletion.getChoices().get(0).getMessage().getContent();
      final AnalysisDto analysisDto = JsonUtil.parse(content, AnalysisDto.class);
      ChatCompletion.Usage usage = chatCompletion.getUsage();

      logAndAlert("营销素材文件LLM解析成功，商品ID: {}，商品 Code:{}", itemId, itemCode);

      return new AnalysisResult(chatCompletion, analysisDto, usage);
    } catch (Exception e) {
      recordError(
          RecordErrorCmd.builder()
              .userId(userId)
              .itemId(itemId)
              .itemCode(itemCode)
              .errorPrefix("营销素材文件分析处理异常")
              .throwable(e)
              .sendWeChatMsg(true)
              .build());
      throw new RuntimeException("营销素材文件分析处理异常");
    }
  }

  private void saveAnalysisRecord(
      Long itemId,
      String itemCode,
      Long userId,
      AnalysisResult analysisResult,
      ItemMarketAnalysisRecord record) {
    try {
      buildAnalysisRecord(itemId, itemCode, userId, analysisResult, record);
      analysisRecordService.updateById(record);
    } catch (Exception e) {
      recordError(
          RecordErrorCmd.builder()
              .userId(userId)
              .itemId(itemId)
              .itemCode(itemCode)
              .errorPrefix("保存营销素材文件结果保存异常")
              .throwable(e)
              .sendWeChatMsg(true)
              .build());
      throw new RuntimeException("保存营销素材文件结果保存异常");
    }
  }

  private ItemMarketAnalysisRecord buildInitRecord(Long itemId, String itemCode, Long userId) {
    ItemMarketAnalysisRecord record = new ItemMarketAnalysisRecord();
    record.setItemId(itemId);
    record.setItemCode(itemCode);
    record.setCreatedUid(userId);
    record.setStatus(MarketAnalysisStatus.PENDING.getCode());
    analysisRecordService.save(record);
    return record;
  }

  private void buildAnalysisRecord(
      Long itemId,
      String itemCode,
      Long userId,
      AnalysisResult analysisResult,
      ItemMarketAnalysisRecord record) {

    // 基本信息
    record.setCreatedUid(userId);
    record.setItemId(itemId);
    record.setItemCode(itemCode);

    // Token信息
    ChatCompletion.Usage usage = analysisResult.getUsage();
    record.setInputTokens(safeIntegerConvert(usage.getPromptTokens()));
    record.setOutputTokens(safeIntegerConvert(usage.getCompletionTokens()));
    record.setTotalTokens(safeIntegerConvert(usage.getTotalTokens()));

    // 原始响应
    record.setRes(JsonUtil.toJson(analysisResult.getChatCompletion()));

    // 分析数据
    AnalysisDto analysisDto = analysisResult.getAnalysisDto();
    record.setProductTitles(safeStringConvert(analysisDto.getProductTitles()));
    record.setShortProductTitles(safeStringConvert(analysisDto.getShortProductTitles()));
    record.setSearchKeywords(safeStringConvert(analysisDto.getSearchKeywords()));
    record.setTargetAudienceAnalysis(safeStringConvert(analysisDto.getTargetAudienceAnalysis()));
    record.setProductSellingPoints(safeStringConvert(analysisDto.getProductSellingPoints()));
    record.setCustomerServiceBotScripts(
        safeStringConvert(analysisDto.getCustomerServiceBotScripts()));
    record.setGeneralProductAttributes(
        safeStringConvert(analysisDto.getGeneralProductAttributes()));
    record.setGeneralUsageSuggestions(safeStringConvert(analysisDto.getGeneralUsageSuggestions()));
    record.setSummaryAndContentSuggestions(
        safeStringConvert(analysisDto.getSummaryAndContentSuggestions()));

    // 状态
    record.setStatus(MarketAnalysisStatus.END.getCode());
    record.setErrorMsg("");
  }

  @Override
  public void reportTechDocument(Long itemId, Boolean override) {

    final Item item = itemService.getById(itemId);

    Assert.notNull(item, "商品 ID 非法，查不到此商品");

    allConfirmCheck(itemId);

    final List<ItemMarketAnalysisRecord> list =
        analysisRecordService
            .lambdaQuery()
            .eq(ItemMarketAnalysisRecord::getItemId, itemId)
            .eq(ItemMarketAnalysisRecord::getStatus, MarketAnalysisStatus.END)
            .orderByDesc(ItemMarketAnalysisRecord::getId)
            .list();
    Assert.isTrue(CollUtil.isNotEmpty(list), itemId + "，此商品 ID 不存成功的 AI 分析记录");
    final ItemMarketAnalysisRecord record = list.get(0);

    String logPrefix =
        Objects.nonNull(override) && override ? "营销素材解析结果上报TECH（覆盖）" : "营销素材解析结果上报TECH";
    try {
      ReportTechDto reportTechDto =
          buildReportTechDto(
              UserContext.getUserId(),
              Objects.nonNull(record.getTechFileId()) && record.getTechFileId() > 0
                  ? record.getTechFileId()
                  : null,
              itemId,
              item.getName(),
              item.getCode(),
              record);
      final Long techFileId = reportTech(reportTechDto);
      analysisRecordService
          .lambdaUpdate()
          .set(ItemMarketAnalysisRecord::getTechFileId, techFileId)
          .eq(ItemMarketAnalysisRecord::getItemId, itemId)
          .update();

      logAndAlert(logPrefix + "成功，商品ID: {}，商品 Code:{}", itemId, record.getItemCode());
    } catch (Exception e) {
      recordError(
          RecordErrorCmd.builder()
              .userId(UserContext.getUserId())
              .itemId(itemId)
              .itemCode(record.getItemCode())
              .errorPrefix(logPrefix + "异常")
              .throwable(e)
              .sendWeChatMsg(true)
              .build());
    }
  }

  public Long reportTech(ReportTechDto reportTechDto) throws Exception {
    try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
      log.info("Tech report request: {},{}", reportTechDto.getFileName(), reportTechDto.getId());
      HttpPost httpPost = new HttpPost(REPORT_TECH_URL + "/tech/open/repository/itemMaterial");
      httpPost.setHeader("Content-Type", "application/json");

      String jsonBody = JsonUtil.toJson(reportTechDto);
      httpPost.setEntity(new StringEntity(jsonBody, "UTF-8"));

      try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
        String responseBody = EntityUtils.toString(response.getEntity(), "UTF-8");
        log.info("Tech report response: {}", responseBody);
        TechResponse techResponse = JsonUtil.parseJsonStr(responseBody, TechResponse.class);
        return Objects.nonNull(techResponse.getData())
            ? ((Number) techResponse.getData()).longValue()
            : 0L;
      }
    }
  }

  private ReportTechDto buildReportTechDto(
      Long userId,
      Long techFileId,
      Long itemId,
      String name,
      String itemCode,
      ItemMarketAnalysisRecord record) {
    ReportTechDto reportTechDto = new ReportTechDto();
    reportTechDto.setUserId(userId);
    reportTechDto.setId(techFileId);
    reportTechDto.setFileName(name + "_" + itemCode + ".md");

    final Item item = itemService.getById(itemId);

    // AI 营销素材
    final String llmRes = ItemMarketAnalysisRecord.generateMarkdown(record);

    // 后端商品字段
    String itemStr = "";
    final SingleResponse<ItemDetailVo> itemDetailVoRes = itemBizService.queryDetail(itemId);
    if (itemDetailVoRes.isSuccess()) {
      final ItemDetailVo itemDetailVo = itemDetailVoRes.getData();
      Map<String, Object> map = new LinkedHashMap<>();
      map.put("采购品名", itemDetailVo.getItemDetailBaseVo().getName());
      map.put("商品编码", itemCode);
      map.put("供应商商品款号", itemDetailVo.getItemDetailBaseVo().getPartnerCode());
      map.put("商品品类", itemDetailVo.getItemDetailBaseVo().getCategoryPath());
      map.put("商品品牌", itemDetailVo.getItemDetailBaseVo().getBrandName());
      map.put("供应商", itemDetailVo.getItemDetailBaseVo().getProviderName());
      map.put("采购员", itemDetailVo.getItemDetailBaseVo().getBuyerName());
      final String delivery =
          itemDetailVo.getItemDetailBaseVo().getDelivery().equals("0")
              ? "仓库发货"
              : (itemDetailVo.getItemDetailBaseVo().getDelivery().equals("1")
                  ? "工厂发货"
                  : "仓库发货和工厂发货");
      map.put("发货渠道", delivery);
      map.put("仓库", itemDetailVo.getItemDetailProcurementVo().getWarehouseName());
      final String spec =
          itemDetailVo.getItemDetailSkuVoList().stream()
              .map(
                  val ->
                      val.getAttrList().stream()
                          .map(va -> StrUtil.format("{}/{}", va.getName(), va.getValue()))
                          .collect(Collectors.joining("；")))
              .distinct()
              .collect(Collectors.joining("。"));
      map.put("规格", spec);
      final String skuCodes =
          itemDetailVo.getItemDetailSkuVoList().stream()
              .map(ItemSkuListDto::getSkuCode)
              .collect(Collectors.joining(";"));
      map.put("商品sku", skuCodes);

      final SingleResponse<ItemDetailPriceVo> itemDetailPriceVoSingleResponse =
          itemBizService.queryPrice(itemId);
      if (itemDetailPriceVoSingleResponse.isSuccess()
          && Objects.nonNull(itemDetailPriceVoSingleResponse.getData())) {
        final ItemDetailPriceVo data = itemDetailPriceVoSingleResponse.getData();
        data.getSalesPrices().stream()
            .filter(val -> val.getName().equals("产品日销价"))
            .findFirst()
            .ifPresent(v -> map.put("产品日销价", v.getPrice()));
      }
      itemStr = generateMarkdownFromAllListFields(map);
    }

    // 新品商品库字段
    String newGoodsStr;
    Map<String, Object> newGoodsMap = new LinkedHashMap<>();
    if (Objects.nonNull(item)) {
      final String desc =
          IEnum.getEnumByValue(ItemLaunchStatus.class, item.getLaunchStatus()).getDesc();
      newGoodsMap.put("商品状态", desc);
    }
    final List<CorpBizTypeDTO> corpBizType =
        iBizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemId);
    if (CollUtil.isNotEmpty(corpBizType)) {
      String collect1 =
          corpBizType.stream()
              .map(
                  val -> {
                    String biz =
                        IEnum.getEnumByValue(DivisionLevelValueEnum.class, val.getCorpType())
                            .getDesc();
                    final String collect =
                        val.getBizType().stream()
                            .map(
                                va ->
                                    IEnum.getEnumByValue(DivisionLevelValueEnum.class, va)
                                        .getDesc())
                            .collect(Collectors.joining(","));
                    return StrUtil.format("{}/{}", biz, collect);
                  })
              .collect(Collectors.joining("；"));
      newGoodsMap.put("合作方/业务类型", collect1);

      final List<NewGoodsVo> newGoodsVO = newGoodsBizService.getNewGoodsVO(itemId);
      if (CollUtil.isNotEmpty(newGoodsVO)) {
        final NewGoodsVo newGoodsGroupVO = newGoodsVO.get(0);
        newGoodsMap.put(
            "QC负责人",
            newGoodsGroupVO.getQcs().stream()
                .map(DadStaffVO::getNickname)
                .collect(Collectors.joining(",")));
        newGoodsMap.put("产品负责人", newGoodsGroupVO.getPrincipal().getNickname());
        newGoodsMap.put("预计上架时间", newGoodsGroupVO.getLaunchDate());
        newGoodsMap.put("是否 48小时发货", newGoodsGroupVO.getShipmentAging() == 0 ? "是" : "否");
        newGoodsMap.put("是否 7天无理由", newGoodsGroupVO.getNoReason() == 0 ? "是" : "否");
        newGoodsMap.put("发货地", newGoodsGroupVO.getShipmentArea());
        newGoodsMap.put("产品标准名", newGoodsGroupVO.getStandardName());
        newGoodsMap.put("商品类型", newGoodsGroupVO.getItemType() == 1 ? "内部" : "外包");
        newGoodsMap.put("淘宝标题", newGoodsGroupVO.getTbTitle());
      }
      final ItemDrawer itemDrawer = itemDrawerService.getItemDrawer(itemId);
      if (Objects.nonNull(itemDrawer)) {
        newGoodsMap.put("首页文案", itemDrawer.getHomeCopy());
        final List<ItemDrawerPlatformLink> platformLinks =
            itemDrawerPlatformLinkService.getByDrawerId(itemDrawer.getId());
        if (CollUtil.isNotEmpty(platformLinks)) {
          platformLinks.stream()
              .filter(val -> val.getPlatform().equals(Platform.TAOBAO))
              .findFirst()
              .ifPresent(val -> newGoodsMap.put("淘宝链接", val.getLink()));
          platformLinks.stream()
              .filter(val -> val.getPlatform().equals(Platform.XIAOHONGSHU))
              .findFirst()
              .ifPresent(val -> newGoodsMap.put("小红书链接", val.getLink()));
          platformLinks.stream()
              .filter(val -> val.getPlatform().equals(Platform.WECHAT_VIDEO))
              .findFirst()
              .ifPresent(val -> newGoodsMap.put("小程序标题", val.getTitle()));
          platformLinks.stream()
              .filter(val -> val.getPlatform().equals(Platform.WECHAT_VIDEO))
              .findFirst()
              .ifPresent(val -> newGoodsMap.put("微信小程序链接", val.getLink()));
          platformLinks.stream()
              .filter(val -> val.getPlatform().equals(Platform.DOUYIN))
              .findFirst()
              .ifPresent(val -> newGoodsMap.put("抖音链接", val.getLink()));
        }
      }
    }
    newGoodsStr = generateMarkdownFromAllListFields(newGoodsMap);

    // 检测报告
    String checkStr = "";
    Map<String, Object> checkMap = new LinkedHashMap<>();
    final SingleResponse<CommonCheckInfo> commonCheckInfo =
        checkInfoService.getCommonCheckInfo(itemId);
    if (commonCheckInfo.isSuccess() && Objects.nonNull(commonCheckInfo.getData())) {

      final CommonCheckInfo.CommonCheckInfoDetail firstCheck =
          commonCheckInfo.getData().getFirstCheck();
      final String firstCheckInfo = getCheckInfo(firstCheck);
      if (StrUtil.isNotBlank(firstCheckInfo)) {
        checkMap.put("商品首次检测报告", firstCheckInfo);
      }
      final CommonCheckInfo.CommonCheckInfoDetail lastCheck =
          commonCheckInfo.getData().getLastCheck();
      final String lastCheckInfo = getCheckInfo(lastCheck);
      if (StrUtil.isNotBlank(lastCheckInfo)) {
        checkMap.put("商品最新检测报告", lastCheckInfo);
      }

      checkStr = generateMarkdownFromAllListFields(checkMap);
    }

    String markdown = itemStr + "\n" + newGoodsStr + "\n" + checkStr + "\n" + llmRes;
    final String encode = Base64.encode(markdown.getBytes());
    reportTechDto.setFileContent(encode);
    return reportTechDto;
  }

  private String getCheckInfo(CommonCheckInfo.CommonCheckInfoDetail firstCheck) {
    if (firstCheck.getCheckResult() != 1) {
      return "";
    }
    StringBuilder sb1 = new StringBuilder();
    sb1.append("检测类型：").append(checkProjectTye(firstCheck.getProjectType())).append("。");
    sb1.append("报告时间：")
        .append(DateUtil.format(Long.valueOf(firstCheck.getProjectFinishTime())))
        .append("；");
    sb1.append("委托单位：").append(firstCheck.getAgentCompany()).append("。");
    sb1.append("检测结果：").append(firstCheck.getCheckResult() == 1 ? "通过" : "拒绝").append("。");
    sb1.append("检测意见：").append(firstCheck.getOpinion()).append("。");
    // 去掉所有 HTML 标签
    String result = ReUtil.replaceAll(firstCheck.getContent(), "<[^>]+>", "");
    // 去掉多余的空格或 HTML 转义符
    result = result.replace("&nbsp;", " ").trim();
    sb1.append("报告解读：").append(result).append("。");
    if (CollUtil.isNotEmpty(firstCheck.getProjectInfo())) {
      final String collect =
          firstCheck.getProjectInfo().stream()
              .map(
                  va ->
                      StrUtil.format(
                          "检查项目：{}，检查结果：{}，检查类目：{}，检查数量：{}，备注：{}",
                          va.getCheckProject(),
                          va.getCheckResult(),
                          va.getCheckCategory(),
                          va.getCheckNum(),
                          va.getRemark()))
              .collect(Collectors.joining("。"));
      sb1.append("检测项目详情：").append(collect);
    } else {
      sb1.append("检测项目详情：").append("空");
    }
    return sb1.toString();
  }

  private String checkProjectTye(Integer type) {
    if (Objects.isNull(type)) {
      return "";
    }
    switch (type) {
      case 0:
        return "全部";
      case 1:
        return "商城首检";
      case 2:
        return "商城抽检";
      case 3:
        return "老爸抽检";
      case 4:
        return "老爸抽检-抽检";
      default:
        return "";
    }
  }

  CheckDetailVO getCheckDetailVO(Long checkId) {
    try {
      Rsp<CheckDetailDTO> lastCheckDetail = partnerFeignClient.getCheckDetail(checkId);
      if (!lastCheckDetail.isSuccess() || Objects.isNull(lastCheckDetail.getData())) {
        throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "获取最新抽检失败");
      }
      return ItemDrawerConverter.INSTANCE.checkDetailDTOToVO(lastCheckDetail.getData());
    } catch (Exception e) {
      log.error("[检测报告]获取首检失败", e);
    }
    return null;
  }

  public static String generateMarkdownFromAllListFields(Map<String, Object> data) {
    StringBuilder sb = new StringBuilder();

    for (Map.Entry<String, Object> entry : data.entrySet()) {
      String title = entry.getKey();
      Object value = entry.getValue();
      if (value != null) {
        appendListField(sb, title, value.toString());
      }
    }

    return sb.toString();
  }

  private static void appendListField(StringBuilder sb, String title, String strList) {
    if (strList != null && !strList.trim().isEmpty()) {
      String[] lines = strList.split("[\\r\\n,]+"); // 支持换行和逗号分隔
      List<String> list =
          Arrays.stream(lines)
              .map(String::trim)
              .filter(s -> !s.isEmpty())
              .collect(Collectors.toList());

      if (!list.isEmpty()) {
        sb.append("## ").append(title).append("\n");
        for (String item : list) {
          sb.append("- ").append(item).append("\n");
        }
        sb.append("\n");
      }
    }
  }

  private String safeStringConvert(List<String> list) {
    return CollUtil.isNotEmpty(list) ? String.join("。", list) : "";
  }

  private String safeJsonConvert(Object obj) {
    return obj != null ? JsonUtil.toJson(obj) : "[]";
  }

  private String safeStringConvert(String str) {
    return str != null ? str : "";
  }

  private Integer safeIntegerConvert(Integer num) {
    return num != null ? num : 0;
  }

  @Mapper
  interface AnalysisResultAssembler {
    ItemAiAnalysisBizServiceImpl.AnalysisResultAssembler INST =
        Mappers.getMapper(ItemAiAnalysisBizServiceImpl.AnalysisResultAssembler.class);

    @Mapping(target = "recordId", source = "record.id")
    @Mapping(target = "createdTime", ignore = true)
    AnalysisResReqAndResBo entityToVo(ItemMarketAnalysisRecord record);
  }

  @Override
  public AnalysisResReqAndResBo queryAnalysisRes(Long itemId) {

    final List<ItemMarketAnalysisRecord> list =
        analysisRecordService
            .lambdaQuery()
            .eq(ItemMarketAnalysisRecord::getItemId, itemId)
            .orderByDesc(ItemMarketAnalysisRecord::getId)
            .list();
    return CollUtil.isEmpty(list)
        ? null
        : list.stream()
            .map(
                val -> {
                  final AnalysisResReqAndResBo bo = AnalysisResultAssembler.INST.entityToVo(val);
                  bo.setCreatedTime(DateUtil.format(val.getCreatedAt()));
                  return bo;
                })
            .collect(Collectors.toList())
            .get(0);
  }

  @Override
  public List<OptimizeAnalysisCmd.AnalysisUnitDto> llmModifyResult(OptimizeAnalysisCmd cmd) {
    final Long userId = UserContext.getUserId();
    try {
      final String prompt = OptimizeAnalysisCmd.buildOptimizePrompt(cmd);
      List<Message> messages = new LinkedList<>();
      messages.add(new Message("system", "你是一位顶级的营销文案专家和AI优化师，擅长精准理解并完美执行需求。"));
      messages.add(new Message("user", prompt));
      final ChatCompletion chatCompletion =
          moonshotService.createChatCompletionWithContinuation(messages);
      log.info("营销素材文件LLM重新修正响应,{}", gson.toJson(chatCompletion));
      if (chatCompletion == null
          || chatCompletion.getChoices() == null
          || chatCompletion.getChoices().isEmpty()) {
        throw new RuntimeException("营销素材文件LLM重新修正，处理结果为空");
      }
      String content = chatCompletion.getChoices().get(0).getMessage().getContent();
      final List<OptimizeAnalysisCmd.AnalysisUnitDto> analysisUnitDtoList =
          JsonUtil.parseList(content, OptimizeAnalysisCmd.AnalysisUnitDto.class);

      analysisRecordService
          .lambdaUpdate()
          .setSql("optimize_count = optimize_count + 1")
          .eq(ItemMarketAnalysisRecord::getId, cmd.getRecordId())
          .update();

      logAndAlert("营销素材文件LLM重新修正成功，商品ID: {}，商品 Code:{}", cmd.getItemId(), cmd.getItemCode());

      return analysisUnitDtoList;
    } catch (Exception e) {
      recordError(
          RecordErrorCmd.builder()
              .userId(userId)
              .itemId(cmd.getItemId())
              .itemCode(cmd.getItemCode())
              .errorPrefix("营销素材文件LLM重新修正异常")
              .throwable(e)
              .sendWeChatMsg(true)
              .build());
      throw new RuntimeException("营销素材文件LLM重新修正失败");
    }
  }

  @Override
  public void editResult(AnalysisResReqAndResBo cmd) {

    final ItemMarketAnalysisRecord oldOne = analysisRecordService.getById(cmd.getRecordId());
    Assert.notNull(oldOne, "素材 ID 非法");

    // 检查是否操作了确认按钮，并且是否存在操作权限。
//    verifyUserRight(UserContext.getUserId(), cmd);

    // 构建，更新素材
    final ItemMarketAnalysisRecord newOne = buildNewAnalysisRecord(cmd);
    analysisRecordService.updateById(newOne);

    // 保存编辑记录
    final Diff diff = DiffUtil.diff(oldOne, newOne);
    final String editLog = DiffUtil.getSimpleDiffLog(diff, ItemMarketAnalysisRecord.class);
    operateLogGateway.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.ITEM_DRAWER, cmd.getItemId(), editLog, null);

    // 检查是否上报 TECH
    // 1.整个上新流程走完，上报 TECH
    // 2.即使流程走完，业务也可以在 AI 素材 TAB 修改，一旦修改就要上报。

  }

  private void verifyUserRight(Long userId, AnalysisResReqAndResBo cmd) {

    Assert.state(Objects.nonNull(userId) && userId > 0L, "用户ID非法，无法编辑");

    final StaffInfo staffInfo = userGateway.queryStaffInfoById(userId);
    Assert.notNull(staffInfo, "用户信息获取失败，无法编辑");

    final String userNickname = staffInfo.getNickname();

    final boolean isAdmin =
        ListUtil.of(confirmAuthorityConfig.getAdmin().split(",")).contains(userNickname.trim());
    if (isAdmin) {
      return;
    }

    if (Objects.nonNull(cmd.getTitleKeyConfirm()) && 1 == cmd.getTitleKeyConfirm()) {
      Assert.state(
          confirmAuthorityConfig.getTitleKeyConfirm().stream()
              .map(ConfirmUserDto::getId)
              .collect(Collectors.toList())
              .contains(userId),
          StrUtil.format("抱歉{}，你没有确认[商品标题/短标题/搜索关键词]信息的权限", userNickname));
    }

    if (Objects.nonNull(cmd.getTargetSellConfirm()) && 1 == cmd.getTargetSellConfirm()) {
      Assert.state(
          confirmAuthorityConfig.getTargetSellConfirm().stream()
              .map(ConfirmUserDto::getId)
              .collect(Collectors.toList())
              .contains(userId),
          StrUtil.format("抱歉{}，你没有确认[适用人群/商品卖点]信息的权限", userNickname));
    }

    if (Objects.nonNull(cmd.getBotScriptsConfirm()) && 1 == cmd.getBotScriptsConfirm()) {
      Assert.state(
          confirmAuthorityConfig.getBotScriptsConfirm().stream()
              .map(ConfirmUserDto::getId)
              .collect(Collectors.toList())
              .contains(userId),
          StrUtil.format("抱歉{}，你没有确认[机器人话术]信息的权限", userNickname));
    }

    if (Objects.nonNull(cmd.getGeneralConfirm()) && 1 == cmd.getGeneralConfirm()) {
      Assert.state(
          confirmAuthorityConfig.getGeneralConfirm().stream()
              .map(ConfirmUserDto::getId)
              .collect(Collectors.toList())
              .contains(userId),
          StrUtil.format("抱歉{}，你没有确认[商品属性（通用）/使用建议（通用）]信息的权限", userNickname));
    }
  }

  private ItemMarketAnalysisRecord buildNewAnalysisRecord(AnalysisResReqAndResBo cmd) {

    ItemMarketAnalysisRecord record = new ItemMarketAnalysisRecord();

    record.setId(cmd.getRecordId());
    record.setItemId(cmd.getItemId());
    record.setItemCode(cmd.getItemCode());

    Optional.ofNullable(cmd.getProductTitles())
        .filter(StrUtil::isNotBlank)
        .ifPresent(record::setProductTitles);
    Optional.ofNullable(cmd.getShortProductTitles())
        .filter(StrUtil::isNotBlank)
        .ifPresent(record::setShortProductTitles);
    Optional.ofNullable(cmd.getSearchKeywords())
        .filter(StrUtil::isNotBlank)
        .ifPresent(record::setSearchKeywords);
    Optional.ofNullable(cmd.getTargetAudienceAnalysis())
        .filter(StrUtil::isNotBlank)
        .ifPresent(record::setTargetAudienceAnalysis);
    Optional.ofNullable(cmd.getProductSellingPoints())
        .filter(StrUtil::isNotBlank)
        .ifPresent(record::setProductSellingPoints);
    Optional.ofNullable(cmd.getCustomerServiceBotScripts())
        .filter(StrUtil::isNotBlank)
        .ifPresent(record::setCustomerServiceBotScripts);
    Optional.ofNullable(cmd.getGeneralProductAttributes())
        .filter(StrUtil::isNotBlank)
        .ifPresent(record::setGeneralProductAttributes);
    Optional.ofNullable(cmd.getGeneralUsageSuggestions())
        .filter(StrUtil::isNotBlank)
        .ifPresent(record::setGeneralUsageSuggestions);
    Optional.ofNullable(cmd.getSummaryAndContentSuggestions())
        .filter(StrUtil::isNotBlank)
        .ifPresent(record::setSummaryAndContentSuggestions);

    Optional.ofNullable(cmd.getTitleKeyConfirm())
        .filter(v -> 1 == v)
        .ifPresent(v -> record.setTitleKeyConfirm(1));
    Optional.ofNullable(cmd.getTargetSellConfirm())
        .filter(v -> 1 == v)
        .ifPresent(v -> record.setTargetSellConfirm(1));
    Optional.ofNullable(cmd.getBotScriptsConfirm())
        .filter(v -> 1 == v)
        .ifPresent(v -> record.setBotScriptsConfirm(1));
    Optional.ofNullable(cmd.getGeneralConfirm())
        .filter(v -> 1 == v)
        .ifPresent(v -> record.setGeneralConfirm(1));

    // 法务审核
    if (StrUtil.isNotBlank(cmd.getLegalAdvice())) {
      record.setLegalAdvice(cmd.getLegalAdvice());
    }
    if (Objects.nonNull(cmd.getLegalAdviceStatus()) && 1 == cmd.getLegalAdviceStatus()) {
      record.setLegalAdviceStatus(1);
      // 检查是否存在商品信息的QC审核信息
      final Integer count =
          itemLaunchModuleAdviceService
              .lambdaQuery()
              .eq(ItemLaunchModuleAdvice::getItemId, cmd.getItemId())
              .eq(ItemLaunchModuleAdvice::getNode, ItemLaunchProcessNodeId.LEGAL)
              .isNotNull(ItemLaunchModuleAdvice::getAdvice)
              .orderByDesc(ItemLaunchModuleAdvice::getRound)
              .count();
      Assert.state(count > 0, "商品资料，法务 修改意见不得为空");
    }

    // QC 审核意见确认
    if (StrUtil.isNotBlank(cmd.getQcAdvice())) {
      record.setQcAdvice(cmd.getQcAdvice());
    }
    if (Objects.nonNull(cmd.getQcAdviceStatus()) && 1 == cmd.getQcAdviceStatus()) {
      record.setQcAdviceStatus(1);
      // 检查是否存在商品信息的QC审核信息
      final Integer count =
          itemLaunchModuleAdviceService
              .lambdaQuery()
              .eq(ItemLaunchModuleAdvice::getItemId, cmd.getItemId())
              .eq(ItemLaunchModuleAdvice::getNode, ItemLaunchProcessNodeId.QC)
              .isNotNull(ItemLaunchModuleAdvice::getAdvice)
              .orderByDesc(ItemLaunchModuleAdvice::getRound)
              .count();
      Assert.state(count > 0, "商品资料，QC 修改意见不得为空");
    }

    return record;
  }

  @Override
  public Boolean techDocumentExist(Long itemId) {
    return analysisRecordService
            .lambdaQuery()
            .eq(ItemMarketAnalysisRecord::getItemId, itemId)
            .ne(ItemMarketAnalysisRecord::getTechFileId, StrUtil.EMPTY)
            .count()
        > 0;
  }

  @Mapper
  interface AnalysisConfirmRightAssembler {
    ItemAiAnalysisBizServiceImpl.AnalysisConfirmRightAssembler INST =
        Mappers.getMapper(ItemAiAnalysisBizServiceImpl.AnalysisConfirmRightAssembler.class);

    ConfirmAuthorityVo entityToVo(ConfirmAuthorityConfig record);
  }

  @Override
  public ConfirmAuthorityVo getRight(Long itemId) {
    // 使用卫语句，如果itemId为null，直接返回默认值
    if (Objects.isNull(itemId)) {
      return AnalysisConfirmRightAssembler.INST.entityToVo(confirmAuthorityConfig);
    }

    final Item item = itemService.getById(itemId);
    // 如果item为null，也直接返回默认值
    if (Objects.isNull(item)) {
      return AnalysisConfirmRightAssembler.INST.entityToVo(confirmAuthorityConfig);
    }

    // 根据 businessLine 选择合适的过滤器
    Predicate<ConfirmUserDto> filterPredicate;
    if (0 == item.getBusinessLine()) {
      filterPredicate = ConfirmUserDto::getMall;
    } else if (2 == item.getBusinessLine()) {
      filterPredicate = ConfirmUserDto::getDecoration;
    } else {
      // 如果 businessLine 不是 0 或 2，返回默认值
      return AnalysisConfirmRightAssembler.INST.entityToVo(confirmAuthorityConfig);
    }

    // 调用一个私有辅助方法来构建VO，避免代码重复
    return buildConfirmAuthorityVoWithFilter(filterPredicate);
  }

  /**
   * 根据指定的过滤器构建 ConfirmAuthorityVo 对象
   *
   * @param predicate 过滤器
   * @return ConfirmAuthorityVo
   */
  private ConfirmAuthorityVo buildConfirmAuthorityVoWithFilter(
      Predicate<ConfirmUserDto> predicate) {
    ConfirmAuthorityVo vo = new ConfirmAuthorityVo();
    vo.setTitleKeyConfirm(
        confirmAuthorityConfig.getTitleKeyConfirm().stream()
            .filter(predicate)
            .collect(Collectors.toList()));
    vo.setTargetSellConfirm(
        confirmAuthorityConfig.getTargetSellConfirm().stream()
            .filter(predicate)
            .collect(Collectors.toList()));
    vo.setBotScriptsConfirm(
        confirmAuthorityConfig.getBotScriptsConfirm().stream()
            .filter(predicate)
            .collect(Collectors.toList()));
    vo.setGeneralConfirm(
        confirmAuthorityConfig.getGeneralConfirm().stream()
            .filter(predicate)
            .collect(Collectors.toList()));
    return vo;
  }

  @Override
  public void allConfirmCheck(Long itemId) {

    final List<ItemMarketAnalysisRecord> list =
        analysisRecordService
            .lambdaQuery()
            .eq(ItemMarketAnalysisRecord::getItemId, itemId)
            .eq(ItemMarketAnalysisRecord::getStatus, MarketAnalysisStatus.END)
            .orderByDesc(ItemMarketAnalysisRecord::getId)
            .list();
    Assert.isTrue(CollUtil.isNotEmpty(list), itemId + "，此商品 ID 不存成功的 AI 分析记录");
    final ItemMarketAnalysisRecord record = list.get(0);
    Assert.state(record.getTitleKeyConfirm() == 1, "AI 营销素材，请先确认【商品标题/短标题/搜索关键词】");
    Assert.state(record.getTargetSellConfirm() == 1, "AI 营销素材，请先确认【适用人群/商品卖点】");
    Assert.state(record.getBotScriptsConfirm() == 1, "AI 营销素材，请先确认【机器人话术】");
    Assert.state(record.getGeneralConfirm() == 1, "AI 营销素材，请先确认【通用商品属性/通用使用建议】");
  }
}
