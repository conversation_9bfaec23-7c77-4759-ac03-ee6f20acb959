package com.daddylab.supplier.item.application.saleItem.service.impl;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.bean.BeanDesc;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.PropDesc;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.*;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.supplier.item.application.category.CategoryBizService;
import com.daddylab.supplier.item.application.drawer.ItemApprovalAdviceBizService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerAuditBizService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerMergeBizService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.application.fold.FoldBizService;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.itemRunning.ItemRunningBizService;
import com.daddylab.supplier.item.application.itemTag.ItemTagBizService;
import com.daddylab.supplier.item.application.message.QyMsgSendUtils;
import com.daddylab.supplier.item.application.message.wechat.MsgEvent;
import com.daddylab.supplier.item.application.message.wechat.RecipientConfig;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.saleItem.dto.*;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsAssembler;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.*;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.ErrorChecker;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.NewGoodsTransMapper;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.category.dto.CategoryTree;
import com.daddylab.supplier.item.controller.category.dto.CategoryTreeNode;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.controller.item.dto.ItemWithLaunchPlanDto;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerChangeEnum;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.domain.drawer.form.ItemDrawerPlatformLinkVO;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerAttrImage;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerAttrImageEntity;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerAttrImages;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerSkuImageEntity;
import com.daddylab.supplier.item.domain.exportTask.ExportDomainService;
import com.daddylab.supplier.item.domain.exportTask.ExportTaskGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.item.data.ItemLaunchPlanItemRefInfo;
import com.daddylab.supplier.item.domain.item.event.ItemQcChangeEvent;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemImageGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.config.mybatisPlus.BaseFuncEnum;
import com.daddylab.supplier.item.infrastructure.diff.CollChange;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.domain.PropertyAlias;
import com.daddylab.supplier.item.infrastructure.doudian.DouDianSyncBO;
import com.daddylab.supplier.item.infrastructure.doudian.DouDianTemplate;
import com.daddylab.supplier.item.infrastructure.doudian.Resp;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.events.SaveEvent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ItemBaseDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerPlatformLink;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemLaunchPlanMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.NewGoodsMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ChangePropertyObj;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.ObjectUtil;
import com.daddylab.supplier.item.types.BatchResult;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableList.Builder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.ApiModelProperty;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.skywalking.apm.toolkit.trace.RunnableWrapper;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionalEventListener;
import org.springframework.util.Assert;
import org.springframework.util.StopWatch;

/**
 * <AUTHOR> @ClassName NewGoodsBizServiceImpl.java
 * @description
 * @createTime 2022年04月18日 16:32:00
 */
@Slf4j
@Service
public class NewGoodsBizServiceImpl implements NewGoodsBizService {

  public static final FieldMetas fieldMetas =
      new FieldMetas()
          .add(
              FieldMeta.of(NewGoodsCmd::getPrincipalId)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PRODUCT_FIELDS)
                  .required())
          .add(
              FieldMeta.of(NewGoodsCmd::getPlanId)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PRODUCT_FIELDS)
                  .required()
                  .emptyCheckLenient()
                  .notCheck())
          .add(
              FieldMeta.of(NewGoodsCmd::getIsLongTerm)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required()
                  .notCheck())
          .add(
              FieldMeta.of(NewGoodsCmd::getActivePeriodStart)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required()
                  .notCheck())
          .add(
              FieldMeta.of(NewGoodsCmd::getActivePeriodEnd)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required()
                  .notCheck())
          .add(
              FieldMeta.of(NewGoodsCmd::getNoReason)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required())
          .add(
              FieldMeta.of(NewGoodsCmd::getLinePrice)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required())
          .add(
              FieldMeta.of(NewGoodsCmd::getDailyPrice)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required())
          .add(FieldMeta.of(NewGoodsCmd::getDailyActivities).accessAny().emptyCheckLenient())
          .add(
              FieldMeta.of(NewGoodsCmd::getActivePrice)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required())
          .add(
              FieldMeta.of(NewGoodsCmd::getActiveContent)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required()
                  .emptyCheckLenient())
          .add(
              FieldMeta.of(NewGoodsCmd::getChannelLowest)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required())
          .add(
              FieldMeta.of(NewGoodsCmd::getLiveActive)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getIsReduce)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required())
          .add(
              FieldMeta.of(NewGoodsCmd::getShipmentType)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required())
          .add(
              FieldMeta.of(NewGoodsCmd::getShipmentArea)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required()
                  .emptyCheckLenient())
          .add(
              FieldMeta.of(NewGoodsCmd::getShipmentAging)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required())
          .add(
              FieldMeta.of(NewGoodsCmd::getLogistics)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required()
                  .emptyCheckLenient())
          .add(
              FieldMeta.of(NewGoodsCmd::getExpressTemplate)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required()
                  .emptyCheckLenient())
          .add(
              FieldMeta.of(NewGoodsCmd::getIsCoupon)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS)
                  .required())
          .add(
              FieldMeta.of(NewGoodsCmd::getALevelActivityPrice)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getALevelActivityGift)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getALevelActivityLivePrice)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getALevelActivityLiveGift)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getSLevelPromotePrice)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getSLevelPromoteRule)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getSLevelPromoteLivePrice)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getSLevelPromoteLiveRule)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS))
          .add(FieldMeta.of(NewGoodsCmd::getRemark).accessAny())
          .add(FieldMeta.of(NewGoodsCmd::getRunFeedback).accessAny())
          .add(
              FieldMeta.of(NewGoodsCmd::getChannelLowest)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PRODUCT_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getIsReduce)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PRODUCT_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getIsCoupon)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PRODUCT_FIELDS))
          .add(
              FieldMeta.of(NewGoodsCmd::getRunFeedback)
                  .access(GlobalConstant.NEW_GOODS_EDIT_PRODUCT_FIELDS));
  @Autowired FileGateway fileGateway;
  @Autowired private RedissonClient redissonClient;
  @Autowired private INewGoodsService iNewGoodsService;

  @Value("${files.import-new-goods-template}")
  private String excelTemplateUrl;

  @Autowired private CombinationItemBizService combinationItemBizService;
  @Autowired private ItemSkuGateway itemSkuGateway;
  @Autowired private INewGoodsService newGoodsService;
  @Autowired private ExportTaskGateway exportTaskGateway;
  @Autowired private OperateLogDomainService operateLogDomainService;
  @Autowired private ExportDomainService exportDomainService;
  @Autowired private IItemService itemService;
  @Autowired private ItemBizService itemBizService;

  @Autowired
  @Qualifier("UserGatewayCacheImpl")
  private UserGateway userGateway;

  @Autowired private BuyerGateway buyerGateway;
  @Autowired private ItemGateway itemGateway;
  @Autowired private BrandGateway brandGateway;
  @Autowired private CategoryGateway categoryGateway;
  @Autowired private CategoryBizService categoryBizService;
  @Autowired private ItemRunningBizService itemRunningBizService;
  @Autowired private IItemLaunchPlanService itemLaunchPlanService;
  @Autowired private StaffService staffService;
  @Autowired private NewGoodsMapper newGoodsMapper;
  @Autowired private IItemDrawerImageService itemDrawerImageService;
  @Autowired private IItemDrawerService itemDrawerService;
  @Autowired private IItemDrawerPlatformLinkService itemDrawerPlatformItemService;
  @Autowired private ItemDrawerService itemDrawerAppService;
  @Autowired private ItemDrawerService itemDrawerBizService;
  @Resource private ItemApprovalAdviceBizService itemApprovalAdviceBizService;
  @Autowired private ItemDrawerService drawerService;
  @Autowired private ItemImageGateway itemImageGateway;
  @Autowired private FoldBizService foldService;
  @Autowired private IItemProcurementService itemProcurementService;
  @Autowired private OperateLogGateway operateLogGateway;

  @Resource IThirdPlatformSyncService iThirdPlatformSyncService;

  @Resource IThirdPlatformSyncLogService iThirdPlatformSyncLogService;

  @Resource DouDianTemplate douDianTemplate;

  @Resource ItemLaunchPlanMapper itemLaunchPlanMapper;

  @Resource IItemLaunchPlanItemRefService iItemLaunchPlanItemRefService;

  @Resource private IItemLaunchStatsService itemLaunchStatsService;

  @Resource private RefreshConfig refreshConfig;
  @Resource IItemDrawerLiveVerbalService iItemDrawerLiveVerbalService;

  @Autowired ItemDrawerMergeBizService itemDrawerMergeBizService;

  @Autowired IItemTrainingMaterialsService itemTrainingMaterialsService;

  @Autowired private RecipientConfig recipientConfig;

  @Autowired private ItemTagBizService itemTagBizService;

  @Resource IItemSkuService iItemSkuService;
  @Autowired private ItemDrawerAuditBizService itemDrawerAuditBizService;

  @Override
  public SingleResponse<LockResult> lock(Long id) {
    Assert.notNull(id, "锁定记录ID不能为空");
    final RBucket<Long> bucket = redissonClient.getBucket("NEW_GOODS_EDIT:" + id);
    final Long userId = UserContext.getUserId();
    Long lockUserId = bucket.get();
    boolean lockSuccess = false;
    if (lockUserId == null && bucket.compareAndSet(null, userId)) {
      lockSuccess = true;
    } else if (lockUserId != null
        && lockUserId.equals(userId)
        && bucket.compareAndSet(userId, userId)) {
      lockSuccess = true;
    }
    if (lockSuccess) {
      bucket.expire(30, TimeUnit.SECONDS);
      return SingleResponse.of(LockResult.ofSuccess());
    }
    lockUserId = bucket.get();
    final StaffInfo staffInfo = userGateway.queryStaffInfoById(lockUserId);
    return SingleResponse.of(LockResult.ofFail(staffInfo));
  }

  @Override
  public Response unlock(Long id) {
    Assert.notNull(id, "锁定记录ID不能为空");
    final RBucket<Long> bucket = redissonClient.getBucket("NEW_GOODS_EDIT:" + id);
    final Long userId = UserContext.getUserId();
    Long lockUserId = bucket.get();
    if (lockUserId != null && lockUserId.equals(userId)) {
      bucket.delete();
    }
    return Response.buildSuccess();
  }

  @Override
  public int count(NewGoodsQueryPage queryPage) {
    MPJLambdaWrapper<NewGoods> queryWrapper = setParam(queryPage);
    return newGoodsMapper.selectJoinCount(queryWrapper);
  }

  @Override
  public List<GoodsImages> queryImages(NewGoodsQueryPage queryPage) {
    MPJLambdaWrapper<NewGoods> queryWrapper = setParam(queryPage);
    setSelect(queryWrapper);
    final List<NewGoods> newGoods = newGoodsMapper.selectJoinList(NewGoods.class, queryWrapper);
    if (CollectionUtil.isEmpty(newGoods)) {
      return Collections.emptyList();
    }
    final List<Long> itemIds =
        newGoods.stream().map(NewGoods::getItemId).collect(Collectors.toList());
    final Map<Long, Long> mainItemIdMap = itemDrawerMergeBizService.getMainItemIdBatch(itemIds);
    final ArrayList<Long> allItemIds = new ArrayList<>(itemIds);
    allItemIds.addAll(new HashSet<>(mainItemIdMap.values()));
    if (allItemIds.size() > 50) {
      log.error("新品商品图片导出不能同时超过50个");
      return Collections.emptyList();
    }

    final List<ItemDrawer> itemDrawers =
        itemDrawerService
            .lambdaQuery()
            .in(ItemDrawer::getItemId, allItemIds)
            .select(ItemDrawer::getId, ItemDrawer::getItemId, ItemDrawer::getStandardName)
            .list();
    final List<Long> itemDrawerIds =
        itemDrawers.stream().map(ItemDrawer::getId).collect(Collectors.toList());
    final Map<Long, ItemDrawer> itemDrawerMap =
        itemDrawers.stream().collect(Collectors.toMap(ItemDrawer::getItemId, Function.identity()));
    final Map<Long, Long> itemDrawerIdToItemIdMap =
        itemDrawers.stream().collect(Collectors.toMap(ItemDrawer::getId, ItemDrawer::getItemId));

    if (CollUtil.isEmpty(itemDrawerIds)) {
      return new LinkedList<>();
    }

    List<ItemDrawerImage> list =
        itemDrawerImageService
            .lambdaQuery()
            .ne(ItemDrawerImage::getUrl, "")
            .in(ItemDrawerImage::getDrawerId, itemDrawerIds)
            .list();
    final Map<Long, Map<ItemDrawerImageTypeEnum, List<ImageBO>>> itemDrawerImageUrlsMap =
        list.stream()
            .collect(
                Collectors.groupingBy(
                    v -> itemDrawerIdToItemIdMap.get(v.getDrawerId()),
                    Collectors.groupingBy(
                        ItemDrawerImage::getType,
                        Collectors.mapping(
                            itemDrawerImage -> {
                              ImageBO bo = new ImageBO();
                              bo.setUrl(itemDrawerImage.getUrl());
                              bo.setSort(itemDrawerImage.getSort());
                              final String filename = itemDrawerImage.getFilename();
                              bo.setFileName(
                                  StringUtil.ensureSuffix(
                                      filename, "." + itemDrawerImage.getExt()));
                              return bo;
                            },
                            Collectors.toList()))));

    final ArrayList<GoodsImages> goodsImages = new ArrayList<>();
    for (NewGoods newGood : newGoods) {
      Long itemId = newGood.getItemId();
      if (mainItemIdMap.containsKey(itemId)) {
        itemId = mainItemIdMap.get(itemId);
      }
      final ItemDrawer itemDrawer = itemDrawerMap.get(itemId);
      if (itemDrawer == null) {
        continue;
      }
      final GoodsImages goodsImagesItem = new GoodsImages();
      goodsImagesItem.setNewGoodsId(newGood.getId());
      goodsImagesItem.setItemId(newGood.getItemId());
      // 获取平台商品标题
      String platformTitle = itemDrawer.getTbTitle();

      goodsImagesItem.setStandardName(
          Stream.of(itemDrawer.getStandardName(), platformTitle)
              .filter(StringUtil::isNotBlank)
              .findAny()
              .orElse("商品无标题#ID" + itemDrawer.getItemId()));
      if (StringUtils.isBlank(goodsImagesItem.getStandardName())) {
        continue;
      }
      final Map<ItemDrawerImageTypeEnum, List<ImageBO>> itemDrawerImageTypeEnumListMap =
          itemDrawerImageUrlsMap.get(itemId);
      if (CollUtil.isEmpty(itemDrawerImageTypeEnumListMap)) {
        continue;
      }
      goodsImagesItem.setImages(
          itemDrawerImageTypeEnumListMap.getOrDefault(
              ItemDrawerImageTypeEnum.ITEM, Collections.emptyList()));
      goodsImagesItem.setDetailImages(
          itemDrawerImageTypeEnumListMap.getOrDefault(
              ItemDrawerImageTypeEnum.DETAIL, Collections.emptyList()));
      goodsImagesItem.setSkuImages(
          itemDrawerImageTypeEnumListMap.getOrDefault(
              ItemDrawerImageTypeEnum.SKU, Collections.emptyList()));
      goodsImagesItem.setMainImageVideos(
          itemDrawerImageTypeEnumListMap.getOrDefault(
              ItemDrawerImageTypeEnum.MAIN_IMAGE_VIDEO, Collections.emptyList()));
      goodsImagesItem.setAttrImages(
          itemDrawerImageTypeEnumListMap.getOrDefault(
              ItemDrawerImageTypeEnum.ATTR, Collections.emptyList()));
      goodsImages.add(goodsImagesItem);
    }
    return goodsImages;
  }

  @Override
  public NewGoodsVo getNewGoodsVoById(Long newGoodsId) {
    final NewGoodsQueryPage queryPage = new NewGoodsQueryPage();
    queryPage.setNewGoodsIds(Collections.singletonList(newGoodsId));
    queryPage.setPageIndex(1);
    queryPage.setPageSize(1);
    queryPage.setShowAll(true);
    //        queryPage.setBusinessLine(UserPermissionJudge.getUserBusinessLineValues());
    final PageResponse<NewGoodsVo> response = queryPage(queryPage);
    return response.getData().isEmpty() ? null : response.getData().get(0);
  }

  private MPJLambdaWrapper<NewGoods> getWrapper(NewGoodsQueryPage queryPage) {
    MPJLambdaWrapper<NewGoods> wrapper = MPJWrappers.lambdaJoin();
    joinWrapper(wrapper);
    whereWrapper(wrapper, queryPage);
    if (!queryPage.getShowAll()) {
      wrapper.and(
          r ->
              r.eq(Buyer::getUserId, queryPage.getUserId())
                  .or()
                  .eq(NewGoods::getPrincipalId, queryPage.getUserId())
                  .or()
                  .like(ItemProcurement::getQcIds, queryPage.getUserId() + ","));
    }
    return wrapper;
  }

  @Autowired private IBizLevelDivisionService bizLevelDivisionService;

  @Override
  public PageResponse<NewGoodsGroupVO> queryPageGroupBySpu(NewGoodsQueryPage queryPage) {
    // 分页查询根据spu的纬度进行分页
    Set<Long> itemIdSet = new HashSet<>();
    boolean filterStatus = false;
    if (Objects.nonNull(queryPage.getLiveVerbalTrickStatus())) {
      filterStatus = true;

      Set<Long> itemIdsForLiveStatus = new HashSet<>();

      List<Long> itemIdList =
          iItemDrawerLiveVerbalService
              .lambdaQuery()
              .eq(
                  ItemDrawerLiveVerbal::getLiveVerbalTrickStatus,
                  queryPage.getLiveVerbalTrickStatus())
              .select(ItemDrawerLiveVerbal::getItemId)
              .list()
              .stream()
              .map(ItemDrawerLiveVerbal::getItemId)
              .collect(Collectors.toList());
      itemIdsForLiveStatus.addAll(itemIdList);

      List<Long> itemIdList2 =
          itemDrawerService
              .lambdaQuery()
              .eq(ItemDrawer::getLiveVerbalTrickStatus, queryPage.getLiveVerbalTrickStatus())
              .ne(ItemDrawer::getLiveVerbalTrick, "")
              .select(ItemDrawer::getItemId)
              .list()
              .stream()
              .map(ItemDrawer::getItemId)
              .collect(Collectors.toList());
      itemIdsForLiveStatus.addAll(itemIdList2);

      if (itemIdsForLiveStatus.isEmpty()) {
        return ResponseFactory.emptyPage();
      }
      itemIdSet.addAll(itemIdsForLiveStatus);
    }
    if (queryPage.getMaterialsStatus() != null) {
      filterStatus = true;

      final List<Long> itemIdsForMaterialsStatus =
          itemTrainingMaterialsService
              .lambdaQuery()
              .eq(ItemTrainingMaterials::getStatus, queryPage.getMaterialsStatus())
              .select(ItemTrainingMaterials::getItemId)
              .list()
              .stream()
              .map(ItemTrainingMaterials::getItemId)
              .collect(Collectors.toList());
      if (itemIdsForMaterialsStatus.isEmpty()) {
        return ResponseFactory.emptyPage();
      }

      if (!itemIdSet.isEmpty()) {
        itemIdSet = new HashSet<>(CollUtil.intersection(itemIdSet, itemIdsForMaterialsStatus));
      } else {
        itemIdSet.addAll(itemIdsForMaterialsStatus);
      }
    }
    if (NumberUtil.isPositive(queryPage.getStatus())) {
      filterStatus = true;

      final LambdaQueryChainWrapper<Item> queryWrapper = itemService.lambdaQuery();
      if (queryPage.getStatus() == 41) {
        queryWrapper
            .eq(Item::getLaunchStatus, ItemLaunchStatus.TO_BE_AUDITED.getValue())
            .in(
                Item::getAuditStatus,
                Arrays.asList(
                    ItemAuditStatus.WAIT_LEGAL_AUDIT.getValue(), ItemAuditStatus.NONE.getValue()));
      } else if (queryPage.getStatus() == 42) {
        queryWrapper
            .eq(Item::getLaunchStatus, ItemLaunchStatus.TO_BE_AUDITED.getValue())
            .eq(Item::getAuditStatus, ItemAuditStatus.WAIT_QC_AUDIT.getValue());
      } else {
        queryWrapper.eq(Item::getLaunchStatus, queryPage.getStatus());
      }
      final List<Long> itemIdsForLaunchStatus =
          queryWrapper.select(Entity::getId).list().stream()
              .map(Entity::getId)
              .collect(Collectors.toList());

      if (itemIdsForLaunchStatus.isEmpty()) {
        return ResponseFactory.emptyPage();
      }

      if (!itemIdSet.isEmpty()) {
        itemIdSet = new HashSet<>(CollUtil.intersection(itemIdSet, itemIdsForLaunchStatus));
      } else {
        itemIdSet.addAll(itemIdsForLaunchStatus);
      }
    }

    if (filterStatus) {
      if (CollUtil.isNotEmpty(itemIdSet)) {
        final List<Long> mergeItemIds =
            itemDrawerMergeBizService.mainItemIdsToMergeItemIds(itemIdSet);
        if (!mergeItemIds.isEmpty()) {
          queryPage.setItemIdsNoStatusFilter(new LinkedList<>(mergeItemIds));
        }
      } else {
        return ResponseFactory.emptyPage();
      }
    }

    final Set<DivisionLevelValueEnum> selectedScopes =
        new HashSet<>(
            DivisionLevelValueEnum.valueOf(queryPage.getCorpType(), queryPage.getBizType()));
    MPJLambdaWrapper<NewGoods> countWrapper = getWrapper(queryPage);
    countWrapper.selectFunc(BaseFuncEnum.DISTINCT, NewGoods::getItemId, "");
    Integer itemIdCount =
        BizDivisionContext.invoke(
            BizUnionTypeEnum.SPU,
            c -> c.setBizIdRef("t.item_id").setSelectedScopes(selectedScopes),
            () -> newGoodsMapper.selectCount(countWrapper));
    if (itemIdCount == 0) {
      return PageResponse.of(
          new LinkedList<>(), 0, queryPage.getPageSize(), queryPage.getPageIndex());
    }

    MPJLambdaWrapper<NewGoods> listWrapper = getWrapper(queryPage);
    listWrapper
        .selectFunc(BaseFuncEnum.DISTINCT, NewGoods::getItemId, "")
        .select(ItemLaunchPlan::getLaunchTime)
        .orderByDesc(ItemLaunchPlan::getLaunchTime)
        .last("LIMIT " + queryPage.getOffset() + ", " + queryPage.getPageSize());
    List<NewGoods> newGoodsList =
        BizDivisionContext.invoke(
            BizUnionTypeEnum.SPU,
            c -> c.setBizIdRef("t.item_id").setSelectedScopes(selectedScopes),
            () -> newGoodsMapper.selectJoinList(NewGoods.class, listWrapper));
    List<Long> itemIdList =
        newGoodsList.stream().map(NewGoods::getItemId).collect(Collectors.toList());

    // 查询目标页spu包含的所有新品商品信息
    NewGoodsQueryPage page = new NewGoodsQueryPage();
    BeanUtils.copyProperties(queryPage, page);
    page.setItemIds(itemIdList);
    page.setPageSize(10000);
    PageResponse<NewGoodsVo> newGoodsVoPageResponse = queryPage(page);

    if (newGoodsVoPageResponse.getTotalCount() == 0) {
      return PageResponse.of(
          new LinkedList<>(), 0, queryPage.getPageSize(), queryPage.getPageIndex());
    }

    Map<Long, List<NewGoodsVo>> itemIdNewGoodsMap =
        newGoodsVoPageResponse.getData().stream()
            .collect(Collectors.groupingBy(NewGoodsVo::getItemId));
    final List<Long> itemIds = new ArrayList<>(itemIdNewGoodsMap.keySet());
    final Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap =
        bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemIds);

    List<NewGoodsGroupVO> resultList = new LinkedList<>();
    itemIdList.forEach(
        itemId -> {
          List<NewGoodsVo> newGoodsVos = itemIdNewGoodsMap.get(itemId);
          if (newGoodsVos == null) {
            return;
          }
          List<NewGoodsVo> sortedList =
              newGoodsVos.stream()
                  .sorted(Comparator.comparing(NewGoodsVo::getLaunchDate).reversed())
                  .collect(Collectors.toList());

          if (CollUtil.isNotEmpty(sortedList)) {
            NewGoodsSpuVO newGoodsItemVO = NewGoodsSpuVO.ofNewGoodsVo(sortedList.get(0));
            newGoodsItemVO.setCorpBizType(
                corpBizTypeMap.getOrDefault(itemId, Collections.emptyList()));

          List<NewGoodsSkuVO> newGoodsSkuVOList = NewGoodsSkuVO.ofNewGoodsVos(sortedList);

          NewGoodsGroupVO vo = new NewGoodsGroupVO();
          vo.setSpu(newGoodsItemVO);
          vo.setSkuList(newGoodsSkuVOList);

            resultList.add(vo);
          }
        });

    return PageResponse.of(
        resultList, itemIdCount, queryPage.getPageSize(), queryPage.getPageIndex());
  }

  public PageResponse<NewGoodsVo> queryPage(NewGoodsQueryPage queryPage) {
    MPJLambdaWrapper<NewGoods> queryWrapper = setParam(queryPage);
    setSelect(queryWrapper);
    Page<NewGoods> pageReq = new Page<>(queryPage.getPageIndex(), queryPage.getPageSize());
    final IPage<NewGoodsVo> page =
        newGoodsMapper.selectJoinPage(pageReq, NewGoodsVo.class, queryWrapper);
    //        log.info("新品商品当前访问人数据为------------------------userId:{}", queryPage.getUserId());
    List<NewGoodsVo> newGoodsVos = page.getRecords();
    if (CollectionUtil.isEmpty(newGoodsVos)) {
      return PageResponse.of(
          newGoodsVos, (int) page.getTotal(), queryPage.getPageSize(), queryPage.getPageIndex());
    }
    final List<Long> brandIds =
        newGoodsVos.stream().map(NewGoodsVo::getBrandId).collect(Collectors.toList());
    final Map<Long, String> brandNamesMap = brandGateway.names(brandIds);
    final List<Long> categoryIds =
        newGoodsVos.stream().map(NewGoodsVo::getCategoryId).collect(Collectors.toList());
    final Map<Long, String> categoryNamesMap = categoryGateway.paths(categoryIds);
    final List<Long> staffIds =
        Stream.of(
                newGoodsVos.stream().map(NewGoodsVo::getBuyerId),
                newGoodsVos.stream().map(NewGoodsVo::getPrincipalId),
                newGoodsVos.stream().map(NewGoodsVo::getLegalId),
                newGoodsVos.stream().flatMap(v -> v.getQcIdList().stream()))
            .flatMap(Function.identity())
            .distinct()
            .collect(Collectors.toList());
    final Map<Long, DadStaffVO> dadStaffVOMap =
        staffIds.isEmpty()
            ? Collections.emptyMap()
            : staffService.getStaffList(staffIds).stream()
                .collect(Collectors.toMap(DadStaffVO::getUserId, Function.identity(), (a, b) -> a));
    final List<Long> itemIds =
        newGoodsVos.stream().map(NewGoodsVo::getItemId).distinct().collect(Collectors.toList());
    final SingleResponse<CategoryTree> categoryTreeSingleResponse =
        categoryBizService.categoryTree();
    ErrorChecker.checkAndThrowIfError(categoryTreeSingleResponse, ErrorCode.SYS_ERROR, "商品类目查询异常");
    final CategoryTree categoryTree = categoryTreeSingleResponse.getData();

    // 直播话术查询处理
    List<Long> itemIdList =
        newGoodsVos.stream().map(NewGoodsVo::getItemId).collect(Collectors.toList());
    final List<ItemDrawerLiveVerbal> liveVerbals =
        iItemDrawerLiveVerbalService
            .lambdaQuery()
            .in(ItemDrawerLiveVerbal::getItemId, itemIdList)
            .list();
    Map<Long, List<LiveVerbalTrickVO>> liveVerbalTrickListMap =
        liveVerbals.stream()
            .collect(
                Collectors.groupingBy(
                    ItemDrawerLiveVerbal::getItemId,
                    Collectors.collectingAndThen(
                        Collectors.toList(),
                        ll ->
                            ll.stream()
                                .map(
                                    val -> {
                                      LiveVerbalTrickVO oo = new LiveVerbalTrickVO();
                                      oo.setId(val.getId());
                                      final boolean hasOldData =
                                          newGoodsVos.stream()
                                              .anyMatch(
                                                  v ->
                                                      v.getItemId().equals(val.getItemId())
                                                          && StringUtil.isNotBlank(
                                                              v.getLiveVerbalTrick()));
                                      oo.setName(
                                          iItemDrawerLiveVerbalService.getName(
                                              hasOldData, val, liveVerbals));
                                      oo.setLiveVerbalTrickStatus(
                                          val.getLiveVerbalTrickStatus().getValue());
                                      return oo;
                                    })
                                .collect(Collectors.toList()))));

    // 查询商品主图
    Map<Long, String> itemImageMap = itemImageGateway.batchGetItemMainImgUrls(itemIds);

    // 查询P系统商品数据
    final Map<Long, PartnerItemResp> itemIdToPartnerItemRespMap =
        itemBizService.queryPartnerItemBatch(itemIds);

    // 查询商品标签
    final Map<Long, List<String>> tagMap = itemTagBizService.listByItemIds(itemIds);

    // 查询合并商品信息
    final Map<Long, Long> mainItemIdsMap = itemDrawerMergeBizService.getMainItemIdBatch(itemIds);
    final Collection<Long> mainItemIds = mainItemIdsMap.values();
    List<NewGoodsVo> mainItemNewGoodsList = Collections.emptyList();
    if (!mainItemIds.isEmpty() && queryPage.isMergeLogic()) {
      final NewGoodsQueryPage mainItemQuery = new NewGoodsQueryPage();
      mainItemQuery.setItemIds(new ArrayList<>(mainItemIds));
      mainItemQuery.setShowAll(true);
      mainItemQuery.setMergeLogic(false);
      final PageResponse<NewGoodsVo> mainItemQueryResponse = queryPage(mainItemQuery);
      if (mainItemQueryResponse.isSuccess()) {
        mainItemNewGoodsList = mainItemQueryResponse.getData();
      }
    }

    final Map<Long, List<CorpBizTypeDTO>> corpBizTypeMap =
        bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemIds);
    for (NewGoodsVo newGoodsVo : newGoodsVos) {
      final Long itemId = newGoodsVo.getItemId();
      // 采购负责人信息
      newGoodsVo.setBuyer(dadStaffVOMap.get(newGoodsVo.getBuyerId()));
      // 产品负责人信息
      newGoodsVo.setPrincipal(dadStaffVOMap.get(newGoodsVo.getPrincipalId()));
      // 法务负责人信息
      newGoodsVo.setLegal(dadStaffVOMap.get(newGoodsVo.getLegalId()));
      // QC负责人信息
      final List<DadStaffVO> qcStaffVoList =
          newGoodsVo.getQcIdList().stream()
              .map(dadStaffVOMap::get)
              .filter(Objects::nonNull)
              .collect(Collectors.toList());

      // 设置商品主图
      newGoodsVo.setImage(itemImageMap.getOrDefault(newGoodsVo.getItemId(), StrUtil.EMPTY));

      newGoodsVo.setQcs(qcStaffVoList);
      newGoodsVo.setCategory(categoryNamesMap.getOrDefault(newGoodsVo.getCategoryId(), ""));
      newGoodsVo.setBrand(brandNamesMap.getOrDefault(newGoodsVo.getBrandId(), ""));
      final Optional<PartnerItemResp> partnerItemRespOptional =
          Optional.ofNullable(itemIdToPartnerItemRespMap.get(newGoodsVo.getItemId()));
      newGoodsVo.setIsBlacklist(
          partnerItemRespOptional.map(PartnerItemResp::getIsBlacklist).orElse(0));
      newGoodsVo.setIsDadCheck(partnerItemRespOptional.map(PartnerItemResp::isDadCheck).orElse(0));

      // 类目拓展属性键名列表
      newGoodsVo.setPropKeys(
          categoryTree
              .getByCategoryId(newGoodsVo.getCategoryId())
              .map(CategoryTreeNode::getAllProps)
              .orElseGet(Collections::emptyList));

      // 直播话术
      List<LiveVerbalTrickVO> liveVerbalTrickVos =
          liveVerbalTrickListMap.getOrDefault(newGoodsVo.getItemId(), new LinkedList<>());
      boolean newVersionFlag = CollUtil.isNotEmpty(liveVerbalTrickVos);
      if (Objects.nonNull(newGoodsVo.getLiveVerbalTrickStatus())
          && StringUtils.isNotBlank(newGoodsVo.getLiveVerbalTrick())) {
        LiveVerbalTrickVO oo = new LiveVerbalTrickVO();
        oo.setName("直播话术0");
        oo.setLiveVerbalTrickStatus(newGoodsVo.getLiveVerbalTrickStatus());
        oo.setId(0L);
        liveVerbalTrickVos.add(oo);
      }
      if (newVersionFlag) {
        newGoodsVo.setNewLiveVerbalTrickFlag(1);
      } else {
        newGoodsVo.setNewLiveVerbalTrickFlag(0);
      }
      newGoodsVo.setLiveVerbalTrickVos(liveVerbalTrickVos);
      if (!liveVerbalTrickVos.isEmpty()) {
        newGoodsVo.setLiveVerbalTrickStatus(liveVerbalTrickVos.get(0).getLiveVerbalTrickStatus());
      }
      newGoodsVo.setTags(tagMap.getOrDefault(newGoodsVo.getItemId(), new ArrayList<>()));

      final Long mainItemId = Optional.ofNullable(mainItemIdsMap.get(itemId)).orElse(0L);
      if (mainItemId > 0) {
        mainItemNewGoodsList.stream()
            .filter(v -> Objects.equals(v.getItemId(), mainItemId))
            .findFirst()
            .ifPresent(
                ng -> {
                  newGoodsVo.setStatus(ng.getStatus());
                  newGoodsVo.setLiveVerbalTrickStatus(ng.getLiveVerbalTrickStatus());
                  newGoodsVo.setLiveVerbalTrickVos(ng.getLiveVerbalTrickVos());
                  newGoodsVo.setMaterialsStatus(ng.getMaterialsStatus());
                  newGoodsVo.setStandardName(ng.getStandardName());
                  newGoodsVo.setQcs(ng.getQcs());
                  newGoodsVo.setQcIds(ng.getQcIds());
                  newGoodsVo.setLegal(ng.getLegal());
                  newGoodsVo.setLegalId(ng.getLegalId());
                });
      }

      final List<CorpBizTypeDTO> corpBizType =
          corpBizTypeMap.getOrDefault(itemId, Collections.emptyList());
      newGoodsVo.setCorpBizType(corpBizType);
    }

    return PageResponse.of(
        newGoodsVos, (int) page.getTotal(), queryPage.getPageSize(), queryPage.getPageIndex());
  }

  @Override
  public Response update(NewGoodsCmd cmd) {
    String updateType = cmd.getUpdateType();

    if ("spu".equals(updateType)) {
      Long itemId = cmd.getItemId();
      Assert.notNull(itemId, "spu更新，商品Id不得为空");
      List<String> saveSpuCmdFileNameList =
          BeanUtil.getBeanDesc(NewGoodsSpuSaveCmd.class).getProps().stream()
              .map(PropDesc::getFieldName)
              .collect(Collectors.toList());

      final List<NewGoods> list =
          iNewGoodsService.lambdaQuery().eq(NewGoods::getItemId, cmd.getItemId()).select().list();
      for (int i = 0; i < list.size(); i++) {
        final NewGoods newGoods = list.get(i);
        cmd.setId(newGoods.getId());
        final Response response = update0(cmd, saveSpuCmdFileNameList, true, i == 0);
        if (!response.isSuccess()) {
          return response;
        }
      }
      checkUpdateItemLaunchStatus(itemId);
      return Response.buildSuccess();

    } else if ("sku".equals(updateType)) {

      List<String> saveSpuCmdFileNameList =
          BeanUtil.getBeanDesc(NewGoodsSkuSaveCmd.class).getProps().stream()
              .map(PropDesc::getFieldName)
              .collect(Collectors.toList());
      return update0(cmd, saveSpuCmdFileNameList, false, true);
    }

    throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "更新类型参数非法");
  }

  @Transactional(rollbackFor = Exception.class)
  public Response update0(
      NewGoodsCmd cmd, List<String> updateFiledNameList, Boolean isUpdateSpu, Boolean logChange) {
    Assert.notNull(cmd.getId(), "newGoodsId入参不得为空");
    final SingleResponse<LockResult> lockResp = lock(cmd.getId());
    if (!lockResp.isSuccess() || !lockResp.getData().getLockSuccess()) {
      return lockResp;
    }
    log.debug(
        "编辑新品商品 newGoodsId:{} cmd:{} updateFiledNameList:{} isUpdateSpu:{} logChange:{} user:{} {}",
        cmd.getId(),
        cmd,
        updateFiledNameList,
        isUpdateSpu,
        logChange,
        UserContext.getUserId(),
        UserContext.getNickName());
    try {
      final Long userId = UserContext.getUserId();

      NewGoods newGoods = iNewGoodsService.getById(cmd.getId());
      if (Objects.isNull(newGoods)) {
        throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "数据不存在,操作失败");
      }
      NewGoodsCmd newGoodsCmd = NewGoodsTransMapper.INSTANCE.doToCmd(newGoods);
      checkParamCmd(cmd, newGoodsCmd, updateFiledNameList);

      // 编辑字段范围内的字段需进行比较
      for (PropDesc prop : BeanUtil.getBeanDesc(NewGoodsCmd.class).getProps()) {
        final String fieldName = prop.getFieldName();
        if (!"id".equals(fieldName) && !updateFiledNameList.contains(fieldName)) {
          setNull(newGoodsCmd, cmd, fieldName);
        }
      }

      DiffUtil.ObjListDiff objListDiff =
          DiffUtil.getInstance()
              .diffObjList(
                  Collections.singletonList(newGoodsCmd),
                  Collections.singletonList(cmd),
                  NewGoodsCmd.class,
                  true);
      Set<DiffUtil.ChangePropertyObj> changes = objListDiff.getChangeMap().get(cmd.getId());
      if (changes == null) {
        changes = new HashSet<>();
      }

      final Long itemId = newGoods.getItemId();

      if (updateFiledNameList.contains("principalId")) {

        // 如果产品负责人发生修改
        if (NumberUtil.isPositive(cmd.getPrincipalId())
            && !Objects.equals(cmd.getPrincipalId(), newGoods.getPrincipalId())) {
          // 这个字段的权限在 checkParamCmd 方法中已经校验过了，所以这边不做校验
          final StaffInfo oldPrincipal = userGateway.queryStaffInfoById(newGoods.getPrincipalId());
          final StaffInfo newPrincipal = userGateway.queryStaffInfoById(cmd.getPrincipalId());
          if (newPrincipal == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "指定产品负责人不存在！");
          }
          updateItemPrincipalId(newGoods.getItemId(), cmd.getPrincipalId());

          log.info(
              "新品商品编辑:修改产品负责人 商品ID:{} 原负责人:{} {} 新负责人:{} {}",
              itemId,
              oldPrincipal != null ? oldPrincipal.getUserId() : 0L,
              oldPrincipal != null ? oldPrincipal.getNickname() : "",
              newPrincipal.getUserId(),
              newPrincipal.getNickname());

          final ChangePropertyObj planChangeLog =
              new ChangePropertyObj(
                  "产品负责人",
                  oldPrincipal != null ? oldPrincipal.getNickname() : "",
                  newPrincipal.getNickname());
          changes.add(planChangeLog);

          // 产品负责人发生修改，发送触发事件消息
          //                    MsgEvent event =
          // MsgEvent.buildByPrincipalChange(cmd.getPrincipalId(), UserContext.getUserId()
          //                            , ListUtil.of(newGoods.getItemId()));
          //                    EventBusUtil.post(event, true);
          itemDrawerAppService.noticeItemLaunchModifyPrincipals(
              UserContext.getNickName(),
              newPrincipal.getUserId(),
              newGoods.getName(),
              newGoods.getItemId());

          log.info("新品商品编辑:修改产品负责人触发企微通知 商品ID:{}", itemId);
        }
      }

      // 如果上新计划发生修改
      if (NumberUtil.isPositive(cmd.getPlanId())) {
        final ItemLaunchPlan plan = itemLaunchPlanService.getPlanByItemId(itemId);
        if (Objects.nonNull(plan) && !Objects.equals(plan.getId(), cmd.getPlanId())) {
          if (!UserContext.hasPermission(GlobalConstant.NEW_GOODS_EDIT_PRODUCT_FIELDS)) {
            log.debug(
                "新品商品编辑:修改上新计划无权限 userId:{} 商品ID:{} 原计划ID:{} 新计划ID:{}",
                UserContext.getUserId(),
                itemId,
                plan.getId(),
                cmd.getPlanId());

            throw ExceptionPlusFactory.bizException(
                ErrorCode.VERIFY_PARAM.getCode(),
                StringUtil.format("您缺少编辑 ''{}'' 字段的权限，请联系系统管理员申请权限", "预计上新时间"));
          }
          final ItemLaunchPlan newPlan = itemLaunchPlanService.getById(cmd.getPlanId());
          if (newPlan != null) {
            // 修改上新计划，同步刷新新品活动周期
            setActivePeriodByLaunchTime(newGoods, newPlan.getLaunchTime());
            itemLaunchPlanService.syncItemLaunchPlan(itemId, cmd.getPlanId());

            log.info(
                "新品商品编辑:修改上新计划 商品ID:{} 原计划ID:{} 新计划ID:{}", itemId, plan.getId(), cmd.getPlanId());

            final ChangePropertyObj planChangeLog =
                new ChangePropertyObj(
                    "商品上新计划",
                    StringUtil.format(
                        "{}({}}", DateUtil.formatDate(plan.getLaunchTime()), plan.getPlanName()),
                    StringUtil.format(
                        "{}({}}",
                        DateUtil.formatDate(newPlan.getLaunchTime()),
                        newPlan.getPlanName()));
            changes.add(planChangeLog);
          }
        }
      }

      final BigDecimal dailyPriceToUpdate = cmd.getDailyPrice();
      if (Objects.nonNull(dailyPriceToUpdate)) {
        // 如果产品日销价发生修改，刷新此SKU的销售价格
        if (newGoods.getDailyPrice().compareTo(dailyPriceToUpdate) != 0) {
          final Optional<ItemSku> itemSkuOptional =
              iItemSkuService.getByMixCode(newGoods.getSkuCode());
          itemSkuOptional.ifPresent(
              itemSku -> {
                final ItemSku itemSkuUpdateObj = new ItemSku();
                itemSkuUpdateObj.setId(itemSku.getId());
                itemSkuUpdateObj.setSalePrice(dailyPriceToUpdate);
                iItemSkuService.updateById(itemSkuUpdateObj);
                operateLogGateway.addOperatorLog(
                    UserContext.getUserId(),
                    OperateLogTarget.ITEM,
                    itemId,
                    String.format(
                        "同步新品商品修改，规格[%s]修改[产品日销价]从[%s]改为[%s]",
                        newGoods.getSkuCode(), itemSku.getSalePrice(), dailyPriceToUpdate),
                    null);
              });
        }
      }

      NewGoods newGoodsUpdate = NewGoodsTransMapper.INSTANCE.cmdToDo(cmd);
      BigDecimal singleBuyPrice = getSingleBuyPrice(newGoodsUpdate.getDailyPrice());
      if (Objects.nonNull(singleBuyPrice)) {
        newGoodsUpdate.setSingleBuyPrice(singleBuyPrice);
      }
      final boolean update = iNewGoodsService.updateById(newGoodsUpdate);
      if (!update) {
        return Response.buildSuccess();
      }

      // 检查SPU关联的所有新品商品是否已经填写完整，填写完成更新上新状态
      if (!isUpdateSpu) {
        checkUpdateItemLaunchStatus(itemId);
      }

      // 如果商品上新状态为已上架，那么数据有更新时需要通知
      final ItemLaunchStatus itemLaunchStatus = itemService.getItemLaunchStatus(itemId);
      // 2022-11-25【徵乌】：现编辑SPU字段不触发通知，无变更不触发通知
      if (itemLaunchStatus == ItemLaunchStatus.HAS_BE_RELEASED
          && !isUpdateSpu
          && !changes.isEmpty()) {
        log.info("新品商品编辑:商品上新状态已上架，数据更新时触发消息提醒 商品ID:{}", itemId);
        //                EventBusUtil.post(MsgEvent
        //                                .of(Collections.singletonList(itemId),
        // MsgEventType.ONE_FIELD_CHANGE),
        //                        true);
        noticeItemLaunchFieldEditAfterListed(itemId);
      }

      if (!changes.isEmpty() && logChange) {
        logNewGoodsOperate(isUpdateSpu, userId, changes, itemId, newGoodsUpdate, newGoods);

        boolean hadOrToRelease =
            itemLaunchStatus == ItemLaunchStatus.HAS_BE_RELEASED
                || itemLaunchStatus == ItemLaunchStatus.TO_BE_RELEASED;
        // 2023-07-14【七喜】，针对待上架/已上架的商品,如果发生变更。发送消息
        if (hadOrToRelease) {
          String changeProperties =
              changes.stream().map(ChangePropertyObj::getProperty).collect(Collectors.joining(","));
          EventBusUtil.post(
              MsgEvent.buildShelfItemChange(
                  Collections.singletonList(itemId), UserContext.getUserId(), changeProperties),
              true);
        }

        SpringUtil.publishEvent(new SaveEvent<>(this, newGoods, false).setOperatorId(userId));
      }

      // 2022-11-28【七喜】,将新品商品的活动时间同步到上新计划
      Optional<ItemLaunchPlanItemRef> itemLaunchPlanItemRef =
          iItemLaunchPlanItemRefService
              .lambdaQuery()
              .eq(ItemLaunchPlanItemRef::getItemId, itemId)
              .orderByDesc(ItemLaunchPlanItemRef::getId)
              .oneOpt();

      itemLaunchPlanItemRef.ifPresent(
          val -> {
            boolean update1 =
                Objects.nonNull(cmd.getActivePeriodStart())
                    && Objects.nonNull(cmd.getActivePeriodEnd());
            if (update1) {
              iItemLaunchPlanItemRefService
                  .lambdaUpdate()
                  .set(ItemLaunchPlanItemRef::getActivePeriodStart, cmd.getActivePeriodStart())
                  .set(ItemLaunchPlanItemRef::getActivePeriodEnd, cmd.getActivePeriodEnd())
                  .eq(ItemLaunchPlanItemRef::getItemId, itemId)
                  .update();
            }
          });

      return Response.buildSuccess();
    } finally {
      unlock(cmd.getId());
    }
  }

  private void logNewGoodsOperate(
      Boolean isUpdateSpu,
      Long userId,
      Set<ChangePropertyObj> changes,
      Long itemId,
      NewGoods newGoodsUpdate,
      NewGoods newGoods) {
    final Item item = itemService.getById(itemId);
    if (isUpdateSpu) {
      operateLogGateway.addOperatorLog(
          userId,
          OperateLogTarget.NEW_GOODS_SPU,
          itemId,
          "【修改新品商品】SPU维度字段发生变更："
              + changes.stream()
                  .map(ChangePropertyObj::getProperty)
                  .collect(Collectors.joining(",")),
          changes);
    } else {
      operateLogGateway.addOperatorLog(
          userId,
          OperateLogTarget.NEW_GOODS,
          newGoods.getId(),
          String.format(
              "【修改新品商品】SKU\"%s\"字段发生变更：%s",
              newGoods.getSkuCode(),
              changes.stream()
                  .map(ChangePropertyObj::getProperty)
                  .collect(Collectors.joining(","))),
          changes);
    }
    final List<String> propertiesOfPrice =
        Arrays.asList(
            "产品划线价",
            "产品日销价",
            "日常活动机制",
            "S级/新品活动价",
            "新品活动开始周期",
            "新品活动结束周期",
            "新品活动周期是否长期有效",
            "S级/新品活动机制",
            "S级一口价/直播价",
            "S级一口价活动机制/直播机制");
    final Set<ChangePropertyObj> priceChanges =
        changes.stream()
            .filter(change -> propertiesOfPrice.contains(change.getProperty()))
            .collect(Collectors.toSet());
    if (!priceChanges.isEmpty()) {
      final Optional<ItemLaunchStatus> launchStatus =
          IEnum.getEnumOptByValue(ItemLaunchStatus.class, item.getLaunchStatus());
      final StringJoiner priceLog = new StringJoiner("，");
      for (ChangePropertyObj priceChange : priceChanges) {
        final String property = priceChange.getProperty();
        if ("新品活动开始周期".equals(property) || "新品活动结束周期".equals(property)) {
          priceLog.add(
              String.format(
                  "%s 从 “%s” 修改为 “%s”",
                  property,
                  Optional.ofNullable(priceChange.getOldVal())
                      .filter(v -> (Long) v > 0)
                      .map(v -> DateUtil.format((Long) v))
                      .orElse(""),
                  Optional.ofNullable(priceChange.getNewVal())
                      .filter(v -> (Long) v > 0)
                      .map(v -> DateUtil.format((Long) v))
                      .orElse("")));
        } else if ("新品活动周期是否长期有效".equals(property)) {
          priceLog.add(
              String.format(
                  "%s 从 “%s” 修改为 “%s”",
                  property,
                  Boolean.TRUE.equals(priceChange.getOldVal()) ? "是" : "否",
                  Boolean.TRUE.equals(priceChange.getNewVal()) ? "是" : "否"));
        } else {
          priceLog.add(
              String.format(
                  "%s 从 “%s” 修改为 “%s”",
                  property, priceChange.getOldVal(), priceChange.getNewVal()));
        }
      }
      operateLogGateway.addOperatorLog(
          userId,
          OperateLogTarget.NEW_GOODS_PRICE,
          itemId,
          String.format(
              "修改了【%s】状态下的价格，【%s】的%s",
              launchStatus.map(ItemLaunchStatus::getDesc).orElse("?"),
              newGoods.getSkuCode(),
              priceLog),
          priceChanges);
    }
  }

  private void noticeItemLaunchFieldEditAfterListed(Long itemId) {
    final List<Long> skuIdList = itemSkuGateway.getSkuIdList(itemId);
    final NewGoodsPrincipalsInfo newGoodsPrincipalsInfo =
        getNewGoodsPrincipalsInfo(itemId).getData();
    final HashMap<String, Object> variables = new HashMap<>();
    variables.put("商品数量", 1);
    variables.put("SKU数量", skuIdList.size());
    variables.put(
        "采购",
        newGoodsPrincipalsInfo.getBuyerUsers().stream()
            .map(StaffBrief::getQwUserId)
            .collect(Collectors.joining(",")));
    variables.put(
        "QC",
        newGoodsPrincipalsInfo.getQcUsers().stream()
            .map(StaffBrief::getQwUserId)
            .collect(Collectors.joining(",")));
    variables.put(
        "产品负责人",
        newGoodsPrincipalsInfo.getPrincipalUsers().stream()
            .map(StaffBrief::getQwUserId)
            .collect(Collectors.joining(",")));
    variables.put("平台运营", String.join(",", recipientConfig.getPlatformList()));
    variables.put("itemIds", itemId);
    QyMsgSendUtils.send(
        RemindType.TO_DO_REMINDER, MsgTemplateCode.ITEM_LAUNCH_FIELD_EDIT_AFTER_LISTED, variables);
  }

  /**
   * 到手价计算规则（产品日销价为空时，此价格为空） 1、产品日销价*1.25 ≥ 200时：单买到手价 =
   * 产品日销价-（产品日销价/200）*40；即计算日销价在200-40中可能享受到满减后的到手价 2、以上情况小于200时，单买到手价=产品日销价
   *
   * @param dailyPrice
   * @return
   */
  public BigDecimal getSingleBuyPrice(BigDecimal dailyPrice) {
    if (Objects.nonNull(dailyPrice)) {
      if (dailyPrice.compareTo(new BigDecimal(160)) < 0) {
        return dailyPrice;
      } else {
        return dailyPrice.multiply(new BigDecimal("0.8")).setScale(4, RoundingMode.HALF_UP);
      }
    }
    return null;
  }

  private boolean updateItemPrincipalId(Long itemId, Long principalId) {
    return newGoodsService
        .lambdaUpdate()
        .eq(NewGoods::getItemId, itemId)
        .set(NewGoods::getPrincipalId, principalId)
        .update();
  }

  private void checkUpdateItemLaunchStatus(Long itemId) {
    final Item item = itemGateway.getItem(itemId);

    Assert.notNull(item, "商品ID非法。itemID:" + itemId);

    if (Objects.equals(item.getLaunchStatus(), ItemLaunchStatus.TO_BE_IMPROVED.getValue())) {
      boolean infoCompleted = true;
      final List<NewGoods> newGoods =
          iNewGoodsService.lambdaQuery().eq(NewGoods::getItemId, itemId).list();
      L1:
      for (NewGoods newGood : newGoods) {
        for (Entry<String, FieldMeta> metaEntry : fieldMetas.getFieldMetas().entrySet()) {
          final FieldMeta fieldMeta = metaEntry.getValue();
          if (fieldMeta.isNotCheck()) {
            continue;
          }
          final Object fieldValue = BeanUtil.getFieldValue(newGood, fieldMeta.getProp());
          if (ObjectUtil.isEmpty(fieldValue, fieldMeta.isEmptyCheckLenient())
              && fieldMeta.isRequired()) {
            log.debug("新品商品编辑/检查是否填写完整:{} 必填字段:{} 未填写", newGood.getId(), fieldMeta.getProp());
            infoCompleted = false;
            break L1;
          }
        }
      }
      for (NewGoods newGood : newGoods) {
        if (newGood.getIsLongTerm() == null
            && (newGood.getActivePeriodStart() == null || newGood.getActivePeriodEnd() == null)) {
          log.info("新品商品编辑/检查是否填写完整:{} 必填字段:{} 未填写", newGood.getId(), "新品活动周期");
          infoCompleted = false;
          break;
        }
      }
      if (infoCompleted) {
          log.info("新品商品编辑/检查是否填写完整:新品商品信息完善，修改商品上新状态到待上传资料 商品ID:{}", itemId);
          itemBizService.updateLaunchStatus(itemId, ItemLaunchStatus.TO_BE_UPLOAD_FILE);
      }
    }
  }

  @Override
  @Transactional(rollbackFor = Throwable.class)
  public Response delete(Long id) {

    final LambdaQueryWrapper<NewGoods> query = Wrappers.lambdaQuery();
    query.eq(NewGoods::getItemId, id);
    iNewGoodsService.remove(query);

    // 移除上新计划中的关联记录
    itemLaunchPlanService.deleteRefByItemId(id);

    // 终止掉对应商品的上新流程
    itemDrawerBizService.processTerminateByItemId(id);

    // 删除商品库抽屉信息
    itemDrawerBizService.deleteItemDrawer(id);

    // 删除商品审批意见
    itemApprovalAdviceBizService.deleteAllByItemId(id);

    // 删除商品上新统计记录
    itemLaunchStatsService.delStats(id);

    return Response.buildSuccess();
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public void exportExcel(NewGoodsQueryPage queryPage) {
    ExportTask exportTask = new ExportTask();
    exportTask.setStatus(ExportTaskStatus.RUNNING);
    exportTask.setName("新品商品导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
    exportTask.setType(ExportTaskType.NEW_GOODS);
    final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
    exportTask.setId(taskId);

    log.info("新品商品导出 taskId={} name={} params={}", taskId, exportTask.getName(), queryPage);

    final Runnable runnable =
        () -> {
          final String localSavePath = String.format("/tmp/%s.xlsx", exportTask.getName());
          try (final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
            queryPage.setPageSize(999999);
            final List<List<String>> head = exportHead();
            List<List<String>> distinctHead = head.stream().distinct().collect(Collectors.toList());
            log.info("正在进行新品商品导出，当前导出列：" + distinctHead);
            final List<List<String>> exportData = getExportData(queryPage);
            EasyExcel.write(byteArrayOutputStream)
                .useDefaultStyle(false)
                .head(distinctHead)
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                .sheet("工作表1")
                .doWrite(exportData);

            final byte[] bytes = byteArrayOutputStream.toByteArray();
            if (refreshConfig.getExportFileSaveLocal()) {
              FileUtil.writeFromStream(new ByteArrayInputStream(bytes), localSavePath);
            }
            UploadFileAction action =
                UploadFileAction.ofInputStream(
                    new ByteArrayInputStream(bytes), exportTask.getName() + ".xlsx");
            final String downloadUrl = fileGateway.uploadFile(action).getUrl();
            log.info("新品商品导出成功，下载链接：{}", downloadUrl);

            exportTask.setStatus(ExportTaskStatus.SUCCESS);
            exportTask.setDownloadUrl(downloadUrl);
          } catch (Exception e) {
            log.error(
                "导出新品商品异常 taskId={} name={} params={}", taskId, exportTask.getName(), queryPage, e);
            exportTask.setStatus(ExportTaskStatus.FAIL);
            exportTask.setError(StringUtil.truncateByMaxBytes(e.getMessage(), 20000));
          } finally {
            try {
              exportTaskGateway.saveOrUpdateExportTask(exportTask);
            } catch (Exception e) {
              log.error("导出新品商品，更新导出记录失败 task:{}", exportTask);
            }
          }
        };
    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(RunnableWrapper.of(runnable));
  }

  private List<List<String>> getExportData(NewGoodsQueryPage queryPage) {
    PageResponse<NewGoodsGroupVO> response = queryPageGroupBySpu(queryPage);
    ResponseAssert.assertJust(response);

    // 批量查询平台链接数据
    List<ItemDrawerPlatformLink> itemDrawerPlatformLinks = batchQueryPlatformLinks(response.getData());

    final List<NewGoodsSheet> newGoodsSheets =
        NewGoodsTransMapper.INSTANCE.listVoToSheets(response.getData());

    // 设置平台链接数据
    setPlatformLinksToSheets(newGoodsSheets, response.getData(), itemDrawerPlatformLinks);

    final List<String> hiddenFields = getHiddenFields();
    final List<List<String>> data = new ArrayList<>();
    final Field[] fields = ReflectUtil.getFields(NewGoodsSheet.class);
    for (NewGoodsSheet newGoodsSheet : newGoodsSheets) {
      final ArrayList<String> row = new ArrayList<>();
      for (Field field : fields) {
        final String name = field.getName();
        if ((field.getModifiers() & Modifier.STATIC) > 0) {
          continue;
        }
        final PropertyAlias propertyAlias = field.getAnnotation(PropertyAlias.class);
        if (hiddenFields.contains(name)
            || propertyAlias != null && hiddenFields.contains(propertyAlias.value())) {
          continue;
        }
        final ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
        if (annotation == null) {
          continue;
        }
        final ExcelIgnore excelIgnore = field.getAnnotation(ExcelIgnore.class);
        if (excelIgnore != null) {
          continue;
        }
        Object fieldValue = BeanUtil.getFieldValue(newGoodsSheet, name);
        if ("costPrice".equals(name) && !queryPage.isHasPricePerm()) {
          fieldValue = "/";
        }
        row.add(fieldValue != null ? fieldValue.toString() : "");
      }
      data.add(row);
    }
    return data;
  }

  private List<List<String>> exportHead() {
    final Field[] fields = ReflectUtil.getFields(NewGoodsSheet.class);
    final Builder<List<String>> headBuilder = ImmutableList.builder();
    final List<String> hiddenFields = getHiddenFields();
    for (Field field : fields) {
      final String name = field.getName();
      if ((field.getModifiers() & Modifier.STATIC) > 0) {
        continue;
      }
      final PropertyAlias propertyAlias = field.getAnnotation(PropertyAlias.class);
      if (hiddenFields.contains(name)
          || propertyAlias != null && hiddenFields.contains(propertyAlias.value())) {
        continue;
      }
      final ExcelProperty annotation = field.getAnnotation(ExcelProperty.class);
      if (annotation == null) {
        continue;
      }
      final ExcelIgnore excelIgnore = field.getAnnotation(ExcelIgnore.class);
      if (excelIgnore != null) {
        continue;
      }
      headBuilder.add(ImmutableList.of(annotation.value()[0]));
    }

    return headBuilder.build();
  }

  private String convertFileName(String filename) {
    filename = urlDecode(filename);
    //        final int indexOfDash = filename.indexOf("-");
    //        if (indexOfDash > 0 && indexOfDash != filename.lastIndexOf("-") &&
    // filename.indexOf(".") > 0) {
    //            int lastSplit = filename.lastIndexOf("-");
    //            String fileName1 = filename.substring(0, lastSplit);
    //            String fileName2 = filename.substring(filename.lastIndexOf("."));
    //            filename = fileName1 + fileName2;
    //        }
    return filename;
  }

  private String checkRepeatName(Map<String, Integer> map, String resourceFileName) {
    String fileName = convertFileName(resourceFileName);
    Integer integer = map.get(fileName);
    if (Objects.isNull(integer)) {
      map.put(fileName, 1);
    } else {
      String[] split = fileName.split("\\.");
      fileName = split[0] + "(" + (integer + 1) + ")" + "." + split[1];
      map.put(fileName, integer + 1);
    }
    return fileName;
  }

  @Override
  public long exportImages(NewGoodsQueryPage newGoodsQueryPage) {
    ExportTask exportTask = new ExportTask();
    exportTask.setStatus(ExportTaskStatus.RUNNING);
    exportTask.setName("新品商品图片导出-" + DateUtil.formatNow(DatePattern.PURE_DATETIME_PATTERN));
    exportTask.setType(ExportTaskType.NEW_GOODS);
    final Long taskId = exportTaskGateway.saveOrUpdateExportTask(exportTask);
    exportTask.setId(taskId);

    log.info(
        "新品商品图片导出 taskId={} name={} params={}", taskId, exportTask.getName(), newGoodsQueryPage);

    String rootDir = "/tmp/新品商品图片导出#" + taskId;
    final Runnable runnable =
        () -> {
          File zip = null;
          try {
            FileUtil.mkdir(rootDir);
            final List<GoodsImages> goodsImages = queryImages(newGoodsQueryPage);
            if (CollUtil.isNotEmpty(goodsImages)) {
              for (GoodsImages goodsImage : goodsImages) {

                final List<ImageBO> images = goodsImage.getImages();
                Map<String, Integer> nameRepeatCheckMap1 = new HashMap<>();
                if (CollUtil.isNotEmpty(images)) {
                  images.stream()
                      .sorted(Comparator.comparing(ImageBO::getSort))
                      .forEach(
                          val -> {
                            final String image = val.getUrl();
                            String fileName =
                                checkRepeatName(nameRepeatCheckMap1, val.getFileName());
                            HttpUtil.downloadFile(
                                image,
                                rootDir
                                    + "/"
                                    + goodsImage.getStandardName()
                                    + goodsImage.getItemId()
                                    + "/商品主图"
                                    + "/"
                                    + fileName);
                          });
                }

                final List<ImageBO> detailImages = goodsImage.getDetailImages();
                Map<String, Integer> nameRepeatCheckMap2 = new HashMap<>();
                if (CollUtil.isNotEmpty(detailImages)) {
                  detailImages.stream()
                      .sorted(Comparator.comparing(ImageBO::getSort))
                      .forEach(
                          val -> {
                            final String image = val.getUrl();
                            String fileName =
                                checkRepeatName(nameRepeatCheckMap2, val.getFileName());
                            HttpUtil.downloadFile(
                                image,
                                rootDir
                                    + "/"
                                    + goodsImage.getStandardName()
                                    + goodsImage.getItemId()
                                    + "/详情页图"
                                    + "/"
                                    + fileName);
                          });
                }

                final List<ImageBO> skuImages =
                    CollUtil.isNotEmpty(goodsImage.getAttrImages())
                        ? goodsImage.getAttrImages()
                        : goodsImage.getSkuImages();
                Map<String, Integer> nameRepeatCheckMap3 = new HashMap<>();
                if (CollUtil.isNotEmpty(skuImages)) {
                  skuImages.stream()
                      .sorted(Comparator.comparing(ImageBO::getSort))
                      .forEach(
                          val -> {
                            final String image = val.getUrl();
                            String fileName =
                                checkRepeatName(nameRepeatCheckMap3, val.getFileName());
                            HttpUtil.downloadFile(
                                image,
                                rootDir
                                    + "/"
                                    + goodsImage.getStandardName()
                                    + goodsImage.getItemId()
                                    + "/sku图"
                                    + "/"
                                    + fileName);
                          });
                }

                final List<ImageBO> mainImageVideos = goodsImage.getMainImageVideos();
                if (CollUtil.isNotEmpty(mainImageVideos)) {
                  mainImageVideos.stream()
                      .sorted(Comparator.comparing(ImageBO::getSort))
                      .forEach(
                          val -> {
                            final String mainImageVideo = val.getUrl();
                            String fileName =
                                checkRepeatName(nameRepeatCheckMap3, val.getFileName());
                            HttpUtil.downloadFile(
                                mainImageVideo,
                                rootDir
                                    + "/"
                                    + goodsImage.getStandardName()
                                    + goodsImage.getItemId()
                                    + "/主图视频"
                                    + "/"
                                    + fileName);
                          });
                }
              }
            }

            zip = ZipUtil.zip(rootDir, rootDir + ".zip", true);

            UploadFileAction action =
                UploadFileAction.ofInputStream(
                    IOUtil.toInputStream(zip), exportTask.getName() + ".zip");
            final String downloadUrl = fileGateway.uploadFile(action).getUrl();

            exportTask.setStatus(ExportTaskStatus.SUCCESS);
            exportTask.setDownloadUrl(downloadUrl);
          } catch (Exception e) {
            log.error(
                "导出新品商品图片异常 taskId={} name={} params={}",
                taskId,
                exportTask.getName(),
                newGoodsQueryPage,
                e);
            exportTask.setStatus(ExportTaskStatus.FAIL);
            exportTask.setError(StringUtil.truncateByMaxBytes(e.getMessage(), 20000));
          } finally {
            if (zip != null) {
              zip.delete();
            }
            FileUtil.del(rootDir);
            try {
              exportTaskGateway.saveOrUpdateExportTask(exportTask);
            } catch (Exception e) {
              log.error("导出新品商品图片，更新导出记录失败 task:{}", exportTask);
            }
          }
        };
    ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(RunnableWrapper.of(runnable));
    return taskId;
  }

  @Override
  public NewGoods getBySkuCode(String code) {
    return iNewGoodsService.getOne(
        new QueryWrapper<NewGoods>().lambda().eq(NewGoods::getSkuCode, code));
  }

  private void joinWrapper(MPJLambdaWrapper<NewGoods> queryWrapper) {
    queryWrapper
        .leftJoin(Item.class, Item::getId, NewGoods::getItemId)
        .leftJoin(
            ItemProcurement.class,
            q ->
                q.eq(ItemProcurement::getItemId, NewGoods::getItemId)
                    .eq(ItemProcurement::getIsDel, 0))
        .leftJoin(
            ItemSku.class,
            q -> q.eq(ItemSku::getSkuCode, NewGoods::getSkuCode).eq(ItemSku::getIsDel, 0))
        .leftJoin(
            ItemDrawer.class,
            q -> q.eq(ItemDrawer::getItemId, NewGoods::getItemId).eq(ItemDrawer::getIsDel, 0))
        .leftJoin(
            Buyer.class,
            q -> q.eq(Buyer::getId, ItemProcurement::getBuyerId).eq(Buyer::getIsDel, 0))
        .leftJoin(
            ItemLaunchPlanItemRef.class,
            q ->
                q.eq(ItemLaunchPlanItemRef::getItemId, NewGoods::getItemId)
                    .eq(ItemLaunchPlanItemRef::getIsDel, 0))
        .leftJoin(ItemLaunchPlan.class, ItemLaunchPlan::getId, ItemLaunchPlanItemRef::getPlanId)
        .leftJoin(
            ItemTrainingMaterials.class,
            q ->
                q.eq(ItemTrainingMaterials::getItemId, NewGoods::getItemId)
                    .eq(ItemTrainingMaterials::getIsDel, 0));
  }

  private void whereWrapper(MPJLambdaWrapper<NewGoods> queryWrapper, NewGoodsQueryPage queryPage) {
    queryWrapper
        .and(
            StringUtils.isNotBlank(queryPage.getSkuCode()),
            q ->
                q.eq(ItemSku::getSkuCode, queryPage.getSkuCode())
                    .or()
                    .eq(ItemSku::getProviderSpecifiedCode, queryPage.getSkuCode()))
        .and(
            StringUtils.isNotBlank(queryPage.getItemCode()),
            q ->
                q.eq(Item::getCode, queryPage.getItemCode())
                    .or()
                    .eq(Item::getProviderSpecifiedCode, queryPage.getItemCode()));
    queryWrapper
        .ge(
            Objects.nonNull(queryPage.getDownFrameTimeStart())
                && queryPage.getDownFrameTimeStart() > 0,
            Item::getDownFrameTime,
            queryPage.getDownFrameTimeStart())
        .le(
            Objects.nonNull(queryPage.getDownFrameTimeEnd()) && queryPage.getDownFrameTimeEnd() > 0,
            Item::getDownFrameTime,
            queryPage.getDownFrameTimeEnd());
    queryWrapper
        .in(
            CollUtil.isNotEmpty(queryPage.getNewGoodsIds()),
            NewGoods::getId,
            queryPage.getNewGoodsIds())
        .in(
            CollUtil.isNotEmpty(queryPage.getItemIds()),
            NewGoods::getItemId,
            queryPage.getItemIds())
        .like(StringUtils.isNotBlank(queryPage.getName()), Item::getName, queryPage.getName())
        //        .and(
        //            CollectionUtil.isNotEmpty(queryPage.getBusinessLine()),
        //            q -> {
        //              for (Integer businessLine : queryPage.getBusinessLine()) {
        //                q.or().apply("t1.is_business_line" + businessLine + " = 1");
        //              }
        //            })
        .like(
            StringUtils.isNotBlank(queryPage.getStandardName()),
            ItemDrawer::getStandardName,
            queryPage.getStandardName())
        .eq(
            Objects.nonNull(queryPage.getBrandId()) && queryPage.getBrandId() > 0,
            Item::getBrandId,
            queryPage.getBrandId())
        .eq(
            Objects.nonNull(queryPage.getPartnerSysType()),
            Item::getPartnerSysType,
            queryPage.getPartnerSysType())
        .eq(
            Objects.nonNull(queryPage.getBuyerId()) && queryPage.getBuyerId() > 0,
            Buyer::getUserId,
            queryPage.getBuyerId())
        .eq(
            Objects.nonNull(queryPage.getPrincipalId()) && queryPage.getPrincipalId() > 0,
            NewGoods::getPrincipalId,
            queryPage.getPrincipalId())
        .like(
            Objects.nonNull(queryPage.getQcId()) && queryPage.getQcId() > 0,
            ItemProcurement::getQcIds,
            queryPage.getQcId())
        .eq(
            Objects.nonNull(queryPage.getCategoryId()) && queryPage.getCategoryId() > 0,
            Item::getCategoryId,
            queryPage.getCategoryId())
        .ge(
            Objects.nonNull(queryPage.getStartTime()) && queryPage.getStartTime() > 0,
            ItemLaunchPlan::getLaunchTime,
            queryPage.getStartTime())
        .le(
            Objects.nonNull(queryPage.getEndTime()) && queryPage.getEndTime() > 0,
            ItemLaunchPlan::getLaunchTime,
            queryPage.getEndTime())
        .like(
            StringUtils.isNotBlank(queryPage.getSpecs()),
            ItemSku::getSpecifications,
            queryPage.getSpecs())
        .eq(
            Objects.nonNull(queryPage.getShipmentType()) && queryPage.getShipmentType() > 0,
            NewGoods::getShipmentType,
            queryPage.getShipmentType())
        .like(
            StringUtils.isNotBlank(queryPage.getLogistics()),
            NewGoods::getLogistics,
            queryPage.getLogistics())
        .eq(
            Objects.nonNull(queryPage.getIsReduce()),
            NewGoods::getIsReduce,
            queryPage.getIsReduce())
        .eq(
            Objects.nonNull(queryPage.getIsCoupon()),
            NewGoods::getIsCoupon,
            queryPage.getIsCoupon())
        .eq(
            Objects.nonNull(queryPage.getShipmentAging()),
            NewGoods::getShipmentAging,
            queryPage.getShipmentAging())
        .eq(
            Objects.nonNull(queryPage.getNoReason()),
            NewGoods::getNoReason,
            queryPage.getNoReason())
        .eq(
            NumberUtil.isPositive(queryPage.getItemType()),
            ItemDrawer::getType,
            queryPage.getItemType())
        .apply(
            StringUtil.isNotBlank(queryPage.getTbId()),
            "exists(select 1 from item_drawer_platform_link where drawer_id = t4.id and platform = 1 and outer_item_id = {0} and is_del = 0)",
            queryPage.getTbId())
        .apply(
            StringUtil.isNotBlank(queryPage.getDouId()),
            "exists(select 1 from item_drawer_platform_link where drawer_id = t4.id and platform = 3 and outer_item_id = {0} and is_del = 0)",
            queryPage.getDouId())
        .apply(
            StringUtil.isNotBlank(queryPage.getWechatId()),
            "exists(select 1 from item_drawer_platform_link where drawer_id = t4.id and platform = 7 and outer_item_id = {0} and is_del = 0)",
            queryPage.getWechatId())
        .apply(
            StringUtil.isNotBlank(queryPage.getMiniRedBookId()),
            "exists(select 1 from item_drawer_platform_link where drawer_id = t4.id and platform = 5 and outer_item_id = {0} and is_del = 0)",
            queryPage.getMiniRedBookId())
        .eq(
            StringUtil.isNotBlank(queryPage.getPartnerProviderItemSn()),
            Item::getPartnerProviderItemSn,
            queryPage.getPartnerProviderItemSn());
    queryWrapper.apply(
        StringUtil.isNotBlank(queryPage.getTag()),
        "exists(select id from item_tag where is_del = 0 and item_id = t1.id and tag_id = {0})",
        queryPage.getTag());
    final Integer merge = queryPage.getMerge();
    if (merge != null && merge != 0) {
      if (merge == 1) {
        queryWrapper.apply(
            "exists(select id from item_drawer_merge_item where is_del = 0 and item_id = t1.id)");
      }
      if (merge == 2) {
        queryWrapper.apply(
            "not exists(select id from item_drawer_merge_item where is_del = 0 and item_id = t1.id)");
      }
    }
    if (Objects.nonNull(queryPage.getMaterialsStatus())
        || NumberUtil.isPositive(queryPage.getStatus())
        || CollectionUtil.isNotEmpty(queryPage.getItemIdsNoStatusFilter())) {
      queryWrapper.and(
          q -> {
            if (CollectionUtil.isNotEmpty(queryPage.getItemIdsNoStatusFilter())) {
              q.or().in(Item::getId, queryPage.getItemIdsNoStatusFilter());
            }
            if (Objects.nonNull(queryPage.getMaterialsStatus())
                || NumberUtil.isPositive(queryPage.getStatus())) {
              q.or(
                  q1 -> {
                    if (Objects.nonNull(queryPage.getMaterialsStatus())) {
                      q1.eq(ItemTrainingMaterials::getStatus, queryPage.getMaterialsStatus());
                    }
                    if (NumberUtil.isPositive(queryPage.getStatus())) {
                      if (queryPage.getStatus() == 41) {
                        q1.eq(Item::getLaunchStatus, ItemLaunchStatus.TO_BE_AUDITED.getValue())
                            .in(
                                Item::getAuditStatus,
                                Arrays.asList(
                                    ItemAuditStatus.WAIT_LEGAL_AUDIT.getValue(),
                                    ItemAuditStatus.NONE.getValue()));
                      } else if (queryPage.getStatus() == 42) {
                        q1.eq(Item::getLaunchStatus, ItemLaunchStatus.TO_BE_AUDITED.getValue())
                            .eq(Item::getAuditStatus, ItemAuditStatus.WAIT_QC_AUDIT.getValue());
                      } else {
                        q1.eq(
                            queryPage.getStatus() > 0,
                            Item::getLaunchStatus,
                            queryPage.getStatus());
                      }
                    }
                  });
            }
          });
    }
  }

  private MPJLambdaWrapper<NewGoods> setParam(NewGoodsQueryPage queryPage) {
    final MPJLambdaWrapper<NewGoods> queryWrapper = MPJWrappers.lambdaJoin();

    joinWrapper(queryWrapper);

    whereWrapper(queryWrapper, queryPage);

    if (!queryPage.getShowAll()) {
      queryWrapper.and(
          r ->
              r.eq(Buyer::getUserId, queryPage.getUserId())
                  .or()
                  .eq(NewGoods::getPrincipalId, queryPage.getUserId())
                  .or()
                  .like(ItemProcurement::getQcIds, queryPage.getUserId() + ","));
    }
    queryWrapper.orderByDesc(NewGoods::getCreatedAt).orderByAsc(NewGoods::getId);
    return queryWrapper;
  }

  private void setSelect(MPJLambdaWrapper<NewGoods> queryWrapper) {
    queryWrapper
        .selectAs(NewGoods::getId, NewGoodsVo::getId)
        .selectAs(NewGoods::getItemId, NewGoodsVo::getItemId)
        .selectAs(ItemSku::getSkuCode, NewGoodsVo::getSkuCode)
        .selectAs(ItemSku::getProviderSpecifiedCode, NewGoodsVo::getSpecialSkuCode)
        .selectAs(Item::getName, NewGoodsVo::getName)
        .selectAs(Item::getCode, NewGoodsVo::getItemCode)
        .selectAs(ItemSku::getSpecifications, NewGoodsVo::getSpecs)
        .selectAs(ItemSku::getCostPrice, NewGoodsVo::getCostPrice)
        .selectAs(ItemSku::getProps, NewGoodsVo::getProps)
        .selectAs(Item::getCategoryId, NewGoodsVo::getCategoryId)
        .selectAs(Item::getBrandId, NewGoodsVo::getBrandId)
        .selectAs(Item::getPartnerSysType, NewGoodsVo::getPartnerSysType)
        .selectAs(ItemLaunchPlan::getLaunchTime, NewGoodsVo::getShelfTime)
        .selectAs(ItemLaunchPlan::getId, NewGoodsVo::getPlanId)
        .selectAs(ItemLaunchPlan::getPlanName, NewGoodsVo::getPlanName)
        .selectAs(ItemDrawer::getStandardName, NewGoodsVo::getStandardName)
        .selectAs(ItemDrawer::getType, NewGoodsVo::getItemType)
        .selectAs(NewGoods::getIsLongTerm, NewGoodsVo::getIsLongTerm)
        .selectAs(NewGoods::getActivePeriodStart, NewGoodsVo::getActivePeriodStart)
        .selectAs(NewGoods::getActivePeriodEnd, NewGoodsVo::getActivePeriodEnd)
        .selectAs(NewGoods::getNoReason, NewGoodsVo::getNoReason)
        .selectAs(NewGoods::getLinePrice, NewGoodsVo::getLinePrice)
        .selectAs(NewGoods::getDailyPrice, NewGoodsVo::getDailyPrice)
        .selectAs(NewGoods::getActivePrice, NewGoodsVo::getActivePrice)
        .selectAs(NewGoods::getActiveContent, NewGoodsVo::getActiveContent)
        .selectAs(NewGoods::getLiveActive, NewGoodsVo::getLiveActive)
        .selectAs(NewGoods::getChannelLowest, NewGoodsVo::getChannelLowest)
        .selectAs(NewGoods::getIsReduce, NewGoodsVo::getIsReduce)
        .selectAs(NewGoods::getShipmentType, NewGoodsVo::getShipmentType)
        .selectAs(NewGoods::getShipmentArea, NewGoodsVo::getShipmentArea)
        .selectAs(NewGoods::getShipmentAging, NewGoodsVo::getShipmentAging)
        .selectAs(NewGoods::getLogistics, NewGoodsVo::getLogistics)
        .selectAs(NewGoods::getExpressTemplate, NewGoodsVo::getExpressTemplate)
        .selectAs(NewGoods::getRemark, NewGoodsVo::getRemark)
        .selectAs(Item::getLaunchStatus, NewGoodsVo::getStatus)
        .selectAs(Item::getAuditStatus, NewGoodsVo::getAuditStatus)
        .selectAs(NewGoods::getIsCoupon, NewGoodsVo::getIsCoupon)
        .selectAs(Buyer::getUserId, NewGoodsVo::getBuyerId)
        .selectAs(NewGoods::getPrincipalId, NewGoodsVo::getPrincipalId)
        .selectAs(NewGoods::getLegalId, NewGoodsVo::getLegalId)
        .selectAs(ItemProcurement::getQcIds, NewGoodsVo::getQcIds)
        .selectAs(ItemDrawer::getHomeCopy, NewGoodsVo::getHomeCopy)
        .selectAs(NewGoods::getDailyActivities, NewGoodsVo::getDailyActivities)
        .selectAs(NewGoods::getSingleBuyPrice, NewGoodsVo::getSingleBuyPrice)
        .selectAs(NewGoods::getRunFeedback, NewGoodsVo::getRunFeedback)
        .selectAs(NewGoods::getALevelActivityGift, NewGoodsVo::getALevelActivityGift)
        .selectAs(NewGoods::getALevelActivityPrice, NewGoodsVo::getALevelActivityPrice)
        .selectAs(NewGoods::getALevelActivityLiveGift, NewGoodsVo::getALevelActivityLiveGift)
        .selectAs(NewGoods::getALevelActivityLivePrice, NewGoodsVo::getALevelActivityLivePrice)
        .selectAs(NewGoods::getSLevelPromotePrice, NewGoodsVo::getSLevelPromotePrice)
        .selectAs(NewGoods::getSLevelPromoteRule, NewGoodsVo::getSLevelPromoteRule)
        .selectAs(NewGoods::getSLevelPromoteLivePrice, NewGoodsVo::getSLevelPromoteLivePrice)
        .selectAs(NewGoods::getSLevelPromoteLiveRule, NewGoodsVo::getSLevelPromoteLiveRule)
        .selectAs(Item::getBusinessLine, NewGoodsVo::getBusinessLine)
        .selectAs(Item::getBusinessLines, NewGoodsVo::getBusinessLines)
        .selectAs(ItemDrawer::getLiveVerbalTrickStatus, NewGoodsVo::getLiveVerbalTrickStatus)
        .selectAs(ItemDrawer::getLiveVerbalTrick, NewGoodsVo::getLiveVerbalTrick)
        .selectAs(ItemTrainingMaterials::getStatus, NewGoodsVo::getMaterialsStatus)
        .selectAs(Item::getPartnerProviderItemSn, NewGoodsVo::getPartnerProviderItemSn)
        .selectAs(ItemSku::getContractSalePrice, NewGoodsVo::getContractSalePrice)
        .selectAs(ItemSku::getPlatformCommission, NewGoodsVo::getPlatformCommission)
        .selectAs(Item::getDownFrameTime, NewGoodsVo::getDownFrameTime)
        .selectAs(Item::getDownFrameReason, NewGoodsVo::getDownFrameReason)
        .selectAs(ItemDrawer::getTbTitle, NewGoodsVo::getTbTitle)
    ;
  }

  @Override
  public BatchResult<NewGoods> itemSync(
      List<ItemLaunchPlanItemRefInfo> refInfos, ItemLaunchPlan plan) {
    final BatchResult<NewGoods> batchResult = new BatchResult<NewGoods>();
    if (CollUtil.isEmpty(refInfos)) {
      return batchResult;
    }
    List<Long> itemIds =
        refInfos.stream()
            .map(ItemLaunchPlanItemRefInfo::getItemId)
            .distinct()
            .collect(Collectors.toList());
    ArrayList<NewGoods> list = Lists.newArrayList();
    Map<Long, ItemWithLaunchPlanDto> itemDtoMap = itemService.getItemDtoMap(itemIds);
    // Map<Long, ItemBuyerDto> itemsBuyerMap = itemService.getItemsBuyerMap(itemIds);

    final Long launchTime = plan.getLaunchTime();

    for (ItemLaunchPlanItemRefInfo refInfo : refInfos) {
      Long itemId = refInfo.getItemId();
      List<ItemSku> skuList = itemSkuGateway.getSkuList(itemId);
      for (ItemSku itemSku : skuList) {
        String skuCode = itemSku.getSkuCode();
        ItemWithLaunchPlanDto itemWithLaunchPlanDto = itemDtoMap.get(itemId);
        String itemName =
            Optional.ofNullable(itemWithLaunchPlanDto)
                .map(ItemWithLaunchPlanDto::getItemName)
                .orElse("");

        ItemBaseDO baseInfo = itemGateway.getBaseInfo(itemId);
        Assert.notNull(baseInfo, "item基础信息查询为空，itemId非法。itemId:" + itemId);
        NewGoods newGoods = new NewGoods();
        LambdaQueryWrapper<NewGoods> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(NewGoods::getSkuCode, skuCode);
        NewGoods one = newGoodsService.getOne(queryWrapper);
        if (Objects.nonNull(one)) {
          newGoods = one;
        }

        newGoods.setItemId(refInfo.getItemId());
        newGoods.setSkuCode(skuCode);
        newGoods.setName(itemName);
        // 品牌
        if (Objects.nonNull(baseInfo.getBrandId())) {
          newGoods.setBrandId(baseInfo.getBrandId());
        }
        newGoods.setPrincipalId(refInfo.getPrincipalId());
        // 发货方式
        // 新品商品 #发货类型 1:工厂发货 2:仓库发货
        // 后端商品 #商品发货渠道 0:仓库发货 1:工厂发货（多选逗号分隔）
        if (newGoods.getId() == null) {
          batchResult.addNewObject(newGoods);
          int shipmentType = 0;
          if (StringUtil.isNotBlank(baseInfo.getDelivery())) {
            shipmentType =
                baseInfo.getDelivery().contains(ItemDelivery.FACTORY.getValue().toString()) ? 1 : 2;
          }
          newGoods.setShipmentType(shipmentType);
        } else {
          batchResult.addUpdateObject(newGoods);
        }
        // 新品活动周期联动
        setActivePeriodByLaunchTime(newGoods, launchTime);

        list.add(newGoods);
      }
    }
    newGoodsService.saveOrUpdateBatch(list);
    return batchResult;
  }

  private static void setActivePeriodByLaunchTime(NewGoods newGoods, Long launchTime) {
    // 新品活动周期：去掉默认【长期】，改为按照上架日期，自动往后顺延8天；（如上架日期为2024-08-13，则新品活动周期默认为2024-08-13～2024-08-20）
    final Long activePeriodEnd = launchTime + 86400 * 7;
    newGoods.setActivePeriodStart(launchTime);
    newGoods.setActivePeriodEnd(activePeriodEnd);
    newGoods.setIsLongTerm(false);
  }

  public BatchResult<NewGoods> itemSyncActivePeriod(
      List<ItemLaunchPlanItemRef> itemRefs, ItemLaunchPlan plan) {
    return null;
  }

  @Override
  public List<NewGoods> listById(Long itemId) {
    List<ItemSku> skuList = itemSkuGateway.getSkuList(itemId);
    ArrayList<NewGoods> list = Lists.newArrayList();
    skuList.forEach(
        sku -> {
          NewGoods newGoods = getBySkuCode(sku.getSkuCode());
          if (Objects.nonNull(newGoods)) {
            list.add(newGoods);
          }
        });
    return list;
  }

  private Boolean checkShelfTime(String time) {
    DateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    Date date;
    boolean equals;
    try {
      date = formatter.parse(time.trim());
      equals = time.equals(formatter.format(date));
    } catch (Exception e) {
      throw ExceptionPlusFactory.bizException(
          ErrorCode.VERIFY_PARAM.getCode(), "预计上架时间格式错误,格式应为2022-05-12 12:59:59");
    }
    return equals;
  }

  /** 校验必填项 */
  private void checkParamCmd(
      NewGoodsCmd newGoodsCmd, NewGoodsCmd currentData, List<String> updateFiledNameList) {
    List<String> hiddenFields = getHiddenFields();

    log.debug(
        "编辑新品商品/检查参数: newGoodsCmd:{} currentData:{} updateFiledNameList:{} hiddenFields:{}",
        newGoodsCmd,
        currentData,
        updateFiledNameList,
        hiddenFields);

    final BeanDesc beanDesc = BeanUtil.getBeanDesc(NewGoodsCmd.class);
    for (PropDesc propertyDescriptor : beanDesc.getProps()) {
      final String name = propertyDescriptor.getFieldName();
      if (name.equals("id")) {
        continue;
      }

      if (!updateFiledNameList.contains(name)) {
        setNull(newGoodsCmd, currentData, name);
        log.debug("编辑新品商品/检查参数:字段 {} 不在指定需要编辑的字段范围内", name);
        continue;
      }

      final Field field = ReflectUtil.getField(NewGoodsCmd.class, name);
      final ApiModelProperty apiModelProperty =
          AnnotationUtil.getAnnotation(field, ApiModelProperty.class);
      final String displayName = apiModelProperty != null ? apiModelProperty.value() : name;

      final PropertyAlias propertyAlias = AnnotationUtil.getAnnotation(field, PropertyAlias.class);

      // 如果不存在说明这个字段不允许修改
      final Optional<FieldMeta> fieldMetaOpt = fieldMetas.get(name);
      if (!fieldMetaOpt.isPresent()) {
        setNull(newGoodsCmd, currentData, name);
        log.debug("编辑新品商品/检查参数:字段 {} 未配置元数据不允许修改", name);
        continue;
      }
      final FieldMeta fieldMeta = fieldMetaOpt.get();
      if (fieldMeta.isNotCheck()) {
        log.debug("编辑新品商品/检查参数:字段 {} 配置为跳过默认检查", name);
        continue;
      }

      final Object fieldValue = propertyDescriptor.getValue(newGoodsCmd);
      final Object currentFieldValue = propertyDescriptor.getValue(currentData);

      boolean isHidden =
          CollUtil.isNotEmpty(hiddenFields)
              && (hiddenFields.contains(name)
                  || propertyAlias != null && hiddenFields.contains(propertyAlias.value()));

      final boolean isAccessField =
          fieldMeta.isAccessAny()
              || fieldMeta.getPermissions().stream().anyMatch(UserContext::hasPermission);
      if (ObjectUtil.isEmpty(fieldValue, fieldMeta.isEmptyCheckLenient())) {
        // 如果用户设置当前字段隐藏则跳过检查
        if (isHidden) {
          // 如果字段已经隐藏则不做任何修改
          setNull(newGoodsCmd, currentData, name);
          log.debug("编辑新品商品/检查参数:字段 {} 字段为空但被隐藏，跳过检查", name);
          continue;
        }

        // 非必填字段跳过
        if (!fieldMeta.isRequired()) {
          log.debug("编辑新品商品/检查参数:字段 {} 字段为空但非必填，跳过检查", name);

          continue;
        }

        if (isAccessField) {
          log.debug("编辑新品商品/检查参数:字段 {} 字段必填且有访问权限强制填写", name);
          throw ExceptionPlusFactory.bizException(
              ErrorCode.VERIFY_PARAM.getCode(), StringUtil.format("{}为必填字段", displayName));
        }

        // 当前字段为空不做修改，强制 set 为 Null
        setNull(newGoodsCmd, currentData, name);
      } else if (isHidden) {
        log.debug("编辑新品商品/检查参数:字段 {} 字段不为空但是被隐藏忽略修改", name);

        // 如果用户当前字段隐藏，需要忽略掉这个字段的修改
        setNull(newGoodsCmd, currentData, name);

      } else if (!isAccessField && !ObjectUtil.equal(fieldValue, currentFieldValue)) {
        log.debug("编辑新品商品/检查参数:字段 {} 字段被更改但无访问权限", name);

        // 如果用户没有修改这个字段的权限，提示不允许修改
        throw ExceptionPlusFactory.bizException(
            ErrorCode.VERIFY_PARAM.getCode(),
            StringUtil.format("您缺少编辑 ''{}'' 字段的权限，请联系系统管理员申请权限", displayName));
      }
    }

    // 当前用户是否有采购字段的权限
    final boolean hasPermissionOfEditPurchaseFields =
        UserContext.hasPermission(GlobalConstant.NEW_GOODS_EDIT_PURCHASE_FIELDS);
    // 检查新品活动周期这个列是否被隐藏
    boolean isHiddenOfActivePeriod =
        CollUtil.isNotEmpty(hiddenFields) && hiddenFields.contains("activePeriod");
    if (!updateFiledNameList.contains("activePeriodStart")) {
      isHiddenOfActivePeriod = true;
    }
    // 新品活动周期字段未被隐藏需要校验是否填写，是否有权限
    if (!isHiddenOfActivePeriod) {
      final Boolean newIsLongTerm = Optional.ofNullable(newGoodsCmd.getIsLongTerm()).orElse(false);
      final Long newActivePeriodStart =
          Optional.ofNullable(newGoodsCmd.getActivePeriodStart()).orElse(0L);
      final Long newActivePeriodEnd =
          Optional.ofNullable(newGoodsCmd.getActivePeriodEnd()).orElse(0L);

      // 如果有权限编辑采购字段检查是否填写完整
      if (hasPermissionOfEditPurchaseFields) {
        // 如果不是长期，检查是否填写时间范围
        if (!newIsLongTerm) {
          if (!NumberUtil.isPositive(newActivePeriodStart)) {
            log.debug("编辑新品商品/检查参数:字段 {} 非长期且未填写开始结束周期", "activePeriod");
            throw ExceptionPlusFactory.bizException(
                ErrorCode.VERIFY_PARAM.getCode(), StringUtil.format("{}为必填字段", "新品活动开始周期"));
          }
          if (!NumberUtil.isPositive(newActivePeriodEnd)) {
            log.debug("编辑新品商品/检查参数:字段 {} 非长期且未填写开始结束周期", "activePeriod");
            throw ExceptionPlusFactory.bizException(
                ErrorCode.VERIFY_PARAM.getCode(), StringUtil.format("{}为必填字段", "新品活动结束周期"));
          }
          if (Objects.isNull(newGoodsCmd.getIsLongTerm())) {
            newGoodsCmd.setIsLongTerm(false);
          }
        } else {
          // 如果是长期活动，活动日期范围不需要填写
          newGoodsCmd.setActivePeriodStart(0L);
          newGoodsCmd.setActivePeriodEnd(0L);
        }
      } else {
        // 如果没有权限编辑采购字段，需要检查是否修改了这个数据
        if (!ObjectUtil.equal(newIsLongTerm, currentData.getIsLongTerm())
            || !ObjectUtil.equal(newActivePeriodStart, currentData.getActivePeriodStart())
            || !ObjectUtil.equal(newActivePeriodEnd, currentData.getActivePeriodEnd())) {
          log.debug("编辑新品商品/检查参数:字段 {} 发生变更但无编辑权限", "activePeriod");
          throw ExceptionPlusFactory.bizException(
              ErrorCode.VERIFY_PARAM.getCode(),
              StringUtil.format("您缺少编辑 ''{}'' 字段的权限，请联系系统管理员申请权限", "新品活动周期"));
        }
      }
    } else {
      // 如果新品活动周期字段被隐藏，则不允许编辑这几个字段
      newGoodsCmd.setActivePeriodStart(null);
      newGoodsCmd.setActivePeriodEnd(null);
      newGoodsCmd.setIsLongTerm(null);
      log.debug("编辑新品商品/检查参数:字段 {} 被隐藏不允许编辑", "activePeriod");
    }

    // 当前用户是否有编辑产品字段的权限
    final boolean hasPermissionOfEditProductFields =
        UserContext.hasPermission(GlobalConstant.NEW_GOODS_EDIT_PRODUCT_FIELDS);

    // 检查预计上架时间（上新计划）这个列是否被隐藏
    boolean isHiddenOfShelfTime =
        CollUtil.isNotEmpty(hiddenFields) && hiddenFields.contains("shelfTime");
    if (isHiddenOfShelfTime) {
      log.debug("编辑新品商品/检查参数:字段 {} 被隐藏不允许编辑", "shelfTime");
    }

    if (!updateFiledNameList.contains("planId")) {
      log.debug("编辑新品商品/检查参数:字段 {} 不在指定需要编辑的字段范围内", "shelfTime");
      isHiddenOfShelfTime = true;
    }
    if (isHiddenOfShelfTime) {
      newGoodsCmd.setPlanId(null);
    } else {
      if (hasPermissionOfEditProductFields && NumberUtil.isZeroOrNull(newGoodsCmd.getPlanId())) {
        log.debug("编辑新品商品/检查参数:字段 {} 为必填字段但为空", "shelfTime");
        throw ExceptionPlusFactory.bizException(
            ErrorCode.VERIFY_PARAM.getCode(), StringUtil.format("{}为必填字段", "预计上架时间"));
      }
      // 没有权限的用户不允许修改上新计划（因为这边不想重复再查一次关联的上新计划，这个在update方法中在修改之前判断）
    }
  }

  private void setNull(NewGoodsCmd newGoodsCmd, NewGoodsCmd currentData, String name) {
    BeanUtil.setFieldValue(newGoodsCmd, name, null);
    BeanUtil.setFieldValue(currentData, name, null);
  }

  @NonNull
  private List<String> getHiddenFields() {
    List<Fold> folds = foldService.getFoldByType(1);
    List<String> hiddenFields = Lists.newArrayList();

    if (CollUtil.isNotEmpty(folds)) {
      hiddenFields =
          folds.stream()
              .filter(fold -> Objects.equals(fold.getStatus(), 1))
              .map(Fold::getField)
              .collect(Collectors.toList());
    }
    return hiddenFields;
  }

  /** 根据SkuCode 查询数据库存在的商品 */
  private Map<String, NewGoods> batchQueryBySkuCode(List<String> skuCodes) {
    QueryWrapper<NewGoods> query = new QueryWrapper<>();
    query.lambda().in(NewGoods::getSkuCode, skuCodes);
    return iNewGoodsService.list(query).stream()
        .collect(Collectors.toMap(NewGoods::getSkuCode, Function.identity()));
  }

  private ItemSku getItemSku(String skuCode) {
    return itemSkuGateway.getBySkuCode(skuCode);
  }

  //    @Override
  //    public Boolean itemDrawerSyncStandardName(Long itemId, String standardName) {
  //        LambdaUpdateWrapper<NewGoods> newGoodsUpdateWrapper = new LambdaUpdateWrapper<>();
  //        newGoodsUpdateWrapper.set(NewGoods::getStandardName, standardName)
  //                .eq(NewGoods::getItemId, itemId);
  //        boolean update = iNewGoodsService.update(newGoodsUpdateWrapper);
  //
  //        List<NewGoods> newGoods = iNewGoodsService.list(new LambdaQueryWrapper<NewGoods>() {{
  //            eq(NewGoods::getItemId, itemId);
  //        }});
  //        for (NewGoods newGood : newGoods) {
  //            DiffUtil.ChangePropertyObj changePropertyObj = new
  // DiffUtil.ChangePropertyObj("产品标准名", newGood.getStandardName(), standardName);
  //            operateLogGateway.addOperatorLog(UserContext.getUserId(),
  // OperateLogTarget.NEW_GOODS, newGood.getId(),
  //                    "商品抽屉同步产品标准名到新品库", Collections.singletonList(changePropertyObj));
  //        }
  //        return update;
  //    }

  @Override
  public List<NewGoodsVo> getNewGoodsVO(Long itemId) {
    final NewGoodsQueryPage queryPage = new NewGoodsQueryPage();
    queryPage.setItemIds(Collections.singletonList(itemId));
    queryPage.setPageIndex(1);
    queryPage.setPageSize(100);
    queryPage.setShowAll(true);
    //        queryPage.setBusinessLine(UserPermissionJudge.getUserBusinessLineValues());
    final PageResponse<NewGoodsVo> response = queryPage(queryPage);
    return response.getData();
  }

  @Override
  public NewGoods getNewGoods(Long itemId) {
    return iNewGoodsService
        .getBaseMapper()
        .selectOne(
            new LambdaQueryWrapper<NewGoods>() {
              {
                eq(NewGoods::getItemId, itemId);
                last("limit 1");
              }
            });
  }

  @Override
  public Boolean syncPrincipal(Long itemId, Long principalId) {
    LambdaUpdateWrapper<NewGoods> objectLambdaUpdateWrapper = new LambdaUpdateWrapper<>();
    objectLambdaUpdateWrapper
        .eq(NewGoods::getItemId, itemId)
        .set(NewGoods::getPrincipalId, principalId);
    return iNewGoodsService.update(objectLambdaUpdateWrapper);
  }

  @Override
  public Boolean syncQc(Long itemId, String qcIds) {
    final ItemProcurement itemProcurement = itemProcurementService.getByItemId(itemId);
    if (Objects.isNull(itemProcurement)) {
      return false;
    }
    final String qcIdL = itemProcurement.getQcIds();
    LambdaUpdateWrapper<ItemProcurement> itemProcurementLambdaUpdateWrapper =
        new LambdaUpdateWrapper<>();
    itemProcurementLambdaUpdateWrapper
        .set(ItemProcurement::getQcIds, qcIds)
        .eq(ItemProcurement::getItemId, itemId);
    final boolean update = itemProcurementService.update(itemProcurementLambdaUpdateWrapper);
    if (update) {
      EventBusUtil.post(new ItemQcChangeEvent(itemId, qcIdL, qcIds), true);
    }
    return update;
  }

  @Override
  public List<OperateLog> getOperateLogs(Long targetId) {
    List<NewGoods> newGoods =
        iNewGoodsService.lambdaQuery().eq(NewGoods::getItemId, targetId).select().list();
    List<Long> newGoodsIds = newGoods.stream().map(NewGoods::getId).collect(Collectors.toList());
    final List<OperateLog> newGoodsOperateLogs =
        operateLogDomainService.getOperateLogs(OperateLogTarget.NEW_GOODS, newGoodsIds);
    final List<OperateLog> newGoodsSpuOperateLogs =
        operateLogDomainService.getOperateLogs(OperateLogTarget.NEW_GOODS_SPU, targetId);
    newGoodsOperateLogs.addAll(newGoodsSpuOperateLogs);
    final ItemDrawer itemDrawer = itemDrawerBizService.getItemDrawer(targetId);
    final ArrayList<Long> drawerIds = new ArrayList<>();
    drawerIds.add(targetId);
    if (itemDrawer != null) {
      drawerIds.add(itemDrawer.getId());
    }
    final List<OperateLog> itemDrawerOperateLogs =
        operateLogDomainService.getOperateLogs(OperateLogTarget.ITEM_DRAWER, drawerIds);
    return parseLogData(newGoodsOperateLogs, itemDrawerOperateLogs);
  }

  @Override
  public List<OperateLog> getPriceLog(Long targetId) {
    return operateLogDomainService.getOperateLogs(
        Arrays.asList(OperateLogTarget.HANDING_SHEET_ITEM_PRICE, OperateLogTarget.NEW_GOODS_PRICE),
        targetId);
  }

  @Override
  public Response importExcel(InputStream inputStream) {
    List<NewGoodsSheetRow> sheetRowsRaw =
        EasyExcel.read(inputStream)
            .headRowNumber(1)
            .head(NewGoodsSheetRow.class)
            .sheet(1)
            .doReadSync();
    final List<NewGoodsSheetRow> sheetRows =
        sheetRowsRaw.stream().distinct().collect(Collectors.toList());
    log.info("批量导入新品商品预处理，总计{}行，去重后{}行", sheetRowsRaw.size(), sheetRows.size());
    CompletableFuture.runAsync(
            () -> {
              final HashMap<String, Long> launchDateEpochsMap = new HashMap<>();
              for (NewGoodsSheetRow sheetRow : sheetRows) {
                final String launchDate = sheetRow.getLaunchDate();
                if (StringUtil.isBlank(launchDate)) {
                  continue;
                }
                final LocalDateTime launchLDT = DateUtil.parseCompatibility(launchDate);
                if (launchLDT == null) {
                  log.error(
                      "商品上架日期格式错误:{}，SKU编码:{}，名称:{}",
                      launchDate,
                      sheetRow.getSkuCode(),
                      sheetRow.getName());
                  continue;
                }
                launchDateEpochsMap.put(
                    launchDate, DateUtil.toEpochSecond(DateUtil.toZero(launchLDT)));
              }
              final Map<Long, Long> planIdMap =
                  itemLaunchPlanService.saveBatchIfNotExistAndReturnPlanId(
                      launchDateEpochsMap.values());
              final Long currentTime = DateUtil.currentTime();
              final StopWatch stopWatch = new StopWatch();
              stopWatch.start();
              log.info("批量导入新品商品执行中，执行时间：{}", currentTime);
              for (NewGoodsSheetRow sheetRow : sheetRows) {
                ApplicationContextUtil.getBean(NewGoodsImportRowHandler.class)
                    .handleImportRow(launchDateEpochsMap, planIdMap, currentTime, sheetRow);
              }
              final Collection<Long> planIds = planIdMap.values();
              itemLaunchPlanService.updateBatchPlanRefCount(planIds);
              log.info("更新上新计划关联商品数量成功，上新计划ID:{}", planIds);
              stopWatch.stop();
              log.info("批量导入新品商品完成，耗时：{} ms", stopWatch.getTotalTimeMillis());
            })
        .exceptionally(
            ex -> {
              log.error("批量导入新品商品异常", ex);
              return null;
            });
    return Response.buildSuccess();
  }

  private List<OperateLog> parseLogData(
      List<OperateLog> newGoodsOperateLogs, List<OperateLog> itemDrawerOperateLogs) {
    final Set<Long> newGoodsIds =
        newGoodsOperateLogs.stream()
            .map(OperateLog::getTargetId)
            .map(Long::parseLong)
            .collect(Collectors.toSet());
    final Map<Long, String> newGoodsSkuCodeMap =
        newGoodsIds.isEmpty()
            ? Collections.emptyMap()
            : newGoodsService
                .lambdaQuery()
                .in(NewGoods::getId, newGoodsIds)
                .select(NewGoods::getSkuCode, NewGoods::getId)
                .list()
                .stream()
                .collect(Collectors.toMap(NewGoods::getId, NewGoods::getSkuCode));
    newGoodsOperateLogs.forEach(
        operateLog -> {
          if (StringUtil.isNotBlank(operateLog.getData())) {
            List<ChangePropertyObj> changePropertyObjs = null;
            try {
              changePropertyObjs = JSON.parseArray(operateLog.getData(), ChangePropertyObj.class);
            } catch (Exception e) {
              log.warn(
                  "新品商品操作日志，数据解析异常 logId={} data={}", operateLog.getId(), operateLog.getData());
              return;
            }
            StringBuffer sb = new StringBuffer(operateLog.getTargetType().getDesc());
            if (operateLog.getTargetType() == OperateLogTarget.NEW_GOODS) {
              sb.append("（")
                  .append("规格")
                  .append(newGoodsSkuCodeMap.get(Long.parseLong(operateLog.getTargetId())))
                  .append("）");
            }
            sb.append(";");
            if (StringUtil.isNotBlank(operateLog.getMsg())) {
              sb.append(operateLog.getMsg()).append(";");
            }
            changePropertyObjs.forEach(
                change -> {
                  sb.append(" 修改")
                      .append("【")
                      .append(change.getProperty())
                      .append("】")
                      .append("从")
                      .append("“")
                      .append(handleValue(change.getProperty(), change.getOldVal()))
                      .append("“ ")
                      .append("改为")
                      .append(" “")
                      .append(handleValue(change.getProperty(), change.getNewVal()))
                      .append("“")
                      .append(";");
                  operateLog.setMsg(String.valueOf(sb));
                });
          }
        });
    itemDrawerOperateLogs.forEach(
        operateLog -> {
          if (StringUtil.isNotBlank(operateLog.getData())) {
            List<ChangePropertyObj> changePropertyObjs = null;
            try {
              changePropertyObjs = JSON.parseArray(operateLog.getData(), ChangePropertyObj.class);
            } catch (Exception e) {
              log.warn(
                  "新品商品操作日志，数据解析异常 logId={} data={}", operateLog.getId(), operateLog.getData());
              return;
            }
            StringBuffer sb =
                new StringBuffer(
                    StringUtil.isNotBlank(operateLog.getMsg())
                        ? operateLog.getMsg()
                        : operateLog.getTargetType().getDesc());
            sb.append(";");
            changePropertyObjs.forEach(
                change -> {
                  final String property = change.getProperty();
                  if (ItemDrawerChangeEnum.SKU_IMAGES.getDesc().equals(property)) {
                    handleSkuImageChange(operateLog, sb, change, property);
                    return;
                  }
                  if (ItemDrawerChangeEnum.ATTR_IMAGES.getDesc().equals(property)) {
                    handleAttrImageChange(operateLog, sb, change, property);
                    return;
                  }
                  sb.append(" 修改")
                      .append("【")
                      .append(property)
                      .append("】")
                      .append("从")
                      .append("“")
                      .append(handleValue(property, change.getOldVal()))
                      .append("“ ")
                      .append("改为")
                      .append(" “")
                      .append(handleValue(property, change.getNewVal()))
                      .append("“")
                      .append(";");
                  operateLog.setMsg(String.valueOf(sb));
                });
          }
        });

    return Stream.of(newGoodsOperateLogs, itemDrawerOperateLogs)
        .flatMap(Collection::stream)
        .sorted((o1, o2) -> o2.getCreatedAt().compareTo(o1.getCreatedAt()))
        .collect(Collectors.toList());
  }

  private void handleSkuImageChange(
      OperateLog operateLog, StringBuffer sb, ChangePropertyObj change, String property) {
    final List<ItemDrawerSkuImageEntity> oldImages =
        Optional.ofNullable(change.getOldVal())
            .map(
                v ->
                    JsonUtil.<List<ItemDrawerSkuImageEntity>>parse(
                        v.toString(),
                        new TypeReference<List<ItemDrawerSkuImageEntity>>() {}.getType()))
            .orElseGet(Collections::emptyList);
    final List<ItemDrawerSkuImageEntity> newImages =
        Optional.ofNullable(change.getNewVal())
            .map(
                v ->
                    JsonUtil.<List<ItemDrawerSkuImageEntity>>parse(
                        v.toString(),
                        new TypeReference<List<ItemDrawerSkuImageEntity>>() {}.getType()))
            .orElseGet(Collections::emptyList);
    final List<String> imageUrls =
        Stream.of(oldImages, newImages)
            .flatMap(v -> v.stream().map(ItemDrawerSkuImageEntity::getUrl))
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    final Map<String, com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File>
        fileMap = fileGateway.fileQueryBatchByUrls(imageUrls);
    final Optional<CollChange<ItemDrawerSkuImageEntity>> skuImageEntityCollChange =
        DiffUtil.diffList(oldImages, newImages, ItemDrawerSkuImageEntity.class);
    skuImageEntityCollChange.ifPresent(
        collChange -> {
          for (ItemDrawerSkuImageEntity addedValue : collChange.getAddedValues()) {
            sb.append(" 上传")
                .append("【")
                .append(property)
                .append("（")
                .append(addedValue.getSpecifications())
                .append("）")
                .append("】")
                .append("“")
                .append(formatUrlLog(addedValue.getUrl(), fileMap))
                .append("“ ")
                .append(";");
          }
          for (ItemDrawerSkuImageEntity removedValue : collChange.getRemovedValues()) {
            sb.append(" 移除")
                .append("【")
                .append(property)
                .append("（")
                .append(removedValue.getSpecifications())
                .append("）")
                .append("】")
                .append("“")
                .append(formatUrlLog(removedValue.getUrl(), fileMap))
                .append("“ ")
                .append(";");
          }
          for (Pair<ItemDrawerSkuImageEntity, ItemDrawerSkuImageEntity> updatedValue :
              collChange.getUpdatedValues()) {
            sb.append(" 修改")
                .append("【")
                .append(property)
                .append("（")
                .append(updatedValue.getLeft().getSpecifications())
                .append("）")
                .append("】")
                .append("从")
                .append("“")
                .append(formatUrlLog(updatedValue.getLeft().getUrl(), fileMap))
                .append("“ ")
                .append("改为")
                .append("“")
                .append(formatUrlLog(updatedValue.getRight().getUrl(), fileMap))
                .append("“ ")
                .append(";");
          }
          operateLog.setMsg(String.valueOf(sb));
        });
  }

  private String formatUrlLog(
      String url,
      Map<String, com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File> fileMap) {
    if (url == null) {
      return "";
    }
    final String urlDecode = urlDecode(url);
    @SuppressWarnings("unused")
    final com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File file =
        fileMap.get(url);
    return urlDecode;
  }

  private void handleAttrImageChange(
      OperateLog operateLog, StringBuffer sb, ChangePropertyObj change, String property) {
    final List<ItemDrawerAttrImages> oldImages =
        Optional.ofNullable(change.getOldVal())
            .map(
                v ->
                    JsonUtil.<List<ItemDrawerAttrImages>>parse(
                        v.toString(), new TypeReference<List<ItemDrawerAttrImages>>() {}.getType()))
            .orElseGet(Collections::emptyList);
    final List<ItemDrawerAttrImages> newImages =
        Optional.ofNullable(change.getNewVal())
            .map(
                v ->
                    JsonUtil.<List<ItemDrawerAttrImages>>parse(
                        v.toString(), new TypeReference<List<ItemDrawerAttrImages>>() {}.getType()))
            .orElseGet(Collections::emptyList);
    final List<ItemDrawerAttrImageEntity> newImagesList =
        newImages.stream()
            .flatMap(v -> v.getItemAttrs().stream())
            .map(v -> new ItemDrawerAttrImageEntity(v.getId(), v.getAttrValue(), v.getUrl()))
            .collect(Collectors.toList());
    final List<ItemDrawerAttrImageEntity> oldImagesList =
        oldImages.stream()
            .flatMap(v -> v.getItemAttrs().stream())
            .map(v -> new ItemDrawerAttrImageEntity(v.getId(), v.getAttrValue(), v.getUrl()))
            .collect(Collectors.toList());
    final Optional<CollChange<ItemDrawerAttrImageEntity>> itemDrawerAttrImageEntityCollChange =
        DiffUtil.diffList(oldImagesList, newImagesList, ItemDrawerAttrImageEntity.class);
    final List<String> imageUrls =
        Stream.of(newImagesList, oldImagesList)
            .flatMap(v -> v.stream().map(ItemDrawerAttrImageEntity::getUrl))
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
    final Map<String, com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File>
        fileMap = fileGateway.fileQueryBatchByUrls(imageUrls);

    final Map<Long, String> attrNameMap = Maps.newHashMap();
    for (ItemDrawerAttrImages oldImage : oldImages) {
      for (ItemDrawerAttrImage itemAttr : oldImage.getItemAttrs()) {
        attrNameMap.put(itemAttr.getId(), oldImage.getAttrName());
      }
    }
    for (ItemDrawerAttrImages newImage : newImages) {
      for (ItemDrawerAttrImage itemAttr : newImage.getItemAttrs()) {
        attrNameMap.put(itemAttr.getId(), newImage.getAttrName());
      }
    }
    itemDrawerAttrImageEntityCollChange.ifPresent(
        collChange -> {
          for (ItemDrawerAttrImageEntity addedValue : collChange.getAddedValues()) {
            sb.append(" 上传")
                .append("【")
                .append(property)
                .append("（")
                .append(attrNameMap.get(addedValue.getId()))
                .append(":")
                .append(addedValue.getAttrValue())
                .append("）")
                .append("】")
                .append("“")
                .append(formatUrlLog(addedValue.getUrl(), fileMap))
                .append("“ ")
                .append(";");
          }
          for (ItemDrawerAttrImageEntity removedValue : collChange.getRemovedValues()) {
            sb.append(" 移除")
                .append("【")
                .append(property)
                .append("（")
                .append(attrNameMap.get(removedValue.getId()))
                .append(":")
                .append(removedValue.getAttrValue())
                .append("）")
                .append("】")
                .append("“")
                .append(formatUrlLog(removedValue.getUrl(), fileMap))
                .append("“ ")
                .append(";");
          }
          for (Pair<ItemDrawerAttrImageEntity, ItemDrawerAttrImageEntity> updatedValue :
              collChange.getUpdatedValues()) {
            sb.append(" 修改")
                .append("【")
                .append(property)
                .append("（")
                .append(attrNameMap.get(updatedValue.getLeft().getId()))
                .append(":")
                .append(updatedValue.getLeft().getAttrValue())
                .append("）")
                .append("】")
                .append("从")
                .append("“")
                .append(formatUrlLog(updatedValue.getLeft().getUrl(), fileMap))
                .append("“ ")
                .append("改为")
                .append("“")
                .append(formatUrlLog(updatedValue.getRight().getUrl(), fileMap))
                .append("“ ")
                .append(";");
          }
          operateLog.setMsg(String.valueOf(sb));
        });
  }

  private String urlDecode(String updatedValue) {
    return URLUtil.decode(updatedValue);
  }

  private String handleValue(String property, Object val) {
    try {
      if (val == null) {
        return "";
      }
      final String valStr = val.toString();
      if (StringUtil.startWith(valStr, "http")) {
        return urlDecode(valStr);
      }
      if (StringUtil.contains(property, "是") && StringUtil.contains(property, "否")) {
        if (val instanceof Integer) {
          return ((Integer) val) == 0 ? "是" : "否";
        }
        if (val instanceof Boolean) {
          return ((Boolean) val) ? "是" : "否";
        }
      } else if (StringUtil.containsAny(property, "周期", "时间", "日期")) {
        final LocalDateTime dateTime = DateUtil.parseCompatibility(valStr);
        if (dateTime != null) {
          if (StringUtil.containsAny(property, "日期", "周期")) {
            return DateUtil.formatDate(dateTime.toLocalDate());
          }
          return DateUtil.format(dateTime);
        } else {
          return " - ";
        }
      } else if (StringUtil.equals(property, "发货类型")) {
        return "0".equals(valStr) ? "仓库发货" : "工厂发货";
      } else if (StringUtil.endWithAny(property, "价格", "价")) {
        return NumberUtil.decimalFormat("#.##", val, RoundingMode.HALF_UP);
      }
      // 商品库抽屉 QC负责人、产品负责人 修改
      else if (ItemDrawerChangeEnum.QC.getDesc().equals(property)
          || ItemDrawerChangeEnum.PRINCIPAL.getDesc().equals(property)) {
        if (StringUtil.isNotBlank(valStr)) {
          final List<Long> qcIds =
              StringUtil.splitTrim(valStr, ",").stream()
                  .map(Long::parseLong)
                  .collect(Collectors.toList());
          return userGateway.batchQueryStaffInfoByIds(qcIds).values().stream()
              .map(StaffInfo::getNickname)
              .collect(Collectors.joining(","));
        }
      } else if (ItemDrawerChangeEnum.TYPE.getDesc().equals(property)) {
        try {
          return LaunchItemType.valueOf(valStr).getDesc();
        } catch (IllegalArgumentException e) {
          return valStr;
        }
      } else if (ItemDrawerChangeEnum.PLATFORM_LINKS.getDesc().equals(property)) {
        return JsonUtil.parseList(valStr, ItemDrawerPlatformLinkVO.class).stream()
            .map(ItemDrawerPlatformLinkVO::toString)
            .collect(Collectors.joining("、"));
      }
      return StringUtil.isBlank(valStr) ? " - " : valStr;
    } catch (Exception e) {
      log.warn("数据格式解析错误 属性名称:{} 原始值:{}", property, val);
      return "";
    }
  }

  @SneakyThrows
  @Override
  public SingleResponse<Boolean> copySku(SkuCopyCmd cmd) {
    Optional<NewGoods> optional =
        iNewGoodsService
            .lambdaQuery()
            .eq(NewGoods::getSkuCode, cmd.getSourceSkuCode())
            .select()
            .oneOpt();
    NewGoods source =
        optional.orElseThrow(
            (Supplier<Exception>)
                () -> ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "参数非法"));

    final List<NewGoods> newGoods =
        iNewGoodsService
            .lambdaQuery()
            .in(NewGoods::getSkuCode, cmd.getTargetSkuCodeList())
            .select()
            .list()
            .stream()
            .peek(
                val -> {
                  val.setLinePrice(source.getLinePrice());
                  val.setDailyPrice(source.getDailyPrice());
                  val.setActivePrice(source.getActivePrice());
                  val.setDailyActivities(source.getDailyActivities());
                  val.setActivePeriodStart(source.getActivePeriodStart());
                  val.setActivePeriodEnd(source.getActivePeriodEnd());
                  val.setLiveActive(source.getLiveActive());
                  val.setActiveContent(source.getActiveContent());
                  val.setIsCoupon(source.getIsCoupon());
                  val.setIsReduce(source.getIsReduce());
                  val.setChannelLowest(source.getChannelLowest());
                  val.setIsLongTerm(source.getIsLongTerm());
                  val.setSingleBuyPrice(source.getSingleBuyPrice());
                  val.setALevelActivityGift(source.getALevelActivityGift());
                  val.setALevelActivityPrice(source.getALevelActivityPrice());
                  val.setALevelActivityLiveGift(source.getALevelActivityLiveGift());
                  val.setALevelActivityLivePrice(source.getALevelActivityLivePrice());
                  val.setSLevelPromoteRule(source.getSLevelPromoteRule());
                  val.setSLevelPromotePrice(source.getSLevelPromotePrice());
                  val.setSLevelPromoteLiveRule(source.getSLevelPromoteLiveRule());
                  val.setSLevelPromoteLivePrice(source.getSLevelPromoteLivePrice());
                })
            .collect(Collectors.toList());
    iNewGoodsService.updateBatchById(newGoods);

    newGoods.forEach(
        newGoodsItem -> {
          operateLogGateway.addOperatorLog(
              UserContext.getUserId(),
              OperateLogTarget.NEW_GOODS,
              newGoodsItem.getId(),
              StringUtil.format("【新品商品数据从{}复制到{}】", source.getSkuCode(), newGoodsItem.getSkuCode()),
              null);
        });

    checkUpdateItemLaunchStatus(source.getItemId());

    return SingleResponse.of(true);
  }

  private static final String URL_REGEX =
      "(https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";

  @Override
  public void douDianSync(List<DouDianSyncCmd> list) {
    log.info("【抖店】同步业务请求参数param:{}", JsonUtil.toJson(list));

    list.forEach(
        cmd -> {
          Long userId = UserContext.getUserId();
          ThreadUtil.execute(
              PoolEnum.COMMON_POOL,
              () -> {
                ThirdPlatformSync sync = buildSyncInfo(cmd, userId);
                iThirdPlatformSyncService.save(sync);

                if (ThirdPlatformSyncState.ERROR.equals(sync.getState())) {
                  return;
                }

                try {
                  Resp<DouDianSyncBO> resp =
                      douDianTemplate.productEdit(
                          cmd.getItemId(), cmd.getProductId(), cmd.getSyncType());
                  if (resp.getIsSuccess()) {
                    sync.setError(resp.getError());
                    sync.setItemCode(resp.getSyncCode());
                    iThirdPlatformSyncService.updateById(sync);
                  } else {
                    sync.setState(ThirdPlatformSyncState.ERROR);
                    sync.setError(resp.getError());
                    iThirdPlatformSyncService.updateById(sync);
                  }
                } catch (Exception e) {
                  log.error("发起抖店同步操作异常", e);
                  sync.setState(ThirdPlatformSyncState.ERROR);
                  sync.setError("发起抖店同步操作异常");
                  iThirdPlatformSyncService.updateById(sync);
                }
              });
        });
  }

  @Override
  public SingleResponse<NewGoodsPrincipalsInfo> getNewGoodsPrincipalsInfo(Long itemId) {
    List<NewGoodsPrincipalsInfoPO> info =
        newGoodsMapper.getNewGoodsPrincipalsInfo(Collections.singletonList(itemId));
    if (info.isEmpty()) {
      return SingleResponse.of(NewGoodsPrincipalsInfo.empty());
    }
    return SingleResponse.of(NewGoodsAssembler.INST.mapNewGoodsPrincipalsInfo(info.get(0)));
  }

  @Override
  public MultiResponse<NewGoodsPrincipalsInfo> getNewGoodsPrincipalsInfoBatch(
      Collection<Long> itemIds) {
    if (CollectionUtil.isEmpty(itemIds)) {
      return MultiResponse.of(Collections.emptyList());
    }
    List<NewGoodsPrincipalsInfoPO> info = newGoodsMapper.getNewGoodsPrincipalsInfo(itemIds);
    if (info.isEmpty()) {
      return MultiResponse.of(Collections.emptyList());
    }
    return MultiResponse.of(
        info.stream()
            .map(NewGoodsAssembler.INST::mapNewGoodsPrincipalsInfo)
            .collect(Collectors.toList()));
  }

  public ThirdPlatformSync buildSyncInfo(DouDianSyncCmd cmd, Long userId) {
    boolean isOk = true;
    String error = "";

    ThirdPlatformSync sync = new ThirdPlatformSync();
    sync.setPlatformType(PlatformType.DOU_DIAN);
    sync.setItemId(cmd.getItemId());
    sync.setItemCode(cmd.getItemCode());
    sync.setName(cmd.getName());
    sync.setSkuCount(cmd.getSkuCount());
    sync.setCreatedUid(userId);
    sync.setEnv(ApplicationContextUtil.getActiveProfile());

    Item item = itemService.getById(cmd.getItemId());
    if (item.getLaunchStatus() <= 4) {
      isOk = false;
      error = "商品状态不符合同步条件";
    }

    if (!cmd.getSyncType().equals(ThirdPlatformSyncType.DOUDIAN_NEW.getValue())
        && !cmd.getSyncType().equals(ThirdPlatformSyncType.DOUDIAN_EDIT.getValue())) {
      isOk = false;
      error = "同步参数syncType非法，请传31或者32";
    }
    Optional<ThirdPlatformSyncType> enumOptByValue =
        IEnum.getEnumOptByValue(ThirdPlatformSyncType.class, cmd.getSyncType());
    if (!enumOptByValue.isPresent()) {
      isOk = false;
      error = "同步参数syncType非法";
    } else {
      sync.setType(enumOptByValue.get());
    }

    sync.setThirdLink(cmd.getDouDianUrl());
    boolean match = ReUtil.isMatch(URL_REGEX, cmd.getDouDianUrl());
    if (!match) {
      isOk = false;
      error = "抖店链接非法，url校验错误";
    }
    try {
      long productId = getDouYinProductIdFromUrl(cmd.getDouDianUrl());
      cmd.setProductId(productId);
    } catch (Exception e) {
      isOk = false;
      error = "抖店链接非法，无法解析出产品id";
    }

    if (isOk) {
      sync.setState(ThirdPlatformSyncState.RUNNING);
    } else {
      sync.setState(ThirdPlatformSyncState.ERROR);
      sync.setError(error);
    }

    return sync;
  }

  private ThirdPlatformSync init(DouDianSyncCmd cmd) {
    ThirdPlatformSync sync = new ThirdPlatformSync();
    sync.setPlatformType(PlatformType.DOU_DIAN);
    sync.setItemId(cmd.getItemId());
    sync.setItemCode(cmd.getItemCode());
    sync.setState(ThirdPlatformSyncState.RUNNING);
    sync.setType(
        IEnum.getEnumOptByValue(ThirdPlatformSyncType.class, cmd.getSyncType())
            .orElseThrow(
                () -> ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "同步类型无效值")));
    sync.setThirdLink(cmd.getDouDianUrl());
    sync.setSkuCount(cmd.getSkuCount());
    return sync;
  }

  private static Long getDouYinProductIdFromUrl(String douYinUrl) throws Exception {
    // 示例：https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3571939372414721647&origin_type=604
    String[] split = douYinUrl.split("id=");
    String[] split1 = split[1].split("&");
    return Long.valueOf(split1[0]);
  }

  @Override
  public void updateById(NewGoods newGoods) {
    newGoodsService.updateById(newGoods);
  }

  @Override
  public Map<Integer, Integer> staticsItemCountByTimeAndStatus(
      Long startTime, Long endTime, List<ItemLaunchStatus> statusList) {
    if (CollectionUtil.isEmpty(statusList)) {
      return new HashMap<>(8);
    }
    List<Integer> valList =
        statusList.stream().map(ItemLaunchStatus::getValue).distinct().collect(Collectors.toList());
    return newGoodsMapper.staticsCountByStatus(startTime, endTime, valList);
  }

  @Override
  public Response down(NewGoodsDownRequest request) {
    final Item item = itemService.getById(request.getItemId());
    if (item == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品不存在");
    }
    ItemDrawerMergeItem itemDrawerMergeItem =
        itemDrawerMergeBizService.getItemDrawerMergeItem(item.getId());
    if (Boolean.TRUE.equals(request.getDownMergeItem()) && itemDrawerMergeItem != null) {
      mergeDown(request, itemDrawerMergeItem);
    } else {
      if (Arrays.asList(
              ItemLaunchStatus.TO_BE_SELECTED.getValue(),
              ItemLaunchStatus.NON_SELECTIVITY.getValue())
          .contains(item.getLaunchStatus())) {
        throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "商品未添加到上新计划");
      }
      if (Objects.equals(item.getLaunchStatus(), ItemLaunchStatus.DOWN.getValue())) {
        throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "商品状态已经是【已下架】");
      }
      if (Objects.equals(item.getLaunchStatus(), ItemLaunchStatus.TO_BE_AUDITED.getValue())) {
        itemDrawerAuditBizService.closeTask(ItemAuditType.ITEM_MATERIAL, item.getId());
      }
      item.setLaunchStatus(ItemLaunchStatus.DOWN.getValue());
      operateLogGateway.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.NEW_GOODS_SPU,
          item.getId(),
          "新品商品修改状态为【已下架】",
          null);
      if (!Objects.equals(item.getStatus(), ItemStatus.OUT.getValue())) {
        item.setStatus(ItemStatus.OUT.getValue());
        item.setDownFrameTime(request.getDownFrameTime());
        item.setDownFrameReason(request.getDownFrameReason());
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.ITEM_DOWN,
            item.getId(),
            "新品商品库【下架】，同步后端商品状态改为【已下架】",
            null);
      }
      itemService.updateById(item);
    }
    return Response.buildSuccess();
  }

  private void mergeDown(NewGoodsDownRequest request, ItemDrawerMergeItem itemDrawerMergeItem) {
    List<ItemDrawerMergeItem> itemDrawerMergeItems =
        itemDrawerMergeBizService.getItemDrawerMergeItemListByMergeId(
            itemDrawerMergeItem.getMergeId());
    List<Long> itemIds =
        itemDrawerMergeItems.stream()
            .map(ItemDrawerMergeItem::getItemId)
            .collect(Collectors.toList());
    List<Item> items = itemService.listByIds(itemIds);
    for (Item itemInMerge : items) {
      if (Objects.equals(
          itemInMerge.getLaunchStatus(), ItemLaunchStatus.TO_BE_AUDITED.getValue())) {
        itemDrawerAuditBizService.closeTask(ItemAuditType.ITEM_MATERIAL, itemInMerge.getId());
      }
      if (!Arrays.asList(
              ItemLaunchStatus.DOWN.getValue(),
              ItemLaunchStatus.TO_BE_SELECTED.getValue(),
              ItemLaunchStatus.NON_SELECTIVITY.getValue())
          .contains(itemInMerge.getLaunchStatus())) {
        itemInMerge.setLaunchStatus(ItemLaunchStatus.DOWN.getValue());
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.NEW_GOODS_SPU,
            itemInMerge.getId(),
            "新品商品修改状态为【已下架】",
            null);
      }
      if (!Objects.equals(itemInMerge.getStatus(), ItemStatus.OUT.getValue())) {
        itemInMerge.setStatus(ItemStatus.OUT.getValue());
        itemInMerge.setDownFrameTime(request.getDownFrameTime());
        itemInMerge.setDownFrameReason(request.getDownFrameReason());
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.ITEM_DOWN,
            itemInMerge.getId(),
            "新品商品库【下架】，同步后端商品状态改为【已下架】",
            null);
      }
      itemService.updateById(itemInMerge);
    }
  }

  public static long getTimestampAt10AM(LocalDate date) {
    LocalDateTime tenAM = date.atTime(10, 0);
    ZonedDateTime zonedDateTime = tenAM.atZone(ZoneId.systemDefault());
    return zonedDateTime.toEpochSecond();
  }

  @TransactionalEventListener(fallbackExecution = true)
  public void onItemUpdate(SaveEvent<Item> event) {
    log.info("新品商品监听后端商品变动：{}", event);

    final Item item = event.getModel();
    final List<ItemSku> itemSkus = iItemSkuService.selectListByItemId(item.getId());
    if (itemSkus.isEmpty()) {
      return;
    }
    final List<String> skuCodeList =
        itemSkus.stream().map(ItemSku::getSkuCode).collect(Collectors.toList());
    final List<NewGoods> newGoodsList = newGoodsService.selectBatchBySkuCodes(skuCodeList);
    final ArrayList<OperateLog> operateLogs = new ArrayList<>();
    final ArrayList<NewGoods> newGoodsUpdateModelList = new ArrayList<>();
    for (ItemSku itemSku : itemSkus) {
      for (NewGoods newGoods : newGoodsList) {
        if (newGoods.getSkuCode().equals(itemSku.getSkuCode())) {
          if (itemSku.getSalePrice() != null
              && itemSku.getSalePrice().compareTo(newGoods.getDailyPrice()) != 0) {
            final NewGoods newGoodsUpdateModel = new NewGoods();
            newGoodsUpdateModel.setId(newGoods.getId());
            newGoodsUpdateModel.setDailyPrice(itemSku.getSalePrice());
            newGoodsUpdateModelList.add(newGoodsUpdateModel);

            final OperateLog operateLog = new OperateLog();
            operateLog.setTargetType(OperateLogTarget.NEW_GOODS);
            operateLog.setTargetId(String.valueOf(newGoods.getId()));
            operateLog.setMsg("同步后端商品变更");
            final ArrayList<ChangePropertyObj> changePropertyObjs =
                Lists.newArrayList(
                    new ChangePropertyObj(
                        "产品日销价", itemSku.getSalePrice(), newGoods.getDailyPrice()));
            operateLog.setData(JsonUtil.toJson(changePropertyObjs));
            operateLogs.add(operateLog);
            break;
          }
        }
      }
      if (!newGoodsUpdateModelList.isEmpty()) {
        newGoodsService.updateBatchById(newGoodsUpdateModelList);
      }
      if (!operateLogs.isEmpty()) {
        operateLogGateway.batchAddOperateLog(operateLogs);
      }
    }
  }

  /**
   * 批量查询平台链接数据
   *
   * @param newGoodsGroupVOList 新品商品组列表
   * @return 商品抽屉平台链接
   */
  private List<ItemDrawerPlatformLink> batchQueryPlatformLinks(
          List<NewGoodsGroupVO> newGoodsGroupVOList) {
    if (CollUtil.isEmpty(newGoodsGroupVOList)) {
      return new ArrayList<>();
    }

    // 收集所有的 itemId
    Set<Long> itemIds =
            newGoodsGroupVOList.stream()
                    .map(group -> group.getSpu().getItemId())
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());

    if (CollUtil.isEmpty(itemIds)) {
      return new ArrayList<>();
    }

    // 批量查询平台商品信息
    return itemDrawerPlatformItemService
            .lambdaQuery()
            .in(ItemDrawerPlatformLink::getItemId, itemIds)
            .isNotNull(ItemDrawerPlatformLink::getLink)
            .ne(ItemDrawerPlatformLink::getLink, "")
            .list();
  }

  /**
   * 设置平台链接数据到 NewGoodsSheet
   *
   * @param newGoodsSheets 新品商品表格数据
   * @param newGoodsGroupVOList 新品商品组列表
   * @param itemDrawerPlatformLinks 平台链接集合
   */
  private void setPlatformLinksToSheets(
      List<NewGoodsSheet> newGoodsSheets,
      List<NewGoodsGroupVO> newGoodsGroupVOList,
      List<ItemDrawerPlatformLink> itemDrawerPlatformLinks) {
    if (CollUtil.isEmpty(newGoodsSheets) || CollUtil.isEmpty(newGoodsGroupVOList) || CollUtil.isEmpty(itemDrawerPlatformLinks)) {
      return;
    }

    Map<Long, Map<Platform, List<ItemDrawerPlatformLink>>> itemPlatformLinksMap = itemDrawerPlatformLinks.stream()
            .collect(Collectors.groupingBy(ItemDrawerPlatformLink::getItemId,
                    Collectors.groupingBy(ItemDrawerPlatformLink::getPlatform)));
    // 创建 sheet 和 itemId 的对应关系
    int index = 0;
    for (NewGoodsGroupVO group : newGoodsGroupVOList) {
      NewGoodsSpuVO spu = group.getSpu();
      if (spu == null) {
        continue;
      }

      Long itemId = spu.getItemId();
      Map<Platform, List<ItemDrawerPlatformLink>> platformLinks = itemPlatformLinksMap.get(itemId);

      Function<Platform, String> functionGetLink = platform -> platformLinks.getOrDefault(platform, Collections.emptyList()).stream()
              .map(ItemDrawerPlatformLink::getLink).collect(Collectors.joining(";"));
      Function<Platform, String> functionGetId = platform -> platformLinks.getOrDefault(platform, Collections.emptyList()).stream()
              .map(ItemDrawerPlatformLink::getOuterItemId).collect(Collectors.joining(";"));

      if (platformLinks != null) {
        for (int i = 0; i < group.getSkuList().size(); i++) {
          if (index < newGoodsSheets.size()) {
            NewGoodsSheet sheet = newGoodsSheets.get(index);

            // 设置各平台链接
            sheet.setTaobaoLinks(functionGetLink.apply(Platform.TAOBAO));
            sheet.setTaobaoIds(functionGetId.apply(Platform.TAOBAO));
            sheet.setDoudianLinks(functionGetLink.apply(Platform.DOUYIN));
            sheet.setDoudianIds(functionGetId.apply(Platform.DOUYIN));
            sheet.setLaobashopLinks(functionGetLink.apply(Platform.LAOBASHOP));
            sheet.setLaobashopIds(functionGetId.apply(Platform.LAOBASHOP));
            sheet.setXiaohongshuLinks(functionGetLink.apply(Platform.XIAOHONGSHU));
            sheet.setXiaohongshuIds(functionGetId.apply(Platform.XIAOHONGSHU));
          }
          index++;
        }
      } else {
        // 如果没有平台链接数据，跳过对应的 SKU 数量
        index += group.getSkuList().size();
      }
    }
  }
}
