package com.daddylab.supplier.item.application.purchase.combinationPrice;

import static com.daddylab.supplier.item.application.purchase.combinationPrice.PriceType.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.daddylab.supplier.item.application.purchase.combinationPrice.sheet.CombinationStepPriceExcelSheet;
import com.daddylab.supplier.item.application.purchase.combinationPrice.sheet.CombinationStepPriceExcelVo;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.QuantityCombinedPriceBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.ExportSingleSkuRow;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.PurchasePriceReadExcelController;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.exportTask.ExportManager;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationStepPriceExcelMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PurchaseMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.format.ResolverStyle;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> up
 * @date 2023年04月04日 10:07 AM
 */
@Service
@Slf4j
public class CombinationPriceBizServiceImpl implements CombinationPriceBizService {

  @Autowired IPurchaseSingleSkuCombinationPriceService singleSkuCombinationPrice;

  @Autowired IPurchaseRandomSkuCombinationPriceService randomSkuCombinationPrice;

  @Resource IItemSkuService iItemSkuService;

  @Resource IItemService iItemService;

  @Resource
  @Qualifier("UserGatewayCacheImpl")
  private UserGateway userGateway;

  @Resource PurchaseMapper purchaseMapper;

  @Resource FileGateway fileGateway;

  @Resource IProviderService iProviderService;

  @Resource IBizLevelDivisionService bizLevelDivisionService;

  @Autowired
  PurchasePriceReadExcelController readExcelController;

  @Autowired ICombinationStepPriceExcelService iCombinationStepPriceExcelService;
  @Autowired CombinationStepPriceExcelMapper combinationStepPriceExcelMapper;
  @Autowired ExportManager exportManager;

  /**
   * 查询 跨sku,spu纬度的组合价
   *
   * @param query
   * @return
   */
  public PageResponse<PurchasePriceVo> skuPricePage(PricePageQuery query) {
    if (query.getIsSingle()) {
      PageInfo<PurchaseSingleSkuCombinationPrice> pageInfo =
          PageHelper.startPage(query.getPageIndex(), query.getPageSize())
              .doSelectPageInfo(
                  () ->
                      singleSkuCombinationPrice
                          .lambdaQuery()
                          .eq(
                              Objects.nonNull(query.getPriceType()),
                              PurchaseSingleSkuCombinationPrice::getPriceType,
                              query.getPriceType())
                          .like(
                              StrUtil.isNotBlank(query.getSkuCode()),
                              PurchaseSingleSkuCombinationPrice::getCode,
                              query.getSkuCode())
                          .orderByDesc(PurchaseSingleSkuCombinationPrice::getId)
                          .list());
      List<PurchasePriceVo> priceVoList = getPriceVoList(pageInfo);
      return PageResponse.of(
          priceVoList, (int) pageInfo.getTotal(), pageInfo.getPageSize(), pageInfo.getPages());
    } else {
      PageInfo<PurchaseRandomSkuCombinationPrice> pageInfo =
          PageHelper.startPage(query.getPageIndex(), query.getPageSize())
              .doSelectPageInfo(
                  () ->
                      randomSkuCombinationPrice
                          .lambdaQuery()
                          .eq(
                              Objects.nonNull(query.getPriceType()),
                              PurchaseRandomSkuCombinationPrice::getPriceType,
                              query.getPriceType())
                          .like(
                              StrUtil.isNotBlank(query.getSkuCode()),
                              PurchaseRandomSkuCombinationPrice::getCode,
                              query.getSkuCode())
                          .orderByDesc(PurchaseRandomSkuCombinationPrice::getId)
                          .list());
      List<PurchasePriceVo> priceVoList = getPriceVoList(pageInfo);
      return PageResponse.of(
          priceVoList, (int) pageInfo.getTotal(), pageInfo.getPageSize(), pageInfo.getPages());
    }
  }

  private List<PurchasePriceVo> getPriceVoList(PageInfo<? extends SkuCombinationPrice> pageInfo) {
    return pageInfo.getList().stream()
        .map(
            val -> {
              PurchasePriceVo vo = new PurchasePriceVo();
              vo.setId(val.getId());
              vo.setCode(val.getCode());
              vo.setPriceDetailDtoList(
                  JsonUtil.parse(val.getPriceInfo(), new TypeReference<List<PriceDetailDto>>() {}));
              vo.setPlatform(val.getPlatform());
              vo.setPriceType(val.getPriceType());
              vo.setStartTime(val.getStartTime());
              vo.setEndTime(val.getEndTime());
              return vo;
            })
        .collect(Collectors.toList());
  }

  // -------------------------------------------------------

  public SingleResponse<Boolean> updateSkuPrice(PriceParam param) {
    Assert.notNull(param.getId(), "价格Id不得为空");
    long count =
        param.getPriceInfo().stream().filter(val -> StrUtil.isBlank(val.getCode())).count();
    Assert.isTrue(count == 0, "所有价格详情数据编码不得为空");

    if (param.getIsSingle()) {
      singleSkuCombinationPrice
          .lambdaUpdate()
          .set(
              PurchaseSingleSkuCombinationPrice::getPriceInfo,
              JsonUtil.toJson(param.getPriceInfo()))
          .set(PurchaseSingleSkuCombinationPrice::getStartTime, param.getStartTime())
          .set(PurchaseSingleSkuCombinationPrice::getEndTime, param.getEndTime())
          .set(PurchaseSingleSkuCombinationPrice::getPriceType, param.getPriceType())
          .eq(PurchaseSingleSkuCombinationPrice::getId, param.getId())
          .update();
    } else {
      randomSkuCombinationPrice
          .lambdaUpdate()
          .set(
              PurchaseRandomSkuCombinationPrice::getPriceInfo,
              JsonUtil.toJson(param.getPriceInfo()))
          .set(PurchaseRandomSkuCombinationPrice::getStartTime, param.getStartTime())
          .set(PurchaseRandomSkuCombinationPrice::getEndTime, param.getEndTime())
          .set(PurchaseRandomSkuCombinationPrice::getPriceType, param.getPriceType())
          .eq(PurchaseRandomSkuCombinationPrice::getId, param.getId())
          .update();
    }
    return SingleResponse.of(true);
  }

  // -----------------------------------------------------------

  public SingleResponse<Boolean> deleteSkuPrice(Long id, Boolean isSingle) {
    if (isSingle) {
      singleSkuCombinationPrice.removeByIdWithTime(id);
      return SingleResponse.of(true);
    } else {
      randomSkuCombinationPrice.removeByIdWithTime(id);
      return SingleResponse.of(true);
    }
  }

  // ------------------------------------------- 增加 ----------------

  public SingleResponse<Boolean> importExcel(
      MultipartFile file, Integer priceType, Boolean isSingle) {
    if (isSingle) {
      readExcelController.singleSku(file, priceType);
    } else {
      readExcelController.randomSku(file, priceType);
    }
    return SingleResponse.of(Boolean.TRUE);
  }

  private static final String REGEX = "^[a-zA-Z0-9]+(,[a-zA-Z0-9]+)*$";

  public SingleResponse<Boolean> savePrice(PriceParam priceParam) {
    Assert.hasText(priceParam.getCode(), "sku编码不得为空");
    Boolean isSingle = priceParam.getIsSingle();
    if (isSingle) {
      PurchaseSingleSkuCombinationPrice price = new PurchaseSingleSkuCombinationPrice();
      price.setCode(priceParam.getCode());
      List<PriceDetailDto> priceInfo = priceParam.getPriceInfo();
      for (int i = 0; i < priceInfo.size(); i++) {
        priceInfo.get(i).setCode(priceParam.getCode() + "P" + i);
      }
      price.setPriceInfo(JsonUtil.toJson(priceInfo));
      price.setSource(1);
      price.setPlatform(priceParam.getPlatformType());
      price.setStartTime(priceParam.getStartTime());
      price.setEndTime(priceParam.getEndTime());
      price.setPriceType(priceParam.getPriceType());

      singleSkuCombinationPrice.save(price);
    } else {
      Assert.isTrue(priceParam.getCode().matches(REGEX), "spu组合价格的sku编码请用英文逗号隔开");
      PurchaseRandomSkuCombinationPrice price = new PurchaseRandomSkuCombinationPrice();
      price.setGroupCode(RandomUtil.randomString(4));
      price.setCode(StrUtil.join("|", Arrays.asList(priceParam.getCode().split(","))));
      List<PriceDetailDto> priceInfo = priceParam.getPriceInfo();
      String priceCode = RandomUtil.randomString(6);
      for (int i = 0; i < priceInfo.size(); i++) {
        priceInfo.get(i).setCode(priceCode + "P" + i);
      }
      price.setPriceInfo(JsonUtil.toJson(priceInfo));
      price.setPlatform(priceParam.getPlatformType());
      price.setStartTime(priceParam.getStartTime());
      price.setEndTime(priceParam.getEndTime());
      price.setPriceType(priceParam.getPriceType());

      randomSkuCombinationPrice.save(price);
    }

    return SingleResponse.of(Boolean.TRUE);
  }

  // ---------------------- 我是分割线 ------------------------------

  @Resource IPurchaseService iPurchaseService;

  @Resource IExportTaskService exportTaskService;

  private static final Integer DAILY_PRICE_TYPE = 1;
  private static final Integer ACTIVITY_PRICE_TYPE = 2;

  /** 日常价格的活动时间。2022-01-01 ～ 2032-01-01; */
  private static final Long DAILY_START_TIME = 1640966400L;

  private static final Long DAILY_END_TIME = 1956499200L;

  @Override
  public PageResponse<CombinationStepPriceExcelVo> pageQuery2(
      PurchaseStepPricePageQuery pageQuery) {
    final PageInfo<CombinationStepPriceExcelVo> voList =
        PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
            .doSelectPageInfo(() -> combinationStepPriceExcelMapper.queryPage(pageQuery));
    if (voList.getTotal() == 0) {
      return PageResponse.of(
          Collections.emptyList(), 0, pageQuery.getPageSize(), voList.getPages());
    }

    final Set<Long> itemIdSet =
        voList.getList().stream()
            .map(CombinationStepPriceExcelVo::getItemId)
            .collect(Collectors.toSet());
    final Map<Long, List<CorpBizTypeDTO>> bizMap = bizLevelDivisionService.queryByItemId(itemIdSet);
    for (CombinationStepPriceExcelVo vo : voList.getList()) {
      vo.setCorpBizType(bizMap.get(vo.getItemId()));
      String priceCategory =
          (Objects.nonNull(vo.getActivityStartTime()) && Objects.nonNull(vo.getActivityEndTime()))
              ? "活动阶梯供价"
              : "日常阶梯供价";
      vo.setPriceCategory(priceCategory);
    }
    return PageResponse.of(
        voList.getList(), (int) voList.getTotal(), pageQuery.getPageSize(), voList.getPages());
  }

  @Override
  public void historyDataHandler() {
    List<CombinationStepPriceExcel> excelList = new LinkedList<>();

    final List<PurchaseSingleSkuCombinationPrice> list =
        singleSkuCombinationPrice.lambdaQuery().list();
    for (PurchaseSingleSkuCombinationPrice price : list) {
      try {
        final String priceInfo = price.getPriceInfo();
        List<QuantityCombinedPriceBO> boList =
            JsonUtil.parseList(priceInfo, QuantityCombinedPriceBO.class);
        for (QuantityCombinedPriceBO bo : boList) {
          CombinationStepPriceExcel excel = new CombinationStepPriceExcel();
          excel.setSkuCode(price.getCode());
          excel.setDeliveredQuantity(bo.getQuantity().toString());
          excel.setDiscountPriceCost(bo.getPrice().toString());
          excel.setDiscountContent("历史数据");
          if (price.getPriceType() == 2) {
            excel.setActivityStartTime(price.getStartTime());
            excel.setActivityEndTime(price.getEndTime());
          }
          excelList.add(excel);
        }
      } catch (Exception e) {
        // ignore
      }
    }

    final List<PurchaseRandomSkuCombinationPrice> randomList =
        randomSkuCombinationPrice.lambdaQuery().list();
    for (PurchaseRandomSkuCombinationPrice price : randomList) {
      try {
        final String priceInfo = price.getPriceInfo();
        List<QuantityCombinedPriceBO> boList =
            JsonUtil.parseList(priceInfo, QuantityCombinedPriceBO.class);
        for (String cc : price.getCode().split("\\|")) {
          for (QuantityCombinedPriceBO bo : boList) {
            CombinationStepPriceExcel excel = new CombinationStepPriceExcel();
            excel.setSpuCode(price.getGroupCode());
            excel.setSkuCode(cc);
            excel.setDeliveredQuantity(bo.getQuantity().toString());
            excel.setDiscountPriceCost(bo.getPrice().toString());
            excel.setDiscountContent("历史数据");
            if (price.getPriceType() == 2) {
              excel.setActivityStartTime(price.getStartTime());
              excel.setActivityEndTime(price.getEndTime());
            }
            excelList.add(excel);
          }
        }
      } catch (Exception e) {
        // ignore
      }
    }

    iCombinationStepPriceExcelService.saveBatch(excelList);
  }

  @Override
  @Transactional
  public Response save(CombinationStepPriceExcel excel) {

    iCombinationStepPriceExcelService.saveOrUpdate(excel);

    saveHandler(excel);

    return Response.buildSuccess();
  }

  private void saveHandler(CombinationStepPriceExcel excel) {
    final PriceType priceType = typeJudgment(excel);

    if (priceType.equals(SKU_ACTIVITY) || priceType.equals(SKU_NORMAL)) {
      List<CombinationStepPriceExcel> list;
      if (priceType.equals(SKU_ACTIVITY)) {
        // 删除原本的数据
        singleSkuCombinationPrice
            .lambdaUpdate()
            .eq(PurchaseSingleSkuCombinationPrice::getCode, excel.getSkuCode())
            .eq(PurchaseSingleSkuCombinationPrice::getPriceType, 2)
            .remove();

        list =
            iCombinationStepPriceExcelService
                .lambdaQuery()
                .eq(CombinationStepPriceExcel::getSkuCode, excel.getSkuCode())
                .ne(CombinationStepPriceExcel::getActivityStartTime, "")
                .ne(CombinationStepPriceExcel::getActivityEndTime, "")
                .list();
        if (CollUtil.isNotEmpty(list)) {
          final List<ExportSingleSkuRow> rowList =
              list.stream()
                  .map(
                      val -> {
                        ExportSingleSkuRow row = new ExportSingleSkuRow();
                        row.setSkuCode(excel.getSkuCode());
                        row.setQuantity(Integer.parseInt(excel.getDeliveredQuantity()));
                        row.setPrice(excel.getDiscountPriceCost());
                        row.setActivityStartTime(
                            DateUtil.parseTimeStamp(
                                excel.getActivityStartTime(), DateUtil.DEFAULT_FORMAT));
                        row.setActivityEndTime(
                            DateUtil.parseTimeStamp(
                                excel.getActivityEndTime(), DateUtil.DEFAULT_FORMAT));
                        return row;
                      })
                  .collect(Collectors.toList());
          singleSkuCombinationPrice.save(
              readExcelController.singleHandler0(rowList, excel.getSkuCode(), 2));
        }
      }
      if (priceType.equals(SKU_NORMAL)) {
        singleSkuCombinationPrice
            .lambdaUpdate()
            .eq(PurchaseSingleSkuCombinationPrice::getCode, excel.getSkuCode())
            .eq(PurchaseSingleSkuCombinationPrice::getPriceType, 1)
            .eq(PurchaseSingleSkuCombinationPrice::getEndTime, excel.getActivityEndTime())
            .remove();

        list =
            iCombinationStepPriceExcelService
                .lambdaQuery()
                .eq(CombinationStepPriceExcel::getSkuCode, excel.getSkuCode())
                .list();
        if (CollUtil.isNotEmpty(list)) {
          final List<ExportSingleSkuRow> rowList =
              list.stream()
                  .map(
                      val -> {
                        ExportSingleSkuRow row = new ExportSingleSkuRow();
                        row.setSkuCode(excel.getSkuCode());
                        row.setQuantity(Integer.parseInt(excel.getDeliveredQuantity()));
                        row.setPrice(excel.getDiscountPriceCost());
                        return row;
                      })
                  .collect(Collectors.toList());
          singleSkuCombinationPrice.save(
              readExcelController.singleHandler0(rowList, excel.getSkuCode(), 1));
        }
      }
    }

    if (priceType.equals(SPU_ACTIVITY) || priceType.equals(SPU_NORMAL)) {
      List<CombinationStepPriceExcel> list;
      if (priceType.equals(SPU_ACTIVITY)) {
        randomSkuCombinationPrice
            .lambdaUpdate()
            .eq(PurchaseRandomSkuCombinationPrice::getCode, excel.getSkuCode())
            .eq(PurchaseRandomSkuCombinationPrice::getPriceType, 2)
            .eq(PurchaseRandomSkuCombinationPrice::getStartTime, excel.getActivityStartTime())
            .eq(PurchaseRandomSkuCombinationPrice::getEndTime, excel.getActivityEndTime())
            .remove();
        list =
            iCombinationStepPriceExcelService
                .lambdaQuery()
                .eq(CombinationStepPriceExcel::getSpuCode, excel.getSpuCode())
                .ne(CombinationStepPriceExcel::getActivityStartTime, "")
                .ne(CombinationStepPriceExcel::getActivityEndTime, "")
                .list();
        if (CollUtil.isNotEmpty(list)) {
          final List<ExportSingleSkuRow> rowList =
              list.stream()
                  .map(
                      val -> {
                        ExportSingleSkuRow row = new ExportSingleSkuRow();
                        row.setSpuCode(excel.getSpuCode());
                        row.setSkuCode(excel.getSkuCode());
                        row.setQuantity(Integer.parseInt(excel.getDeliveredQuantity()));
                        row.setPrice(excel.getDiscountPriceCost());
                        row.setActivityStartTime(
                            DateUtil.parseTimeStamp(
                                excel.getActivityStartTime(), DateUtil.DEFAULT_FORMAT));
                        row.setActivityEndTime(
                            DateUtil.parseTimeStamp(
                                excel.getActivityEndTime(), DateUtil.DEFAULT_FORMAT));
                        return row;
                      })
                  .collect(Collectors.toList());
          randomSkuCombinationPrice.save(
              readExcelController.randomHandler0(rowList, excel.getSpuCode(), 2));
        }
      }
      if (priceType.equals(SPU_NORMAL)) {
        randomSkuCombinationPrice
            .lambdaUpdate()
            .eq(PurchaseRandomSkuCombinationPrice::getCode, excel.getSpuCode())
            .eq(PurchaseRandomSkuCombinationPrice::getPriceType, 1)
            .remove();
        list =
            iCombinationStepPriceExcelService
                .lambdaQuery()
                .eq(CombinationStepPriceExcel::getSpuCode, excel.getSpuCode())
                .list();
        if (CollUtil.isNotEmpty(list)) {
          final List<ExportSingleSkuRow> rowList =
              list.stream()
                  .map(
                      val -> {
                        ExportSingleSkuRow row = new ExportSingleSkuRow();
                        row.setSpuCode(excel.getSpuCode());
                        row.setSkuCode(excel.getSkuCode());
                        row.setQuantity(Integer.parseInt(excel.getDeliveredQuantity()));
                        row.setPrice(excel.getDiscountPriceCost());
                        return row;
                      })
                  .collect(Collectors.toList());
          randomSkuCombinationPrice.save(
              readExcelController.randomHandler0(rowList, excel.getSpuCode(), 1));
        }
      }
    }
  }

  public static boolean isValidDateTimeFormat(String dateTimeStr) {
    if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
      return true;
    }

    try {
      final DateTimeFormatter dateTimeFormatter =
          DateTimeFormatter.ofPattern(DateUtil.DEFAULT_FORMAT)
              .withResolverStyle(ResolverStyle.STRICT);
      LocalDateTime.parse(dateTimeStr, dateTimeFormatter);
      return true;
    } catch (DateTimeParseException e) {
      return false;
    }
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public Response delete(Long id) {
    final CombinationStepPriceExcel excel = iCombinationStepPriceExcelService.getById(id);
    Assert.notNull(excel, "id非法，请联系开发人员");
    iCombinationStepPriceExcelService.removeById(id);

    final PriceType priceType = typeJudgment(excel);
    if (priceType.equals(SKU_ACTIVITY) || priceType.equals(SKU_NORMAL)) {
      final List<PurchaseSingleSkuCombinationPrice> list =
          singleSkuCombinationPrice
              .lambdaQuery()
              .eq(PurchaseSingleSkuCombinationPrice::getCode, excel.getSkuCode())
              .eq(PurchaseSingleSkuCombinationPrice::getStartTime, excel.getActivityStartTime())
              .eq(PurchaseSingleSkuCombinationPrice::getEndTime, excel.getActivityEndTime())
              .list();
      if (!list.isEmpty()) {
        singleSkuCombinationPrice.removeByIds(
                list.stream().map(PurchaseSingleSkuCombinationPrice::getId).collect(Collectors.toSet()));
      }
    }
    if (priceType.equals(SPU_ACTIVITY) || priceType.equals(SPU_NORMAL)) {
      final List<PurchaseRandomSkuCombinationPrice> list =
          randomSkuCombinationPrice
              .lambdaQuery()
              .like(PurchaseRandomSkuCombinationPrice::getCode, excel.getSkuCode())
              .eq(PurchaseRandomSkuCombinationPrice::getStartTime, excel.getActivityStartTime())
              .eq(PurchaseRandomSkuCombinationPrice::getEndTime, excel.getActivityEndTime())
              .list();

      if (!list.isEmpty()) {
        for (PurchaseRandomSkuCombinationPrice price : list) {
          final String code = price.getCode();
          final String newCode =
                  Stream.of(code.split("\\|"))
                          .filter(val -> !val.equals(excel.getSkuCode()))
                          .collect(Collectors.joining("|"));
          price.setCode(newCode);
          randomSkuCombinationPrice.updateById(price);
        }
      }

    }

    return Response.buildSuccess();
  }

  private PriceType typeJudgment(CombinationStepPriceExcel excel) {
    if (StringUtils.isBlank(excel.getSpuCode())) {
      if (Objects.nonNull(excel.getActivityStartTime())
          && Objects.nonNull(excel.getActivityEndTime())) {
        return SKU_ACTIVITY;
      } else {
        return SKU_NORMAL;
      }
    } else {
      if (Objects.nonNull(excel.getActivityStartTime())
          && Objects.nonNull(excel.getActivityEndTime())) {
        return SPU_ACTIVITY;
      } else {
        return SPU_NORMAL;
      }
    }
  }

  @Override
  public Response importExcel2(MultipartFile file) {
    // 1活动SKU多件供价。2活动SPU多件供价。3日常SKU多件供价。4日常SPU多件供价。

    List<CombinationStepPriceExcelSheet> sheetList;
    try {
      sheetList =
          EasyExcel.read(file.getInputStream())
              .headRowNumber(1)
              .head(CombinationStepPriceExcelSheet.class)
              .sheet()
              .doReadSync();
    } catch (IOException e) {
      log.error("importExcel2导入采购阶梯价，读取导入文件异常", e);
      throw ExceptionPlusFactory.bizException("导入采购阶梯价失败，excel文件解析失败");
    }
    Assert.state(CollUtil.isNotEmpty(sheetList), "文件数据为空，无法导入");

    final List<CombinationStepPriceExcel> excelList =
        sheetList.stream()
            .map(
                val -> {
                  CombinationStepPriceExcel excel = new CombinationStepPriceExcel();
                  excel.setSpuCode(val.getSpu());
                  excel.setSkuCode(val.getSkuCode());
                  excel.setProductName(val.getProductName());
                  excel.setProductSpec(val.getProductSpec());
                  excel.setProductSpecQuantity(val.getProductSpecQuantity());
                  excel.setIsActivityOnly(val.getIsActivityOnly());
                  excel.setRegularPrice(val.getRegularPrice());
                  excel.setFactoryOrWarehouse(val.getFactoryOrWarehouse());
                  excel.setSupplier(val.getSupplier());
                  excel.setPurchaser(val.getPurchaser());
                  excel.setDiscountType(val.getDiscountType());
                  excel.setPlatformName(val.getPlatformName());
                  excel.setPriceType(val.getPriceType());

                  final boolean b1 = isValidDateTimeFormat(val.getActivityStartTime());
                  Assert.state(b1, "活动开始时间格式不对，SKU编码：" + val.getSkuCode());
                  final boolean b2 = isValidDateTimeFormat(val.getActivityEndTime());
                  Assert.state(b2, "活动结束时间格式不对，SKU编码：" + val.getSkuCode());

                  excel.setActivityStartTime(DateUtil.parseTime(val.getActivityStartTime()));
                  excel.setActivityEndTime(DateUtil.parseTime(val.getActivityEndTime()));
                  excel.setOrderQuantity(val.getOrderQuantity());
                  excel.setDeliveredQuantity(val.getDeliveredQuantity());
                  excel.setDiscountPriceCost(val.getDiscountPriceCost());
                  excel.setDiscountQuantity(val.getDiscountQuantity());
                  excel.setDiscountContent(val.getDiscountContent());
                  excel.setRemark(val.getRemark());
                  return excel;
                })
            .collect(Collectors.toList());

    final CombinationStepPriceExcel combinationStepPriceExcel = excelList.get(0);
    PriceType priceType = typeJudgment(combinationStepPriceExcel);
    if (priceType.equals(SKU_ACTIVITY)) {
      readExcelController.singleSku(file, 2);
    } else if (priceType.equals(SPU_ACTIVITY)) {
      readExcelController.randomSku(file, 2);
    } else if (priceType.equals(SKU_NORMAL)) {
      readExcelController.singleSku(file, 1);
    } else if (priceType.equals(SPU_NORMAL)) {
      readExcelController.randomSku(file, 1);
    } else {
      throw ExceptionPlusFactory.bizException("导入采购阶梯价，价格类型不支持");
    }

    if (CollUtil.isNotEmpty(excelList)) {
      iCombinationStepPriceExcelService.saveBatch(excelList);
    }

    return Response.buildSuccess();
  }

  //  @Override
  //  public Response export(PurchaseStepPricePageQuery pageQuery) {
  //    ExportTask exportTask = ExportTask.initTask("采购阶梯价格_", ExportTaskType.PURCHASE_STEP_PRICE);
  //    exportTaskService.save(exportTask);
  //
  //    ThreadUtil.execute(
  //        PoolEnum.COMMON_POOL,
  //        () -> {
  //          java.io.File excelFile = null;
  //          try {
  //            pageQuery.setPageSize(9999);
  //            pageQuery.setPageIndex(1);
  //            PageResponse<PurchaseVo> purchaseVoPageResponse = null;
  //            //            PageResponse<PurchaseVo> purchaseVoPageResponse =
  // pageQuery(pageQuery);
  //            List<PurchaseVo> data = purchaseVoPageResponse.getData();
  //            List<PurchaseStepExportSheet> purchaseStepExportSheets =
  //                PurchaseTransMapper.INSTANCE.purchaseVoToStepSheets(data);
  //            excelFile = new java.io.File(RandomUtil.randomString(8) + ".xlsx");
  //            ExcelWriter excelWriter =
  //                EasyExcel.write(excelFile, PurchaseStepExportSheet.class).build();
  //            WriteSheet writeSheet =
  //                EasyExcel.writerSheet(0).head(PurchaseStepExportSheet.class).build();
  //            excelWriter.write(purchaseStepExportSheets, writeSheet);
  //            excelWriter.finish();
  //
  //            UploadFileAction action = UploadFileAction.ofFile(excelFile);
  //            String url = fileGateway.uploadFile(action).getUrl();
  //
  //            exportTask.setStatus(ExportTaskStatus.SUCCESS);
  //            exportTask.setDownloadUrl(url);
  //          } catch (Exception e) {
  //            log.error("导出采购阶梯价格异常", e);
  //            exportTask.setStatus(ExportTaskStatus.FAIL);
  //          } finally {
  //            exportTaskService.updateById(exportTask);
  //            FileUtil.del(excelFile);
  //          }
  //        });
  //
  //    return Response.buildSuccess();
  //  }

  @Override
  public Response export2(PurchaseStepPricePageQuery pageQuery) {

    exportManager.export(
        ExportTaskType.PURCHASE_STEP_PRICE,
        CombinationStepPriceExcelVo.class,
        pageIndex -> {
          pageQuery.setPageIndex(pageIndex);
          return export0(pageQuery);
        });

    return Response.buildSuccess();
  }

  private PageResponse<CombinationStepPriceExcelVo> export0(PurchaseStepPricePageQuery pageQuery) {
    pageQuery.setPageSize(100);
    final PageResponse<CombinationStepPriceExcelVo> response = pageQuery2(pageQuery);
    final List<CombinationStepPriceExcelVo> data = response.getData();
    if (CollectionUtils.isEmpty(data)) {
      ResponseFactory.emptyPage();
    }

    final Set<Long> itemIdSet =
        data.stream().map(CombinationStepPriceExcelVo::getItemId).collect(Collectors.toSet());
    final Map<Long, String> bizMap = bizLevelDivisionService.queryBizTypeByItemId(itemIdSet);

    for (CombinationStepPriceExcelVo datum : data) {
      if (Objects.nonNull(datum.getActivityStartTime())) {
        datum.setActivityStartTimeStr(DateUtil.format(datum.getActivityStartTime()));
      }
      if (Objects.nonNull(datum.getActivityEndTime())) {
        datum.setActivityEndTimeStr(DateUtil.format(datum.getActivityEndTime()));
      }
      datum.setCorpBizTypeStr(bizMap.get(datum.getItemId()));
    }

    return PageResponse.of(
        data, response.getTotalCount(), pageQuery.getPageSize(), pageQuery.getPageIndex());
  }

  public static void main(String[] args) {
    String ss =
        "[{\"code\":\"100013301P0\",\"quantity\":1,\"price\":27.00},{\"code\":\"100013301P1\",\"quantity\":2,\"price\":47.20},{\"code\":\"100013301P2\",\"quantity\":3,\"price\":70.80}]";
    List<QuantityCombinedPriceBO> oldPriceList =
        JsonUtil.parse(ss, new TypeReference<List<QuantityCombinedPriceBO>>() {});
    System.out.println(JsonUtil.toJson(oldPriceList));
  }
}
