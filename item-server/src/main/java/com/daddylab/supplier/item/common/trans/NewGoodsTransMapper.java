package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.application.bizLevelDivision.BizLevelDivisionConvert;
import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsCmd;
import com.daddylab.supplier.item.application.saleItem.dto.NewGoodsSheet;
import com.daddylab.supplier.item.application.saleItem.vo.*;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.NewGoods;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemAuditStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ItemLaunchStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.LaunchItemType;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.itemTrainingMaterials.ItemTrainingMaterialsStatus;
import com.daddylab.supplier.item.types.partner.PartnerItemType;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName NewGoodsTransMapper.java
 * @description
 * @createTime 2022年04月19日 17:36:00
 */
@Mapper(uses = {ScalarTransMapper.class}, imports = {DateUtil.class, IEnum.class, ItemLaunchStatus.class, BizLevelDivisionConvert.class})
public interface NewGoodsTransMapper {

    NewGoodsTransMapper INSTANCE = Mappers.getMapper(NewGoodsTransMapper.class);

    @Mapping(target = "singleBuyPrice", ignore = true)
    @Mapping(target = "shelfTime", ignore = true)
    @Mapping(target = "legalId", ignore = true)
    @Mapping(target = "skuCode", ignore = true)
    @Mapping(target = "principalId", ignore = true)
    @Mapping(target = "name", ignore = true)
    @Mapping(target = "itemId", ignore = true)
    @Mapping(target = "brandId", ignore = true)
    @Mapping(target = "updatedUid", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "isDel", ignore = true)
    @Mapping(target = "createdUid", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    NewGoods cmdToDo(NewGoodsCmd cmd);

    NewGoodsCmd doToCmd(NewGoods newGoods);

    default String statusToString(Integer statusVal) {
        return IEnum.getEnumOptByValue(ItemLaunchStatus.class, statusVal)
                .map(ItemLaunchStatus::getDesc).orElse("");
    }

    default String liveVerbalTrickStatusToString(Integer statusVal) {
        return IEnum.getEnumOptByValue(ItemAuditStatus.class, statusVal)
                .map(ItemAuditStatus::getDesc).orElse("");
    }

    default String enumToString(IEnum<?> enumValue) {
        if (enumValue == null) {
            return "";
        }
        return enumValue.getDesc();
    }

    default String shipmentTypeToString(Integer val) {
        switch (val) {
            case 2:
                return "仓库发货";
            case 1:
                return "工厂发货";
            default:
                return "";
        }
    }

    default String typeToDesc(Integer val) {
        return IEnum.getEnumOptByValue(LaunchItemType.class, val).map(LaunchItemType::getDesc)
                .orElse("");
    }

    default String partnerSysTypeToDesc(Integer val) {
        return IEnum.getEnumOptByValue(PartnerItemType.class, val).map(PartnerItemType::getDesc)
                .orElse("");
    }

    default String materialsStatusToDesc(Integer val) {
        return IEnum.getEnumOptByValue(ItemTrainingMaterialsStatus.class, val).map(ItemTrainingMaterialsStatus::getDesc)
                    .orElse("");
    }

    default String reverseBoolIntToString(Integer val) {
        return val != null && val > 0 ? "否" : "是";
    }

    default String decimal2String(BigDecimal bigDecimal) {
        if (Objects.isNull(bigDecimal)) {
            return "";
        }
        return NumberUtil.toStr(bigDecimal);
    }

    @Mappings({
            @Mapping(target = "shelfTime", expression = "java(mapShelfTime(newGoods))"),
            @Mapping(target = "activePeriod", expression = "java(mapActivePeriod(newGoods.getActivePeriodStart(), newGoods.getActivePeriodEnd(), newGoods.getIsLongTerm()))"),
            @Mapping(target = "status", expression = "java(statusToString(newGoods.getStatus()))"),
            @Mapping(target = "liveVerbalTrickStatus", expression = "java(liveVerbalTrickStatusToString(newGoods.getLiveVerbalTrickStatus()))"),
            @Mapping(target = "shipmentType", expression = "java(shipmentTypeToString(newGoods.getShipmentType()))"),
            @Mapping(target = "shipmentAging", expression = "java(reverseBoolIntToString(newGoods.getShipmentAging()))"),
//            @Mapping(target = "isReduce", ignore = true),
//            @Mapping(target = "isCoupon", expression = "java(reverseBoolIntToString(newGoods.getIsCoupon()))"),
            @Mapping(target = "noReason", expression = "java(reverseBoolIntToString(newGoods.getNoReason()))"),
            @Mapping(target = "itemType", expression = "java(typeToDesc(newGoods.getItemType()))"),
            @Mapping(target = "partnerSysType", expression = "java(partnerSysTypeToDesc(newGoods.getPartnerSysType()))"),
            @Mapping(target = "businessLine", expression = "java(getBusinessLineStr(newGoods.getBusinessLine()))"),
            @Mapping(target = "corpBizType", expression = "java(BizLevelDivisionConvert.INSTANCE.corpBizTypeListToDescStr(newGoods.getCorpBizType()))"),
            @Mapping(target = "costPrice", source = "costPrice"),
            @Mapping(target = "materialsStatus", expression = "java(materialsStatusToDesc(newGoods.getMaterialsStatus()))"),
            @Mapping(target = "downFrameTime", expression = "java(DateUtil.formatDate(newGoods.getDownFrameTime()))"),
            @Mapping(target = "tbTitle", source = "tbTitle")
    })
    NewGoodsSheet listVoToSheet(NewGoodsVo newGoods);

    default String getBusinessLineStr(Integer businessLine) {
        if (0 == businessLine) {
            return "电商";
        } else if (1 == businessLine) {
            return "老爸抽检";
        } else if (2 == businessLine) {
            return "绿色家装";
        } else if (3 == businessLine) {
            return "商家入驻";
        } else {
            return "其他";
        }
    }


    default String mapShelfTime(NewGoodsVo newGoods) {
        if (NumberUtil.isPositive(newGoods.getShelfTime())) {
            return StringUtil.format("{}({})", DateUtil.formatDate(newGoods.getShelfTime()),
                    newGoods.getPlanName());
        }
        return "";
    }

    default String mapActivePeriod(Long activePeriodStart, Long activePeriodEnd,
                                   Boolean isLongTerm) {
        return isLongTerm != null && isLongTerm ? "长期"
                : (DateUtil.formatDate(activePeriodStart) + "-" + DateUtil
                .formatDate(activePeriodEnd));
    }

    default String mapDafStaffVOToString(DadStaffVO value) {
        return value != null ? value.getNickname() : null;
    }

    default String mapDafStaffVOToString(List<DadStaffVO> value) {
        return value.stream().map(this::mapDafStaffVOToString).filter(Objects::nonNull)
                .collect(Collectors.joining(","));
    }


    
    default List<NewGoodsSheet> listVoToSheets(List<NewGoodsGroupVO> data) {
        ArrayList<NewGoodsSheet> newGoodsSheets = new ArrayList<>();
        for (NewGoodsGroupVO newGoodsGroupVO : data) {
            NewGoodsSpuVO spu = newGoodsGroupVO.getSpu();
            for (NewGoodsSkuVO newGoodsSkuVO : newGoodsGroupVO.getSkuList()) {
                NewGoodsSheet newGoodsSheet = NewGoodsTransMapper.INSTANCE.listVoToSheet(newGoodsSkuVO, spu);
                newGoodsSheets.add(newGoodsSheet);
            }
        }
        return newGoodsSheets;
    }
    
    default NewGoodsSheet listVoToSheet(NewGoodsSkuVO sku, NewGoodsSpuVO spu) {
        NewGoodsSheet newGoodsSheet = new NewGoodsSheet();

        // 从SPU获取的字段
        if (spu != null) {
            newGoodsSheet.setItemType(typeToDesc(spu.getItemType()));
            newGoodsSheet.setName(spu.getName());
            newGoodsSheet.setItemCode(spu.getItemCode());
            newGoodsSheet.setCategory(spu.getCategory());
            newGoodsSheet.setBusinessLine(getBusinessLineStr(spu.getBusinessLine()));
            newGoodsSheet.setCorpBizType(BizLevelDivisionConvert.INSTANCE.corpBizTypeListToDescStr(spu.getCorpBizType()));
            newGoodsSheet.setBrand(spu.getBrand());
            newGoodsSheet.setBuyer(mapDafStaffVOToString(spu.getBuyer()));
            newGoodsSheet.setPrincipal(mapDafStaffVOToString(spu.getPrincipal()));
            newGoodsSheet.setQcs(mapDafStaffVOToString(spu.getQcs()));
            newGoodsSheet.setStandardName(spu.getStandardName());
            newGoodsSheet.setShelfTime(spu.getShelfTime() != null ?
                StringUtil.format("{}({})", DateUtil.formatDate(spu.getShelfTime()), spu.getPlanName()) : "");
            newGoodsSheet.setStatus(statusToString(spu.getStatus()));
            String liveVerbalTrickStatus = spu.getLiveVerbalTrickVos() != null ?
                    spu.getLiveVerbalTrickVos().stream()
                            .map(v -> v.getName() + ":" + liveVerbalTrickStatusToString(v.getLiveVerbalTrickStatus()))
                            .collect(Collectors.joining("、")) :
                    liveVerbalTrickStatusToString(spu.getLiveVerbalTrickStatus());
            newGoodsSheet.setLiveVerbalTrickStatus(liveVerbalTrickStatus);
            newGoodsSheet.setShipmentType(shipmentTypeToString(spu.getShipmentType()));
            newGoodsSheet.setShipmentArea(spu.getShipmentArea());
            newGoodsSheet.setShipmentAging(reverseBoolIntToString(spu.getShipmentAging()));
            newGoodsSheet.setLogistics(spu.getLogistics());
            newGoodsSheet.setExpressTemplate(spu.getExpressTemplate());
            newGoodsSheet.setNoReason(reverseBoolIntToString(spu.getNoReason()));
            newGoodsSheet.setRemark(spu.getRemark());
            newGoodsSheet.setPartnerSysType(partnerSysTypeToDesc(spu.getPartnerSysType()));
            newGoodsSheet.setMaterialsStatus(materialsStatusToDesc(spu.getMaterialsStatus()));
            newGoodsSheet.setDownFrameTime(DateUtil.formatDate(spu.getDownFrameTime()));
            newGoodsSheet.setDownFrameReason(spu.getDownFrameReason());
            newGoodsSheet.setTbTitle(spu.getTbTitle());
        }

        // 从SKU获取的字段
        if (sku != null) {
            newGoodsSheet.setSkuCode(sku.getSkuCode());
            newGoodsSheet.setActivePeriod(mapActivePeriod(sku.getActivePeriodStart(), sku.getActivePeriodEnd(), sku.getIsLongTerm()));
            newGoodsSheet.setSpecs(sku.getSpecs());
            newGoodsSheet.setCostPrice(decimal2String(sku.getCostPrice()));
            newGoodsSheet.setLinePrice(decimal2String(sku.getLinePrice()));
            newGoodsSheet.setDailyPrice(decimal2String(sku.getDailyPrice()));
            newGoodsSheet.setDailyActivities(sku.getDailyActivities());
            newGoodsSheet.setActivePrice(decimal2String(sku.getActivePrice()));
            newGoodsSheet.setActiveContent(sku.getActiveContent());
            newGoodsSheet.setLiveActive(sku.getLiveActive());
            newGoodsSheet.setALevelActivityPrice(sku.getALevelActivityPrice());
            newGoodsSheet.setALevelActivityGift(sku.getALevelActivityGift());
            newGoodsSheet.setALevelActivityLivePrice(sku.getALevelActivityLivePrice());
            newGoodsSheet.setALevelActivityLiveGift(sku.getALevelActivityLiveGift());
            newGoodsSheet.setSLevelPromotePrice(sku.getSLevelPromotePrice());
            newGoodsSheet.setSLevelPromoteRule(sku.getSLevelPromoteRule());
            newGoodsSheet.setSLevelPromoteLivePrice(sku.getSLevelPromoteLivePrice());
            newGoodsSheet.setSLevelPromoteLiveRule(sku.getSLevelPromoteLiveRule());
            newGoodsSheet.setSingleBuyPrice(sku.getSingleBuyPrice());
        }

        return newGoodsSheet;
    }
}
