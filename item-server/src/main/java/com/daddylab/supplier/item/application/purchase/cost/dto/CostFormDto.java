package com.daddylab.supplier.item.application.purchase.cost.dto;

import com.daddylab.supplier.item.controller.item.dto.ItemBuyerDto;
import com.daddylab.supplier.item.controller.purchase.dto.PurchaseVo;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2025年09月15日 09:59
 */
@Data
@ApiModel("保存参数封装")
public class CostFormDto {

  private Long id;

  @ApiModelProperty("合作方,0 电商，2 绿色家装")
  private List<Integer> corpType = new LinkedList<>();

  @ApiModelProperty("供应商 ID")
  private Long providerId;

  private String providerName;

  @ApiModelProperty("变更类型。1.日常成本，2.活动成本，3.阶梯供价")
  private Integer type;

  @ApiModelProperty("采购员 ID (入参)")
  private List<Long> buyerId = new LinkedList<>();

  @ApiModelProperty("采购员信息列表 (响应)")
  private List<ItemBuyerDto> buyerDtoList;

  @ApiModelProperty("变更原因")
  private String changeReason;

  @ApiModelProperty("开始时间")
  private Long startTime;

  @ApiModelProperty("上传凭证 URL")
  private List<ProofFileDto> proofUrl = new LinkedList<>();

  private Integer status;

  @ApiModelProperty("商品明细")
  List<ItemCostDto> costPriceList = new LinkedList<>();

  @ApiModelProperty("活动价格")
  List<MultiSkuPriceFullDTO> activityPriceList = new LinkedList<>();

  @ApiModelProperty("阶梯供价")
  List<MultiSkuPriceFullDTO> multiPriceList = new LinkedList<>();
}
