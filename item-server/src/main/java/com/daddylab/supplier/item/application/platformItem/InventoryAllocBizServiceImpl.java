package com.daddylab.supplier.item.application.platformItem;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.application.platformItem.config.PlatformItemSyncConfig;
import com.daddylab.supplier.item.application.platformItem.config.WarehouseConfig;
import com.daddylab.supplier.item.application.stockSpec.StockSpecBizService;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.platformItem.vo.ListOuterSkuCodeQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.InventoryAllocShopStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemSkuStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.stockSpec.AvailableStockSpecVO;
import com.daddylab.supplier.item.types.stockSpec.ShopSpecAllocableStockVO;
import com.google.common.collect.Lists;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.Value;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Service
@Slf4j
public class InventoryAllocBizServiceImpl implements InventoryAllocBizService {

        @Autowired
        IInventoryAllocService inventoryAllocService;
        @Autowired
        IInventoryAllocShopService inventoryAllocShopService;
        @Autowired
        IPlatformItemSkuService platformItemSkuService;
        @Autowired
        IPlatformItemService platformItemService;
        @Autowired
        PlatformItemSyncConfig platformItemSyncConfig;
        @Autowired
        WarehouseConfig warehouseConfig;
        @Autowired
        IWdtStockSpecRtService wdtStockSpecRtService;
        @Autowired
        IWdtStockSpecService wdtStockSpecService;
        @Autowired
        IInventoryMonitorService inventoryMonitorService;
        @Autowired
        ICombinationItemService combinationItemService;
        @Autowired
        IComposeSkuService composeSkuService;
        @Autowired
        private StockSpecBizService stockSpecBizService;
        @Autowired
        private OperateLogDomainService operateLogDomainService;

        @Override
        @DistributedLock(value = "allocInventory", msg = "库存分配任务正在执行中，请稍后再试")
        public void allocInventory() {
                allocInventory(null);
        }

        @Override
        @DistributedLock(value = "allocInventory", msg = "库存分配任务正在执行中，请稍后再试")
        public void allocInventory(Collection<String> skuCodes) {
                StopWatch watch = new StopWatch("库存分配");

                List<InventoryAllocShop> allocShops = getInventoryAllocShops();
                log.info("[库存分配]待分配店铺查询完成，共计={}", allocShops.size());

                if (allocShops.isEmpty()) {
                        log.info("[库存分配]待分配店铺为空，跳过分配");
                        return;
                }

                Set<String> allOuterSkuCodes;
                if (skuCodes != null) {
                        allOuterSkuCodes = new LinkedHashSet<>(skuCodes);
                } else {
                        watch.start("查询所有在售链接商品SKU");
                        List<String> shopNos = allocShops.stream().map(InventoryAllocShop::getShopNo)
                                        .collect(Collectors.toList());
                        allOuterSkuCodes = getAllOnSaleOuterSkuCodes(shopNos);
                        watch.stop();
                        log.info("[库存分配]查询到所有在售链接商品SKU，去重后共计={}，time={}ms", allOuterSkuCodes.size(),
                                        watch.getLastTaskTimeMillis());

                        watch.start("清理已失效的库存分配记录");
                        cleanInvalidAllocRecords(allocShops, allOuterSkuCodes);
                        watch.stop();
                        log.info("[库存分配]清理已失效的库存分配记录完成，time={}ms", watch.getLastTaskTimeMillis());

                }

                watch.start("按编码分配库存");
                Flux.fromIterable(allOuterSkuCodes).window(50)
                                .concatMap(batch -> batch.flatMap(skuCode -> allocInventory(skuCode, allocShops)))
                                .blockLast();
                watch.stop();
                log.info("[库存分配]库存分配完成，分配耗时={}ms，总耗时={}ms", watch.getLastTaskTimeMillis(), watch.getTotalTimeMillis());
        }

        @Override
        public void cleanInvalidAllocRecords(List<InventoryAllocShop> allocShops, Set<String> allOuterSkuCodes) {
                // 删除店铺配置已关闭的店铺的库存分配记录
                List<String> shopNos = allocShops.stream().map(InventoryAllocShop::getShopNo)
                                .collect(Collectors.toList());
                // 使用游标遍历所有库存分配记录，如果店铺配置已关闭，则删除
                long cursorId = 0L;
                while (true) {
                        List<InventoryAlloc> inventoryAllocs = inventoryAllocService.lambdaQuery()
                                        .gt(InventoryAlloc::getId, cursorId).orderByAsc(InventoryAlloc::getId)
                                        .last("limit 1000").list();
                        if (inventoryAllocs.isEmpty()) {
                                break;
                        }
                        List<Long> invalidInventoryAllocsIds = inventoryAllocs.stream().filter(inventoryAlloc -> {
                                if (!shopNos.contains(inventoryAlloc.getShopNo())) {
                                        return true;
                                }
                                if (inventoryAlloc.isMainRecord()) {
                                        if (!allOuterSkuCodes.contains(inventoryAlloc.getSkuCode())) {
                                                return true;
                                        }
                                } else if (!allOuterSkuCodes.contains(inventoryAlloc.getSuiteNo())) {
                                        return true;
                                }
                                return false;
                        }).map(InventoryAlloc::getId).collect(Collectors.toList());
                        if (!invalidInventoryAllocsIds.isEmpty()) {
                                inventoryAllocService.removeByIdsWithTime(invalidInventoryAllocsIds);
                                log.info("[库存分配]清理已失效的库存分配记录完成，共计={}条", invalidInventoryAllocsIds.size());
                        }
                        cursorId = inventoryAllocs.get(inventoryAllocs.size() - 1).getId();
                }
        }

        // =================================================================
        // Core Allocation Logic
        // =================================================================

        private Mono<Void> allocInventory(String skuCode, List<InventoryAllocShop> InventoryAllocShops) {
                // 检查skuCode是否为空，如果为空则直接跳过
                if (StringUtil.isBlank(skuCode)) {
                        log.warn("[库存分配][主入口]商品SKU编码为空，跳过库存分配流程");
                        return Mono.empty();
                }

                log.debug("[库存分配][主入口]开始分配商品SKU：{}，待分配店铺数：{}", skuCode, InventoryAllocShops.size());

                CombinationItem combinationItem = combinationItemService.getByItemCode(skuCode);
                if (combinationItem != null) {
                        log.debug("[库存分配][主入口]商品SKU：{} 识别为组合商品，组合商品ID：{}", skuCode, combinationItem.getId());
                        CombinationItemAllocContext context = new CombinationItemAllocContext().setSkuCode(skuCode)
                                        .setInventoryAllocShops(InventoryAllocShops)
                                        .setCombinationItem(combinationItem);
                        return allocCombinationItemInventory(context);
                } else {
                        log.debug("[库存分配][主入口]商品SKU：{} 识别为普通商品", skuCode);
                        RegularItemAllocContext context = new RegularItemAllocContext().setSkuCode(skuCode)
                                        .setInventoryAllocShops(InventoryAllocShops);
                        return allocRegularItemInventory(context);
                }
        }

        /**
         * 分配普通商品库存
         */
        private Mono<Void> allocRegularItemInventory(RegularItemAllocContext context) {
                List<InventoryAllocShop> inventoryAllocShops = context.getInventoryAllocShops();
                List<String> shopNos = inventoryAllocShops.stream().map(InventoryAllocShop::getShopNo)
                                .collect(Collectors.toList());
                String skuCode = context.getSkuCode();
                // 查询在售的平台商品SKU
                List<PlatformItemSku> platformItemSkus = queryOnSalePlatformItemSkus(shopNos, skuCode);
                if (platformItemSkus.isEmpty()) {
                        log.debug("[库存分配][分配普通商品库存]商品SKU={}，未找到关联的在售平台商品SKU，跳过分配", skuCode);
                        return Mono.empty();
                }
                log.debug("[库存分配][分配普通商品库存]商品SKU={}，找到关联的在售平台商品SKU：{}", skuCode,
                                platformItemSkus.stream().map(PlatformItemSku::getId).collect(Collectors.toList()));

                // 2. 查询相关组合装商品
                List<Long> platformItemIds = platformItemSkus.stream().map(PlatformItemSku::getPlatformItemId)
                                .collect(Collectors.toList());
                List<PlatformItem> platformItems = platformItemService.listByIds(platformItemIds);

                // 查询套装内包含此单品的组合装
                List<CombinationItem> relatedCombinationItems = combinationItemService.listBySkuCode(skuCode);
                List<PlatformItem> relatedCombinationPiList = Collections.emptyList();
                List<PlatformItemSku> relatedCombinationPisList = Collections.emptyList();

                List<String> relatedCombinationCodes = relatedCombinationItems.stream().map(CombinationItem::getCode)
                                .collect(Collectors.toList());

                if (!relatedCombinationItems.isEmpty()) {

                        // 查询使用这些组合装编码的平台商品SKU
                        relatedCombinationPisList = queryOnSalePlatformItemSkus(shopNos, relatedCombinationCodes);
                        if (!relatedCombinationPisList.isEmpty()) {
                                List<Long> relatedCombinationPiIds = relatedCombinationPisList.stream()
                                                .map(PlatformItemSku::getPlatformItemId).collect(Collectors.toList());
                                relatedCombinationPiList = platformItemService.listByIds(relatedCombinationPiIds);
                        }
                }

                Set<PlatformItem> allPlatformItems = new HashSet<>();
                allPlatformItems.addAll(platformItems);
                allPlatformItems.addAll(relatedCombinationPiList);
                log.debug("[库存分配][普通商品分配]商品SKU：{}，相关组合装编码：{}，关联的平台商品：{}", skuCode, relatedCombinationCodes,
                                relatedCombinationPiList.stream().map(PlatformItem::getId)
                                                .collect(Collectors.toList()));

                setSyncEnabledByShopSetting(inventoryAllocShops, allPlatformItems);
                Map<Long, PlatformItem> platformItemMap = allPlatformItems.stream()
                                .collect(Collectors.toMap(PlatformItem::getId, Function.identity()));

                context.setPlatformItemSkus(platformItemSkus).setPlatformItemSkus1(relatedCombinationPisList)
                                .setPlatformItemMap(platformItemMap);

                List<String> calculations = new ArrayList<>();
                context.setCalculations(calculations);

                List<InventoryAlloc> inventoryAllocs = inventoryAllocService.listBySkuCode(skuCode);
                context.setInventoryAllocs(inventoryAllocs);

                List<ShopSpecAllocableStockVO> shopSpecAllocableStocks = getShopSpecAllocableStocks(
                                Collections.singletonList(skuCode), context.getInventoryAllocs(),
                                context.getInventoryAllocShops());
                context.setShopSpecAllocableStocks(shopSpecAllocableStocks);

                BigDecimal lockStock = CollectionUtil.isEmpty(context.getInventoryAllocs()) ? BigDecimal.ZERO
                                : new BigDecimal(context.getInventoryAllocs().stream()
                                                .filter(v -> v.getLockEnabled() && v.getEffectiveNum() != null)
                                                .mapToInt(InventoryAlloc::getEffectiveNum).sum());
                context.setLockStock(lockStock);
                if (lockStock.compareTo(BigDecimal.ZERO) > 0) {
                        log.debug("[库存分配][分配普通商品库存]商品SKU={}，存在锁定库存={}", skuCode, lockStock);
                }

                List<InventoryAlloc> newInventoryAllocs = new ArrayList<>();
                for (InventoryAllocShop InventoryAllocShop : inventoryAllocShops) {
                        RegularItemAllocShopContext regularItemAllocShopContext = new RegularItemAllocShopContext(
                                        context, InventoryAllocShop);
                        String shopNo = InventoryAllocShop.getShopNo();
                        List<PlatformItemSku> shopOnSaleSkus = regularItemAllocShopContext.getPlatformItemSkus();
                        if (shopOnSaleSkus.isEmpty()) {
                                log.debug("[库存分配][分配普通商品库存]商品SKU={}，店铺{}，未找到关联的平台商品SKU，跳过分配", skuCode, shopNo);
                                continue;
                        }
                        List<InventoryAlloc> inventoryAllocsForShop = allocStockByWeight(regularItemAllocShopContext);
                        newInventoryAllocs.addAll(inventoryAllocsForShop);
                }
                List<InventoryAlloc> inventoryAllocs0 = context.getInventoryAllocs().stream()
                                .filter(v -> StringUtil.isBlank(v.getSuiteNo())).collect(Collectors.toList());
                saveOrUpdateInventoryAllocs(inventoryAllocs0, newInventoryAllocs);
                return Mono.empty();
        }

        /**
         * 分配组合装商品库存
         */
        private Mono<Void> allocCombinationItemInventory(CombinationItemAllocContext context) {
                String skuCode = context.getSkuCode();
                List<InventoryAllocShop> inventoryAllocShops = context.getInventoryAllocShops();
                List<String> shopNos = inventoryAllocShops.stream().map(InventoryAllocShop::getShopNo)
                                .collect(Collectors.toList());
                List<InventoryAlloc> inventoryAllocs = inventoryAllocService.listBySkuCode(skuCode);
                Set<Long> inventoryAllocPlatformItemSkuIdsSet = inventoryAllocs.stream()
                                .map(InventoryAlloc::getPlatformItemSkuId).collect(Collectors.toSet());
                List<InventoryAlloc> inventoryAllocsWithDetail = inventoryAllocService
                                .listByPlatformItemSkuId(inventoryAllocPlatformItemSkuIdsSet);
                context.setInventoryAllocs(inventoryAllocsWithDetail);
                CombinationItem combinationItem = context.getCombinationItem();
                List<ComposeSku> composeSkus = composeSkuService.selectByCombinationId(combinationItem.getId());
                context.setComposeSkus(composeSkus);
                List<String> composeSkuCodes = composeSkus.stream().map(ComposeSku::getSkuCode)
                                .collect(Collectors.toList());
                context.setComposeSkuCodes(composeSkuCodes);
                if (composeSkus.isEmpty()) {
                        log.debug("[库存分配][分配组合装商品库存]商品SKU={}，组合装单品列表为空，跳过分配", skuCode);
                        return Mono.empty();
                }

                // 查询组合装在售的平台商品SKU
                List<PlatformItemSku> platformItemSkus = queryOnSalePlatformItemSkus(shopNos, skuCode);
                context.setPlatformItemSkus(platformItemSkus);
                log.debug("[库存分配][分配组合装商品库存]商品SKU={}，组合装作为独立链接在售的平台商品SKU：{}", skuCode,
                                platformItemSkus.stream().map(PlatformItemSku::getId).collect(Collectors.toList()));
                if (platformItemSkus.isEmpty()) {
                        log.debug("[库存分配][分配组合装商品库存]商品SKU={}，组合装在售的平台商品SKU列表为空，跳过分配", skuCode);
                        return Mono.empty();
                }

                // 查询组合装套内单品作为独立链接在售的平台商品SKU信息
                List<PlatformItemSku> platformItemSkusForComposeSkus = queryOnSalePlatformItemSkus(shopNos,
                                composeSkuCodes);
                context.setPlatformItemSkus1(platformItemSkusForComposeSkus);
                log.debug("[库存分配][分配组合装商品库存]商品SKU={}，组合装套内单品作为独立链接在售的平台商品SKU：{}", skuCode,
                                platformItemSkusForComposeSkus.stream().map(PlatformItemSku::getId)
                                                .collect(Collectors.toList()));

                // 查询包含当前组合装内单品的其他组合装
                Map<String, List<CombinationItem>> otherRelatedCombinationItems = combinationItemService
                                .mapListBySkuCode(composeSkuCodes);
                List<String> otherRelatedCombinationItemCodes = otherRelatedCombinationItems.values().stream()
                                .flatMap(Collection::stream)
                                .filter(ci -> !Objects.equals(ci.getId(), combinationItem.getId()))
                                .map(CombinationItem::getCode).distinct().collect(Collectors.toList());
                log.debug("[库存分配][分配组合装商品库存]商品SKU={}，包含当前组合装内单品的其他组合装编码：{}", skuCode, otherRelatedCombinationItemCodes);
                List<PlatformItemSku> platformItemSkusForOtherRelatedCombination = otherRelatedCombinationItemCodes
                                .isEmpty() ? Collections.emptyList()
                                                : queryOnSalePlatformItemSkus(shopNos,
                                                                otherRelatedCombinationItemCodes);
                log.debug("[库存分配][分配组合装商品库存]商品SKU={}，包含当前组合装内单品的其他组合装作为独立链接在售的平台商品SKU：{}", skuCode,
                                platformItemSkusForOtherRelatedCombination.stream().map(PlatformItemSku::getId)
                                                .collect(Collectors.toList()));
                HashMap<String, List<PlatformItemSku>> platformItemSkus2 = new HashMap<>();
                otherRelatedCombinationItems.forEach((sc, items) -> {
                        platformItemSkus2.put(sc, items.stream().filter(item -> !item.getCode().equals(skuCode))
                                        .flatMap(item -> platformItemSkusForOtherRelatedCombination.stream()
                                                        .filter(pis -> pis.getOuterSkuCode().equals(item.getCode())))
                                        .collect(Collectors.toList()));
                });
                context.setPlatformItemSkus2(platformItemSkus2);

                Set<Long> allRelatedPlatformItemIds = Stream
                                .of(platformItemSkus, platformItemSkusForComposeSkus,
                                                platformItemSkusForOtherRelatedCombination)
                                .flatMap(Collection::stream).map(PlatformItemSku::getPlatformItemId)
                                .collect(Collectors.toSet());

                List<PlatformItem> allRelatedPlatformItems = platformItemService.listByIds(allRelatedPlatformItemIds);
                setSyncEnabledByShopSetting(context.getInventoryAllocShops(), allRelatedPlatformItems);
                Map<Long, PlatformItem> platformItemMap = allRelatedPlatformItems.stream()
                                .collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
                context.setPlatformItemMap(platformItemMap);

                List<ShopSpecAllocableStockVO> shopSpecAllocableStocks = getShopSpecAllocableStocks(composeSkuCodes,
                                context.getInventoryAllocShops());
                context.setShopSpecAllocableStocks(shopSpecAllocableStocks);

                List<InventoryAlloc> newInventoryAllocs = new ArrayList<>();
                for (InventoryAllocShop InventoryAllocShop : context.getInventoryAllocShops()) {
                        CombinationItemAllocShopContext allocInventoryAllocShop = new CombinationItemAllocShopContext(
                                        context, InventoryAllocShop);
                        List<InventoryAlloc> shopAllocs = allocCombinationStockByWeight(allocInventoryAllocShop);
                        newInventoryAllocs.addAll(shopAllocs);
                }

                saveOrUpdateInventoryAllocs(context.getInventoryAllocs(), newInventoryAllocs);
                return Mono.empty();
        }

        private static void setSyncEnabledByShopSetting(List<InventoryAllocShop> inventoryAllocShops,
                        Collection<PlatformItem> platformItems) {
                for (PlatformItem platformItem : platformItems) {
                        for (InventoryAllocShop inventoryAllocShop : inventoryAllocShops) {
                                if (platformItem.getShopNo().equals(inventoryAllocShop.getShopNo())) {
                                        platformItem.setSyncEnabledByShopSetting(inventoryAllocShop);
                                        break;
                                }
                        }
                }
        }

        private List<InventoryAlloc> allocStockByWeight(RegularItemAllocShopContext context) {
                String skuCode = context.getParentContext().getSkuCode();
                String shopNo = context.getInventoryAllocShop().getShopNo();

                log.debug("[库存分配][普通商品分配]开始处理普通商品库存分配，商品SKU：{}，店铺：{}", skuCode, shopNo);

                ArrayList<String> calculations = new ArrayList<>();
                WeightCalculation weightCalculation = calculateSkuWeight(context);
                List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
                BigDecimal shopAllocableStock = context.getShopSpecAllocableStocks().stream()
                                .map(ShopSpecAllocableStockVO::getAllocableStock)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                log.debug("[库存分配][普通商品分配]商品SKU：{}，店铺：{}，店铺可分配库存总量：{}，直接关联平台商品SKU数量：{}，组合装关联平台商品SKU数量：{}", skuCode,
                                shopNo, shopAllocableStock, context.getPlatformItemSkus().size(),
                                context.getPlatformItemSkus1() != null ? context.getPlatformItemSkus1().size() : 0);

                calculations.add("# 店铺可用库存计算");
                for (ShopSpecAllocableStockVO shopSpecAllocableStock : context.getShopSpecAllocableStocks()) {
                        calculations.addAll(shopSpecAllocableStock.getCalculations());
                }
                calculations.add("# 平台商品库存分配");
                calculations.add(String.format("店铺可分配库存总量：%s", shopAllocableStock));

                // 记录所有平台商品的权重信息
                calculations.add("# 平台商品权重分布");
                for (WeightCalculation.WeightItem weightItem : weightCalculation.getWeights()) {
                        calculations.add(weightItem.toString());
                }

                for (PlatformItemSku platformItemSku : context.getPlatformItemSkus()) {
                        // 检查outerSkuCode是否为空，如果为空则跳过
                        if (StringUtil.isBlank(platformItemSku.getOuterSkuCode())) {
                                log.warn("[库存分配][普通商品分配]平台商品SKU {} 的outerSkuCode为空，跳过分配", platformItemSku.getId());
                                continue;
                        }

                        Optional<WeightCalculation.WeightItem> weightItemOptional = weightCalculation
                                        .get(platformItemSku.getId());
                        if (!weightItemOptional.isPresent()) {
                                log.warn("[库存分配][普通商品分配]平台商品SKU {} 未找到权重配置，跳过分配", platformItemSku.getId());
                                continue;
                        }
                        WeightCalculation.WeightItem allocWeight = weightItemOptional.get();

                        List<String> contextCalculations = context.getContextCalculations();
                        contextCalculations.addAll(calculations);

                        contextCalculations.add(String.format("# 平台商品SKU：%s，商品名称：%s", platformItemSku.getId(),
                                        context.getParentContext().getPlatformItemMap()
                                                        .get(platformItemSku.getPlatformItemId()).getGoodsName()));

                        BigDecimal allocStock = allocWeight.getPercent().multiply(shopAllocableStock).setScale(0,
                                        RoundingMode.HALF_UP);
                        contextCalculations.add(String.format("平台商品分配库存 = 权重占比 * 店铺可分配库存 = %s * %s = %s",
                                        allocWeight.getPercent().toPlainString(), shopAllocableStock, allocStock));

                        log.debug("[库存分配][普通商品分配]平台商品SKU {} 权重：{}，分配库存：{}", platformItemSku.getId(),
                                        allocWeight.getWeight(), allocStock);

                        InventoryAlloc inventoryAlloc = createInventoryAlloc(platformItemSku, allocStock,
                                        contextCalculations);
                        inventoryAllocs.add(inventoryAlloc);
                }

                log.debug("[库存分配][普通商品分配]商品SKU：{}，店铺：{}，分配完成，创建分配记录数：{}", skuCode, shopNo, inventoryAllocs.size());

                return inventoryAllocs;
        }

        private WeightCalculation calculateSkuWeight(RegularItemAllocShopContext context) {
                Map<Long, PlatformItem> platformItemMap = context.getParentContext().getPlatformItemMap();
                List<PlatformItemSku> skuList = Stream.of(context.getPlatformItemSkus(), context.getPlatformItemSkus1())
                                .flatMap(Collection::stream).collect(Collectors.toList());
                WeightCalculation weightCalculation = new WeightCalculation();
                for (PlatformItemSku sku : skuList) {
                        // 检查outerSkuCode是否为空，如果为空则跳过
                        if (StringUtil.isBlank(sku.getOuterSkuCode())) {
                                log.warn("[库存分配][分配普通商品库存]平台商品SKU {} 的outerSkuCode为空，跳过分配", sku.getId());
                                continue;
                        }

                        PlatformItem platformItem = platformItemMap.get(sku.getPlatformItemId());
                        if (platformItem == null) {
                                log.error("[库存分配][分配普通商品库存]平台商品 {} 未找到，跳过分配", sku.getPlatformItemId());
                                continue;
                        }
                        if (!platformItem.getSyncEnabled()) {
                                log.debug("[库存分配][分配普通商品库存]平台商品 {} {} 未开启库存同步，跳过分配", platformItem.getShopNo(),
                                                platformItem.getId());
                                continue;
                        }
                        if (platformItem.getLockEnabled()) {
                                log.debug("[库存分配][分配普通商品库存]平台商品 {} {} 被锁定，跳过分配", platformItem.getShopNo(),
                                                platformItem.getId());
                                continue;
                        }
                        int inventoryAllocWeight = platformItemSyncConfig.getInventoryAllocWeight(platformItem);
                        weightCalculation.add(sku.getId(), inventoryAllocWeight);
                        log.debug("[库存分配][分配普通商品库存]平台商品 {} 计算权重 = {}",
                                        Arrays.toString(new Object[] { platformItem.getShopNo(), platformItem.getId(),
                                                        sku.getId(), sku.getOuterSkuCode(),
                                                        platformItem.getGoodsName() }),
                                        inventoryAllocWeight);
                }
                context.setWeightCalculation(weightCalculation);
                return weightCalculation;
        }

        // =================================================================
        // Combination Item Allocation Helpers
        // =================================================================

        private List<InventoryAlloc> allocCombinationStockByWeight(CombinationItemAllocShopContext context) {
                List<ComposeSku> composeSkus = context.getParentContext().getComposeSkus();
                Map<Long, PlatformItem> platformItemMap = context.getParentContext().getPlatformItemMap();
                List<ShopSpecAllocableStockVO> shopSpecAllocableStocks = context.getShopSpecAllocableStocks();
                ArrayList<String> calculations = new ArrayList<>();
                calculations.add("# 店铺可用库存计算");
                for (ShopSpecAllocableStockVO shopSpecAllocableStock : context.getShopSpecAllocableStocks()) {
                        calculations.addAll(shopSpecAllocableStock.getCalculations());
                }
                List<InventoryAlloc> inventoryAllocs = new ArrayList<>();
                List<PlatformItemSku> platformItemSkus = context.getPlatformItemSkus();

                log.debug("[库存分配][组合装分配]开始处理组合装库存分配，组合装编码：{}，店铺：{}，组合装构成：{}，关联平台商品：{}",
                                context.getParentContext().getSkuCode(),
                                context.getCurrentInventoryAllocShop().getShopNo(),
                                composeSkus.stream().map(cs -> cs.getSkuCode() + "*" + cs.getCount())
                                                .collect(Collectors.joining(", ")),
                                platformItemSkus.stream().map(PlatformItemSku::getId).collect(Collectors.toList()));

                for (PlatformItemSku platformItemSku : platformItemSkus) {
                        String skuCode = platformItemSku.getOuterSkuCode();
                        // 检查outerSkuCode是否为空，如果为空则跳过
                        if (StringUtil.isBlank(skuCode)) {
                                log.warn("[库存分配][分配组合装商品库存]平台商品SKU {} 的outerSkuCode为空，跳过分配", platformItemSku.getId());
                                continue;
                        }

                        PlatformItem platformItem = platformItemMap.get(platformItemSku.getPlatformItemId());
                        if (!platformItem.getSyncEnabled()) {
                                log.debug("[库存分配][分配组合装商品库存]平台商品 {} {} 未开启库存同步，跳过分配", platformItem.getShopNo(),
                                                platformItem.getId());
                                continue;
                        }
                        if (platformItem.getLockEnabled()) {
                                log.debug("[库存分配][分配组合装商品库存]平台商品 {} {} 被锁定，跳过分配", platformItem.getShopNo(),
                                                platformItem.getId());
                                continue;
                        }

                        ArrayList<String> contextCalculations = new ArrayList<>(context.getContextCalculations());
                        contextCalculations.addAll(calculations);
                        contextCalculations.add("# 组合装平台商品库存分配");
                        contextCalculations.add(String.format("组合装编码：%s，平台商品SKU：%s", skuCode, platformItemSku.getId()));

                        // 计算组合装库存，应用公式：FLOOR(MIN(单品库存/单品数量))
                        List<BigDecimal> composeSkuAllocableAmounts = new ArrayList<>();

                        for (ComposeSku composeSku : composeSkus) {
                                String composeSkuCode = composeSku.getSkuCode();
                                Integer composeSkuCount = composeSku.getCount();

                                contextCalculations.add(String.format("# 分析套装内单品：%s，套装内数量：%s", composeSkuCode,
                                                composeSkuCount));

                                // 构建权重计算，只考虑当前店铺的平台商品SKU
                                WeightCalculation weightCalculation = new WeightCalculation();
                                String currentShopNo = context.getCurrentInventoryAllocShop().getShopNo();

                                // 收集当前店铺下所有相关的平台商品SKU
                                List<PlatformItemSku> currentShopPlatformItemSkus = new ArrayList<>();

                                // 1. 当前组合装的平台商品SKU
                                currentShopPlatformItemSkus.addAll(platformItemSkus.stream()
                                                .filter(pis -> pis.getShopNo().equals(currentShopNo))
                                                .collect(Collectors.toList()));

                                // 2. 套装内单品作为独立链接的平台商品SKU
                                currentShopPlatformItemSkus.addAll(context.getPlatformItemSkus1().stream()
                                                .filter(pis -> pis.getShopNo().equals(currentShopNo))
                                                .collect(Collectors.toList()));

                                // 3. 包含当前单品的其他组合装的平台商品SKU
                                List<PlatformItemSku> relatedCombinationSkus = context.getPlatformItemSkus2()
                                                .getOrDefault(composeSkuCode, Collections.emptyList());
                                currentShopPlatformItemSkus.addAll(relatedCombinationSkus.stream()
                                                .filter(pis -> pis.getShopNo().equals(currentShopNo))
                                                .collect(Collectors.toList()));

                                // 去重并计算权重
                                currentShopPlatformItemSkus.stream().distinct().forEach(pis -> {
                                        PlatformItem thisPlatformItem = platformItemMap.get(pis.getPlatformItemId());
                                        if (thisPlatformItem != null && !thisPlatformItem.getLockEnabled()
                                                        && thisPlatformItem.getSyncEnabled()) {
                                                int inventoryAllocWeight = platformItemSyncConfig
                                                                .getInventoryAllocWeight(thisPlatformItem);
                                                weightCalculation.add(pis.getId(), inventoryAllocWeight);
                                        }
                                });

                                contextCalculations.add(String.format("单品[%s]在店铺[%s]的相关平台商品SKU数量：%s", composeSkuCode,
                                                currentShopNo, currentShopPlatformItemSkus.size()));

                                WeightCalculation.WeightItem weightItem = weightCalculation.get(platformItemSku.getId())
                                                .orElse(null);
                                if (weightItem == null) {
                                        contextCalculations.add(String.format("单品[%s]在平台商品SKU[%s]上未找到权重配置，跳过",
                                                        composeSkuCode, platformItemSku.getId()));
                                        continue;
                                }

                                contextCalculations.add(String.format("单品[%s]在平台商品SKU[%s]上的权重：%s", composeSkuCode,
                                                platformItemSku.getId(), weightItem.getWeight()));

                                // 获取单品的可分配库存
                                BigDecimal shopSpecAllocableStockTotal = shopSpecAllocableStocks.stream()
                                                .filter(v -> v.getSkuCode().equals(composeSkuCode))
                                                .map(ShopSpecAllocableStockVO::getAllocableStock)
                                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                                // 按权重分配的单品库存
                                BigDecimal thisPisComposeSkuAvailableStock = shopSpecAllocableStockTotal
                                                .multiply(weightItem.getPercent()).setScale(0, RoundingMode.DOWN);

                                // 应用组合装公式：单品可分配库存 / 套装内单品数量
                                BigDecimal composeSkuAllocableAmount = BigDecimal.ZERO;
                                if (composeSkuCount > 0) {
                                        composeSkuAllocableAmount = new BigDecimal(
                                                        thisPisComposeSkuAvailableStock.intValue() / composeSkuCount);
                                }

                                contextCalculations.add(String.format(
                                                "单品[%s]: 可分配库存=%s, 权重占比=%s, 按照权重分配库存=%s, 套装内数量=%s, 组合装可制作数量=FLOOR(%s/%s)=%s",
                                                composeSkuCode, shopSpecAllocableStockTotal,
                                                weightItem.getPercent().toPlainString(),
                                                thisPisComposeSkuAvailableStock, composeSkuCount,
                                                thisPisComposeSkuAvailableStock, composeSkuCount,
                                                composeSkuAllocableAmount));

                                composeSkuAllocableAmounts.add(composeSkuAllocableAmount);
                        }

                        // 应用组合装库存计算公式：MIN(所有单品的组合装可制作数量)
                        BigDecimal pisCombinationAllocStock = composeSkuAllocableAmounts.stream()
                                        .min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);

                        contextCalculations.add(String.format("组合装[%s]最终可分配数量 = MIN(%s) = %s", skuCode,
                                        composeSkuAllocableAmounts.stream().map(BigDecimal::toPlainString)
                                                        .collect(Collectors.joining(", ")),
                                        pisCombinationAllocStock));

                        InventoryAlloc inventoryAlloc = createInventoryAlloc(platformItemSku, pisCombinationAllocStock,
                                        contextCalculations);
                        inventoryAllocs.add(inventoryAlloc);

                        // 为组合装内的每个单品创建分配记录
                        for (ComposeSku composeSku : composeSkus) {
                                String composeSkuCode = composeSku.getSkuCode();
                                // 检查composeSkuCode是否为空，如果为空则跳过
                                if (StringUtil.isBlank(composeSkuCode)) {
                                        log.warn("[库存分配][组合装分配]组合装{}内单品SKU编码为空，跳过创建库存分配记录", skuCode);
                                        continue;
                                }
                                BigDecimal composeSkuAllocStock = pisCombinationAllocStock
                                                .multiply(new BigDecimal(composeSku.getCount()))
                                                .setScale(0, RoundingMode.HALF_UP);
                                List<String> composeSkuCalculations = new ArrayList<>();
                                composeSkuCalculations.add(String.format(
                                                "组合装单品[%s]实际占用库存 = 组合装分配数量 * 套装包含的单品数量 = %s * %s = %s", composeSkuCode,
                                                pisCombinationAllocStock, composeSku.getCount(), composeSkuAllocStock));
                                InventoryAlloc inventoryAlloc1 = createSuiteComposeSkuInventoryAlloc(platformItemSku,
                                                composeSkuCode, composeSkuAllocStock, composeSkuCalculations);
                                inventoryAllocs.add(inventoryAlloc1);
                        }
                }
                return inventoryAllocs;
        }

        // =================================================================
        // Context Query
        // =================================================================

        private List<InventoryAllocShop> getInventoryAllocShops() {
                return inventoryAllocShopService.lambdaQuery().in(InventoryAllocShop::getStatus,
                                Arrays.asList(InventoryAllocShopStatus.ACTIVE, InventoryAllocShopStatus.UNABLE)).list();
        }

        private Set<String> getAllOnSaleOuterSkuCodes(List<String> shopNos) {
                ListOuterSkuCodeQuery listOuterSkuCodeQuery = new ListOuterSkuCodeQuery();
                listOuterSkuCodeQuery
                                .setPlatformItemSkuStatus(Lists.newArrayList(PlatformItemSkuStatus.ON_SALE.getValue()));
                listOuterSkuCodeQuery.setShopNos(shopNos);
                return new HashSet<>(platformItemSkuService.listOuterSkuCode(listOuterSkuCodeQuery));
        }

        private List<PlatformItemSku> queryOnSalePlatformItemSkus(List<String> shopNos, String skuCode) {
                return queryOnSalePlatformItemSkus(shopNos, Collections.singletonList(skuCode));
        }

        private List<PlatformItemSku> queryOnSalePlatformItemSkus(List<String> shopNos, List<String> skuCodes) {
                return platformItemSkuService.lambdaQuery().in(PlatformItemSku::getShopNo, shopNos)
                                .in(PlatformItemSku::getOuterSkuCode, skuCodes).ne(PlatformItemSku::getOuterSkuCode, "")
                                .eq(PlatformItemSku::getStatus, PlatformItemSkuStatus.ON_SALE.getValue()).list();
        }

        public List<ShopSpecAllocableStockVO> getShopSpecAllocableStocks(Collection<String> specNos,
                        List<InventoryAllocShop> inventoryAllocShops) {
                if (CollectionUtil.isEmpty(specNos)) {
                        return Collections.emptyList();
                }
                List<InventoryAlloc> inventoryAllocs = inventoryAllocService.lambdaQuery()
                                .in(InventoryAlloc::getSkuCode, specNos).list();
                return getShopSpecAllocableStocks(specNos, inventoryAllocs, inventoryAllocShops);
        }

        public List<ShopSpecAllocableStockVO> getShopSpecAllocableStocks(Collection<String> specNos,
                        List<InventoryAlloc> inventoryAllocs, List<InventoryAllocShop> inventoryAllocShops) {
                List<AvailableStockSpecVO> availableStockSpecVOS = stockSpecBizService.getAvailableStocks(specNos,
                                warehouseConfig.getNotReadStockList());
                BigDecimal totalInventoryWeights = new BigDecimal(
                                inventoryAllocShops.stream().mapToInt(InventoryAllocShop::getInventoryWeight).sum());
                InventoryAllocShop lastInventoryAllocShop = inventoryAllocShops.get(inventoryAllocShops.size() - 1);
                ArrayList<ShopSpecAllocableStockVO> shopSpecAllocableStockVOS = new ArrayList<>();
                List<Long> platformItemIds = inventoryAllocs.stream().map(InventoryAlloc::getPlatformItemId)
                                .collect(Collectors.toList());
                List<PlatformItem> platformItems = platformItemIds.isEmpty() ? Collections.emptyList()
                                : platformItemService.listByIds(platformItemIds);
                Map<Long, PlatformItem> platformItemMap = platformItems.stream()
                                .collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
                for (String specNo : specNos) {
                        BigDecimal totalAllocableStock = availableStockSpecVOS.stream()
                                        .filter(v -> StrUtil.isNotBlank(v.getSkuCode()))
                                        .filter(v -> v.getSkuCode().equals(specNo))
                                        .map(AvailableStockSpecVO::getAllocableStock)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                        BigDecimal lockedNum = CollectionUtil.isEmpty(inventoryAllocs) ? BigDecimal.ZERO
                                        : new BigDecimal(inventoryAllocs.stream().filter(v -> {
                                                PlatformItem platformItem = platformItemMap.get(v.getPlatformItemId());
                                                return platformItem.getLockEnabled() && v.getEffectiveNum() != null
                                                                && specNo.equals(v.getSkuCode());
                                        }).mapToInt(InventoryAlloc::getEffectiveNum).sum());
                        BigDecimal totalAllocableStockSubtractLocked = NumberUtil
                                        .max(totalAllocableStock.subtract(lockedNum), BigDecimal.ZERO);
                        BigDecimal remainStock = totalAllocableStock;
                        for (InventoryAllocShop inventoryAllocShop : inventoryAllocShops) {
                                ArrayList<String> calculations = new ArrayList<>();
                                BigDecimal inventoryWeight = new BigDecimal(inventoryAllocShop.getInventoryWeight());
                                BigDecimal shopAllocableStock = totalAllocableStockSubtractLocked
                                                .divide(totalInventoryWeights, 6, RoundingMode.HALF_UP)
                                                .multiply(inventoryWeight).setScale(0, RoundingMode.HALF_UP);
                                if (lastInventoryAllocShop == inventoryAllocShop) {
                                        shopAllocableStock = remainStock;
                                } else {
                                        remainStock = remainStock.subtract(shopAllocableStock);
                                }
                                calculations.add(String.format(
                                                "店铺[%s|%s]可分配库存 = (SUM(仓内可用库存 - 警戒库存) - 锁定库存) * (店铺权重 / 总权重) = (%s - %s) * (%s / %s) = %s",
                                                inventoryAllocShop.getShopNo(), specNo, totalAllocableStock, lockedNum,
                                                inventoryWeight, totalInventoryWeights, shopAllocableStock));
                                ShopSpecAllocableStockVO shopSpecAllocableStockVO = new ShopSpecAllocableStockVO();
                                shopSpecAllocableStockVO.setShopNo(inventoryAllocShop.getShopNo());
                                shopSpecAllocableStockVO.setSkuCode(specNo);
                                shopSpecAllocableStockVO.setAllocableStock(shopAllocableStock);
                                shopSpecAllocableStockVO.setCalculations(calculations);
                                shopSpecAllocableStockVOS.add(shopSpecAllocableStockVO);
                        }
                }
                return shopSpecAllocableStockVOS;
        }

        // =================================================================
        // Utility Methods
        // =================================================================

        @Data
        private static class WeightCalculation {
                private Integer totalWeight = 0;
                private List<WeightItem> weights = new ArrayList<>();
                private WeightItem last;

                public WeightCalculation add(Long platformItemSkuId, Integer weight) {
                        WeightItem weightItem = new WeightItem();
                        weightItem.setPlatformItemSkuId(platformItemSkuId);
                        weightItem.setWeight(weight);
                        weights.add(weightItem);
                        totalWeight += weight;
                        last = weightItem;
                        return this;
                }

                public Optional<WeightItem> get(Long platformItemSkuId) {
                        return weights.stream().filter(v -> v.getPlatformItemSkuId().equals(platformItemSkuId))
                                        .findFirst();
                }

                @Data
                private class WeightItem {
                        private Long platformItemSkuId;
                        private Integer weight;

                        @Override
                        public String toString() {
                                return String.format("平台商品SKU权重[%s]: %s / %s = %s", platformItemSkuId, weight,
                                                totalWeight, getPercent().toPlainString());
                        }

                        public BigDecimal getPercent() {
                                if (this == last) {
                                        return BigDecimal.ONE.subtract(weights.stream().filter(v -> v != last)
                                                        .map(WeightItem::getPercent)
                                                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                                }
                                return new BigDecimal(weight).divide(new BigDecimal(totalWeight), 6,
                                                RoundingMode.HALF_UP);
                        }
                }
        }

        private InventoryAlloc createInventoryAlloc(PlatformItemSku platformItemSku, BigDecimal allocStock,
                        List<String> calculations) {
                InventoryAlloc inventoryAlloc = new InventoryAlloc();
                inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
                inventoryAlloc.setPlatformItemSkuId(platformItemSku.getId());
                inventoryAlloc.setShopNo(platformItemSku.getShopNo());
                inventoryAlloc.setSkuCode(platformItemSku.getOuterSkuCode());
                inventoryAlloc.setCalcNum(allocStock.intValue());
                inventoryAlloc.setEffectiveNum(allocStock.intValue());
                inventoryAlloc.setLastAllocTime(DateUtil.currentTime());
                inventoryAlloc.setCalculation(String.join(";", calculations));
                return inventoryAlloc;
        }

        private InventoryAlloc createSuiteComposeSkuInventoryAlloc(PlatformItemSku platformItemSku,
                        String composeSkuCode, BigDecimal allocStock, List<String> calculations) {
                InventoryAlloc inventoryAlloc = new InventoryAlloc();
                inventoryAlloc.setPlatformItemId(platformItemSku.getPlatformItemId());
                inventoryAlloc.setPlatformItemSkuId(platformItemSku.getId());
                inventoryAlloc.setShopNo(platformItemSku.getShopNo());
                inventoryAlloc.setSkuCode(composeSkuCode);
                inventoryAlloc.setSuiteNo(platformItemSku.getOuterSkuCode());
                inventoryAlloc.setCalcNum(allocStock.intValue());
                inventoryAlloc.setEffectiveNum(allocStock.intValue());
                inventoryAlloc.setLastAllocTime(DateUtil.currentTime());
                inventoryAlloc.setCalculation(String.join("; ", calculations));
                return inventoryAlloc;
        }

        @Value
        public static class InventoryAllocKey {
                /**
                 * 平台商品SkuID
                 */
                Long platformItemSkuId;
                /**
                 * SKU编码
                 */
                String skuCode;
                /**
                 * 组合装编码
                 */
                String suiteNo;

                public static InventoryAllocKey of(InventoryAlloc inventoryAlloc) {
                        return new InventoryAllocKey(inventoryAlloc.getPlatformItemSkuId(), inventoryAlloc.getSkuCode(),
                                        inventoryAlloc.getSuiteNo() == null ? "" : inventoryAlloc.getSuiteNo());
                }
        }

        private void saveOrUpdateInventoryAllocs(List<InventoryAlloc> inventoryAllocs0,
                        List<InventoryAlloc> inventoryAllocs) {
                Map<InventoryAllocKey, InventoryAlloc> existsMap = inventoryAllocs0.stream()
                                .filter(v -> StringUtil.isNotBlank(v.getSkuCode()))
                                .collect(Collectors.toMap(InventoryAllocKey::of, Function.identity()));
                List<InventoryAlloc> toUpdate = new ArrayList<>();
                List<InventoryAlloc> toSave = new ArrayList<>();
                List<InventoryAlloc> toRemove = new ArrayList<>();

                // 用于记录操作日志的数据
                List<OperateLog> operateLogs = new ArrayList<>();

                for (InventoryAlloc inventoryAlloc : inventoryAllocs) {
                        // 检查skuCode是否为空，如果为空则跳过
                        if (StringUtil.isBlank(inventoryAlloc.getSkuCode())) {
                                log.warn("[库存分配]库存分配记录的skuCode为空，跳过处理，platformItemSkuId: {}",
                                                inventoryAlloc.getPlatformItemSkuId());
                                continue;
                        }

                        InventoryAllocKey key = InventoryAllocKey.of(inventoryAlloc);
                        InventoryAlloc existsOne = existsMap.get(key);
                        if (existsOne != null) {
                                // 记录库存变更前的值
                                int beforeStock = existsOne.getEffectiveNum();
                                int afterStock = inventoryAlloc.getEffectiveNum();

                                existsOne.setLastAllocTime(inventoryAlloc.getLastAllocTime());
                                existsOne.setCalcNum(inventoryAlloc.getCalcNum());
                                existsOne.setEffectiveNum(inventoryAlloc.getEffectiveNum());
                                existsOne.setCalculation(inventoryAlloc.getCalculation());
                                existsOne.setSkuCode(inventoryAlloc.getSkuCode());
                                existsOne.setSuiteNo(inventoryAlloc.getSuiteNo());
                                toUpdate.add(existsOne);

                                // 准备操作日志数据
                                if (beforeStock != afterStock && inventoryAlloc.isMainRecord()) {
                                        OperateLog operateLog = new OperateLog();
                                        operateLog.setTargetType(OperateLogTarget.PLATFORM_ITEM);
                                        operateLog.setTargetId(inventoryAlloc.getPlatformItemId().toString());
                                        operateLog.setMsg(String.format("[系统分配]SKU编码：%s，同步库存前值：%d，同步库存后值：%d",
                                                        inventoryAlloc.getSkuCode(), beforeStock, afterStock));

                                        // 构建日志数据对象
                                        Map<String, Object> logDataItem = new HashMap<>();
                                        logDataItem.put("skuCode", inventoryAlloc.getSkuCode());
                                        logDataItem.put("beforeStock", beforeStock);
                                        logDataItem.put("afterStock", afterStock);
                                        operateLog.setData(JsonUtil.objToStr(logDataItem));
                                        operateLog.setOperatorId(0L);
                                        operateLog.setCreatedAt(DateUtil.currentTime());
                                        operateLogs.add(operateLog);
                                }
                        } else {
                                toSave.add(inventoryAlloc);

                                if (inventoryAlloc.isMainRecord()) {
                                        // 新增记录的操作日志
                                        OperateLog operateLog = new OperateLog();
                                        operateLog.setTargetType(OperateLogTarget.PLATFORM_ITEM);
                                        operateLog.setTargetId(inventoryAlloc.getPlatformItemId().toString());
                                        operateLog.setMsg(String.format("[系统分配]SKU编码：%s，新增库存分配，分配数量：%d",
                                                        inventoryAlloc.getSkuCode(), inventoryAlloc.getEffectiveNum()));

                                        // 构建日志数据对象
                                        Map<String, Object> logDataItem = new HashMap<>();
                                        logDataItem.put("skuCode", inventoryAlloc.getSkuCode());
                                        logDataItem.put("beforeStock", 0);
                                        logDataItem.put("afterStock", inventoryAlloc.getEffectiveNum());
                                        operateLog.setData(JsonUtil.objToStr(logDataItem));
                                        operateLog.setOperatorId(0L);
                                        operateLog.setCreatedAt(DateUtil.currentTime());
                                        operateLogs.add(operateLog);
                                }
                        }
                }
                Map<InventoryAllocKey, InventoryAlloc> newMap = inventoryAllocs.stream()
                                .collect(Collectors.toMap(InventoryAllocKey::of, Function.identity()));
                List<Long> platformItemIds0 = inventoryAllocs0.stream().map(InventoryAlloc::getPlatformItemId)
                                .distinct().collect(Collectors.toList());
                List<PlatformItem> platformItems0 = platformItemIds0.isEmpty() ? Collections.emptyList()
                                : platformItemService.listByIds(platformItemIds0);
                Map<Long, PlatformItem> platformItem0Map = platformItems0.stream()
                                .collect(Collectors.toMap(PlatformItem::getId, Function.identity()));
                inventoryAllocShopService.syncSyncEnabledGlobalConfig(platformItems0);
                for (InventoryAlloc inventoryAlloc : inventoryAllocs0) {
                        PlatformItem platformItem = platformItem0Map.get(inventoryAlloc.getPlatformItemId());
                        if (!newMap.containsKey(InventoryAllocKey.of(inventoryAlloc))) {
                                if (platformItem.getLockEnabled()) {
                                        continue;
                                }
                                if (!platformItem.getSyncEnabled()) {
                                        continue;
                                }
                                toRemove.add(inventoryAlloc);

                                if (inventoryAlloc.isMainRecord()) {
                                        // 删除记录的操作日志
                                        OperateLog operateLog = new OperateLog();
                                        operateLog.setTargetType(OperateLogTarget.PLATFORM_ITEM);
                                        operateLog.setTargetId(inventoryAlloc.getPlatformItemId().toString());
                                        operateLog.setMsg(String.format("[系统分配]SKU编码：%s，删除库存分配，原分配数量：%d",
                                                        inventoryAlloc.getSkuCode(), inventoryAlloc.getEffectiveNum()));

                                        // 构建日志数据对象
                                        Map<String, Object> logDataItem = new HashMap<>();
                                        logDataItem.put("skuCode", inventoryAlloc.getSkuCode());
                                        logDataItem.put("beforeStock", inventoryAlloc.getEffectiveNum());
                                        logDataItem.put("afterStock", 0);
                                        operateLog.setData(JsonUtil.objToStr(logDataItem));
                                        operateLog.setOperatorId(0L);
                                        operateLog.setCreatedAt(DateUtil.currentTime());
                                        operateLogs.add(operateLog);
                                }
                        }
                }
                if (!toUpdate.isEmpty()) {
                        inventoryAllocService.updateBatchById(toUpdate);
                        log.info("[库存分配]更新库存分配记录: {}",
                                        toUpdate.stream().map(InventoryAlloc::getId).collect(Collectors.toList()));
                }
                if (!toSave.isEmpty()) {
                        inventoryAllocService.saveBatch(toSave);
                        log.info("[库存分配]新增库存分配记录: {}", toSave.size());
                }
                if (!toRemove.isEmpty()) {
                        inventoryAllocService.removeByIdsWithTime(
                                        toRemove.stream().map(InventoryAlloc::getId).collect(Collectors.toList()));
                        log.info("[库存分配]删除库存分配记录: {}",
                                        toRemove.stream().map(InventoryAlloc::getId).collect(Collectors.toList()));
                }

                // 批量添加操作日志
                if (!operateLogs.isEmpty()) {
                        try {
                                operateLogDomainService.batchAddOperateLog(operateLogs);
                                log.info("[库存分配]批量添加操作日志，共{}条记录", operateLogs.size());
                        } catch (Exception e) {
                                log.error("[库存分配]批量添加操作日志失败", e);
                        }
                }

                Set<Long> platformItemIds = inventoryAllocs.stream().map(InventoryAlloc::getPlatformItemId)
                                .collect(Collectors.toSet());
                platformItemService.updateLastAllocTime(platformItemIds, DateUtil.currentTime());
        }

        // =================================================================
        // Inner Data Classes
        // =================================================================

        /**
         * 组合装库存分配上下文
         */
        @Data
        @Accessors(chain = true)
        private static class CombinationItemAllocContext {
                /**
                 * 本次分配执行时已存在的库存分配记录
                 */
                private List<InventoryAlloc> inventoryAllocs;
                /**
                 * 组合装编码
                 */
                private String skuCode;
                /**
                 * 店铺权重信息
                 */
                private List<InventoryAllocShop> InventoryAllocShops;
                /**
                 * 组合装
                 */
                private CombinationItem combinationItem;
                /**
                 * 当前组合装内包含的单品
                 */
                private List<ComposeSku> composeSkus;
                /**
                 * 当前组合装内包含的单品编码
                 */
                private List<String> composeSkuCodes;
                /**
                 * 当前组合装作为独立链接在售的SKU
                 */
                private List<PlatformItemSku> platformItemSkus;
                /**
                 * 当前组合装内单品作为独立链接在售的SKU
                 */
                private List<PlatformItemSku> platformItemSkus1;
                /**
                 * 当前组合装内单品被包含在其他组合装内进行销售的SKU
                 */
                private Map<String, List<PlatformItemSku>> platformItemSkus2;
                /**
                 * 上述所有平台商品的映射
                 */
                private Map<Long, PlatformItem> platformItemMap;
                /**
                 * 店铺可分配库存信息
                 */
                private List<ShopSpecAllocableStockVO> shopSpecAllocableStocks;
                /**
                 * 可分配库存计算过程
                 */
                private List<String> calculations;
        }

        /**
         * 组合装库存分配上下文
         */
        @Getter
        @Accessors(chain = true)
        private static class CombinationItemAllocShopContext {
                /**
                 * 父级上下文
                 */
                private final CombinationItemAllocContext parentContext;
                /**
                 * 当前店铺权重信息
                 */
                private final InventoryAllocShop currentInventoryAllocShop;
                /**
                 * 当前组合装作为独立链接在售的SKU
                 */
                private final List<PlatformItemSku> platformItemSkus;
                /**
                 * 当前组合装内单品作为独立链接在售的SKU
                 */
                private final List<PlatformItemSku> platformItemSkus1;
                /**
                 * 当前组合装内单品被包含在其他组合装内进行销售的SKU
                 */
                private final Map<String, List<PlatformItemSku>> platformItemSkus2;
                /**
                 * 可分配库存计算过程
                 */
                private @Setter List<String> calculations;
                /**
                 * 店铺可分配库存信息
                 */
                private final List<ShopSpecAllocableStockVO> shopSpecAllocableStocks;

                public CombinationItemAllocShopContext(CombinationItemAllocContext parentContext,
                                InventoryAllocShop currentInventoryAllocShop) {
                        this.parentContext = parentContext;
                        this.currentInventoryAllocShop = currentInventoryAllocShop;
                        this.platformItemSkus = parentContext.platformItemSkus.stream()
                                        .filter(sku -> sku.getShopNo().equals(currentInventoryAllocShop.getShopNo()))
                                        .collect(Collectors.toList());
                        this.platformItemSkus1 = parentContext.platformItemSkus1.stream()
                                        .filter(sku -> sku.getShopNo().equals(currentInventoryAllocShop.getShopNo()))
                                        .collect(Collectors.toList());
                        this.platformItemSkus2 = new HashMap<>();
                        parentContext.platformItemSkus2.forEach((k, v) -> this.platformItemSkus2.put(k, v.stream()
                                        .filter(sku -> sku.getShopNo().equals(currentInventoryAllocShop.getShopNo()))
                                        .collect(Collectors.toList())));
                        this.shopSpecAllocableStocks = parentContext.shopSpecAllocableStocks.stream()
                                        .filter(v -> v.getShopNo().equals(currentInventoryAllocShop.getShopNo()))
                                        .collect(Collectors.toList());
                }

                public List<String> getContextCalculations() {
                        ArrayList<String> contextCalculations = new ArrayList<>();
                        if (parentContext != null && CollectionUtil.isNotEmpty(parentContext.getCalculations())) {
                                contextCalculations.addAll(parentContext.getCalculations());
                        }
                        if (this.calculations != null) {
                                contextCalculations.addAll(this.calculations);
                        }
                        return contextCalculations;
                }
        }

        /**
         * 普通商品库存分配上下文
         */
        @Data
        @Accessors(chain = true)
        private static class RegularItemAllocContext {
                /**
                 * 本次分配执行时已存在的库存分配记录
                 */
                private List<InventoryAlloc> inventoryAllocs;
                /**
                 * 商品SKU编码
                 */
                private String skuCode;
                /**
                 * 店铺权重信息
                 */
                private List<InventoryAllocShop> InventoryAllocShops;
                /**
                 * 普通商品在售的平台商品SKU
                 */
                private List<PlatformItemSku> platformItemSkus;
                /**
                 * 包含此商品SKU的组合装在售的平台商品SKU
                 */
                private List<PlatformItemSku> platformItemSkus1;
                /**
                 * 平台商品映射
                 */
                private Map<Long, PlatformItem> platformItemMap;
                /**
                 * 店铺可分配库存信息
                 */
                private List<ShopSpecAllocableStockVO> shopSpecAllocableStocks;
                /**
                 * 锁定库存
                 */
                private BigDecimal lockStock;
                /**
                 * 可分配库存计算过程
                 */
                private List<String> calculations;
        }

        /**
         * 普通商品库存分配上下文
         */
        @Data
        @Accessors(chain = true)
        private static class RegularItemAllocShopContext {
                private RegularItemAllocContext parentContext;
                private InventoryAllocShop inventoryAllocShop;
                /**
                 * 普通商品在售的平台商品SKU
                 */
                private List<PlatformItemSku> platformItemSkus;
                /**
                 * 包含此商品SKU的组合装在售的平台商品SKU
                 */
                private List<PlatformItemSku> platformItemSkus1;
                /**
                 * 店铺可分配库存信息
                 */
                private List<ShopSpecAllocableStockVO> shopSpecAllocableStocks;
                /**
                 * 店铺可分配库存计算过程
                 */
                private List<String> calculations;
                /**
                 * 计算平台商品规格权重分配
                 */
                private WeightCalculation weightCalculation;

                public RegularItemAllocShopContext(RegularItemAllocContext parentContext,
                                InventoryAllocShop inventoryAllocShop) {
                        this.parentContext = parentContext;
                        this.inventoryAllocShop = inventoryAllocShop;
                        this.platformItemSkus = parentContext.platformItemSkus.stream()
                                        .filter(sku -> sku.getShopNo().equals(inventoryAllocShop.getShopNo()))
                                        .collect(Collectors.toList());
                        this.platformItemSkus1 = parentContext.platformItemSkus1.stream()
                                        .filter(sku -> sku.getShopNo().equals(inventoryAllocShop.getShopNo()))
                                        .collect(Collectors.toList());
                        this.shopSpecAllocableStocks = parentContext.shopSpecAllocableStocks.stream()
                                        .filter(v -> v.getShopNo().equals(inventoryAllocShop.getShopNo()))
                                        .collect(Collectors.toList());
                }

                public List<String> getContextCalculations() {
                        ArrayList<String> contextCalculations = new ArrayList<>();
                        if (parentContext != null) {
                                contextCalculations.addAll(parentContext.getCalculations());
                        }
                        if (this.calculations != null) {
                                contextCalculations.addAll(this.calculations);
                        }
                        return contextCalculations;
                }
        }
}
