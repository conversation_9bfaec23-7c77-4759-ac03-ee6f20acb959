package com.daddylab.supplier.item.domain.category.gateway;

import com.alibaba.cola.exception.BizException;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryAttr;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/27 4:26 下午
 * @description
 */
public interface CategoryGateway {

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    Category getById(Long id);

    Category getRootCategory(Long id);

    /**
     * 保存数据
     *
     * @param category
     */
    Long saveDo(Category category);

    /**
     * 查询同等级品类，名字是否重复
     *
     * @param name  品类名称
     * @param level 品类登记
     * @return true:重复
     */
    Boolean isNameRepeat(String name, Integer level);

    /**
     * 根据品类id查询属性
     *
     * @param categoryId
     * @return
     */
    Set<CategoryAttr> getAttrListByCategoryId(Long categoryId);

    List<Category> getUpperCategoryList(Long categoryId);
    
    void removeById(Long categoryId);

    boolean canDelete(Long categoryId);

    void removeAttrById(Long attrId);

    boolean canDeleteAttr(Long attrId);

    Boolean isShotNameRepeat(String abb);

    Boolean isLastLevel(Long id);

    Boolean isHaveAttr(String path);

    void setKingDeeId(Long id, String kingDeeId);

    void removeKingDeeId(Long id);

    Map<Long, String> names(List<Long> ids);

    Map<Long, String> paths(List<Long> ids);

    /**
     * 在确保数据安全的情况下修改商品类目
     *
     * @param itemId 商品ID
     * @param newCategoryId 新类目ID
     * @return 是否修改成功
     */
    boolean modifyItemCategory(Long itemId, Long newCategoryId);

    void modifyItemAndSkuAttr(Long itemId, Long categoryId, Long newCategoryId)
            throws BizException;

    Category getByPath(String path);
}
