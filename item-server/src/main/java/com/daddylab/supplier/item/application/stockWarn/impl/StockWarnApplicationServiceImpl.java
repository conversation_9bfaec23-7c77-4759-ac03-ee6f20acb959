package com.daddylab.supplier.item.application.stockWarn.impl;

import com.daddylab.supplier.item.application.stockWarn.StockWarnApplicationService;
import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnInfo;
import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnSummary;
import com.daddylab.supplier.item.domain.stockWarn.service.StockWarnDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockWarnRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockWarnItemDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockWarnRecordService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockWarnItemDetailService;
import com.daddylab.supplier.item.infrastructure.email.EmailService;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.config.StockWarnConfig;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.URLUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.InputStream;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 库存告警应用服务实现
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Slf4j
@Service
public class StockWarnApplicationServiceImpl implements StockWarnApplicationService {

    @Autowired
    private StockWarnDomainService stockWarnDomainService;

    @Autowired
    private IStockWarnRecordService stockWarnRecordService;

    @Autowired
    private IStockWarnItemDetailService stockWarnItemDetailService;

    @Autowired
    private EmailService emailService;

    @Autowired
    private OssGateway ossGateway;

    @Autowired
    private StockWarnConfig stockWarnConfig;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeStockWarnCheck() {
        log.info("[库存告警检查] 开始执行库存告警检查任务");
        
        try {
            // 1. 检查库存告警
            List<StockWarnInfo> warnInfos = stockWarnDomainService.checkStockWarn();
            if (warnInfos.isEmpty()) {
                log.info("[库存告警检查] 没有发现库存告警商品");
                return;
            }

            // 2. 按订单员汇总告警信息
            List<StockWarnSummary> summaries = stockWarnDomainService.summarizeWarnByOrderPersonnel(warnInfos);
            log.info("[库存告警检查] 发现 {} 个订单员需要发送告警邮件", summaries.size());

            // 3. 为每个订单员生成Excel并保存记录
            for (StockWarnSummary summary : summaries) {
                // 生成Excel文件并上传到OSS
                String ossUrl = stockWarnDomainService.generateAndUploadExcel(summary);
                
                // 保存告警记录
                StockWarnRecord record = new StockWarnRecord();
                record.setTargetUserId(summary.getOrderPersonnelId());
                record.setTargetUserName(summary.getOrderPersonnelName());
                record.setEmailAddress(summary.getOrderPersonnelEmail());
                record.setSendStatus(0); // 待发送
                record.setAttachmentOssUrl(ossUrl);
                stockWarnRecordService.save(record);

                // 保存告警商品明细
                List<StockWarnItemDetail> details = summary.getWarnItems().stream()
                    .map(item -> {
                        StockWarnItemDetail detail = new StockWarnItemDetail();
                        detail.setWarnRecordId(record.getId());
                        detail.setItemCode(item.getItemCode());
                        detail.setItemName(item.getItemName());
                        detail.setCategoryId(item.getCategoryId());
                        detail.setCategoryName(item.getCategoryName());
                        detail.setCurrentStock(item.getCurrentStock());
                        detail.setWarnStock(item.getWarnStock());
                        detail.setWarehouseId(item.getWarehouseId());
                        detail.setWarehouseName(item.getWarehouseName());
                        return detail;
                    })
                    .collect(Collectors.toList());
                stockWarnItemDetailService.saveBatch(details);
            }

            log.info("[库存告警检查] 库存告警检查任务执行完成，共生成 {} 条告警记录", summaries.size());
            
        } catch (Exception e) {
            log.error("[库存告警检查] 库存告警检查任务执行失败", e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void executeStockWarnEmailSend() {
        log.info("[库存告警邮件发送] 开始执行库存告警邮件发送任务");
        
        try {
            // 查询待发送的告警记录
            List<StockWarnRecord> pendingRecords = stockWarnRecordService.lambdaQuery()
                .eq(StockWarnRecord::getSendStatus, 0)
                .list();

            if (pendingRecords.isEmpty()) {
                log.info("[库存告警邮件发送] 没有待发送的告警邮件");
                return;
            }

            int successCount = 0;
            int failCount = 0;

            for (StockWarnRecord record : pendingRecords) {
                // 确定实际发送的邮箱地址
                String actualEmailAddress = getActualEmailAddress(record.getEmailAddress());
                
                try {
                    // 构建邮件内容
                    String subject = String.format("仓库商品库存告警【%s-%s】", 
                        record.getTargetUserName(), 
                        java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd")));
                    
                    String content = String.format(
                        "仓库负责人 【%s】你好，\n\n" +
                        "上一周期内，你所负责的部分商品库存告警，请联系供应商确认最新库存，并进行更新，谢谢（见附件）",
                        record.getTargetUserName()
                    );

                    // 发送带附件的邮件
                    String attachmentName = String.format("%s_库存告警明细_%s.xlsx", 
                        record.getTargetUserName(),
                        java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd")));
                    
                    // 从OSS下载附件
                    byte[] attachmentBytes = downloadAttachmentFromOss(record.getAttachmentOssUrl());
                    
                    emailService.sendAttachmentMail(actualEmailAddress, subject, content, 
                        attachmentBytes, attachmentName);
                    
                    // 更新发送状态为成功
                    record.setSendStatus(1);
                    record.setSendTime(System.currentTimeMillis());
                    stockWarnRecordService.updateById(record);
                    
                    successCount++;
                    log.info("[库存告警邮件发送] 邮件发送成功，收件人：{}", actualEmailAddress);
                    
                } catch (Exception e) {
                    // 更新发送状态为失败
                    record.setSendStatus(2);
                    record.setErrorMessage(e.getMessage());
                    stockWarnRecordService.updateById(record);
                    
                    failCount++;
                    log.error("[库存告警邮件发送] 邮件发送失败，收件人：{}，错误：{}", 
                        actualEmailAddress, e.getMessage());
                }
            }

            log.info("[库存告警邮件发送] 库存告警邮件发送任务执行完成，成功：{}，失败：{}", successCount, failCount);
            
        } catch (Exception e) {
            log.error("[库存告警邮件发送] 库存告警邮件发送任务执行失败", e);
            throw e;
        }
    }

    @Override
    public Integer manualTriggerStockWarnCheck() {
        log.info("[手动触发库存告警检查] 开始执行");
        executeStockWarnCheck();
        
        // 返回生成的告警记录数量
        return stockWarnRecordService.lambdaQuery()
            .eq(StockWarnRecord::getSendStatus, 0)
            .count()
            .intValue();
    }

    @Override
    public Integer manualTriggerStockWarnEmailSend() {
        log.info("[手动触发库存告警邮件发送] 开始执行");
        
        // 查询待发送的记录数量
        int pendingCount = stockWarnRecordService.lambdaQuery()
            .eq(StockWarnRecord::getSendStatus, 0)
            .count()
            .intValue();
        
        executeStockWarnEmailSend();
        
        return pendingCount;
    }

    /**
     * 从OSS下载附件
     */
    private byte[] downloadAttachmentFromOss(String attachmentUrl) {
        if (!StringUtils.hasText(attachmentUrl)) {
            log.warn("[OSS下载] 附件URL为空");
            return null;
        }

        try {
            log.info("[OSS下载] 开始下载附件: {}", attachmentUrl);
            
            // 从URL中提取路径信息
            String path = extractPathFromUrl(attachmentUrl);
            if (!StringUtils.hasText(path)) {
                log.warn("[OSS下载] 无法从URL中提取路径: {}", attachmentUrl);
                return null;
            }

            // 判断是否为私有文件（根据URL判断）
            boolean isPrivate = attachmentUrl.contains("private") || attachmentUrl.contains("private-bucket");
            
            // 从OSS下载文件
            InputStream attachmentStream = ossGateway.get(isPrivate, path);
            if (attachmentStream != null) {
                // 将流转换为字节数组
                byte[] attachmentBytes = IoUtil.readBytes(attachmentStream);
                attachmentStream.close();
                
                log.info("[OSS下载] 附件下载成功，大小: {} bytes", attachmentBytes.length);
                return attachmentBytes;
            } else {
                log.warn("[OSS下载] 无法从OSS下载附件: {}", attachmentUrl);
                return null;
            }
        } catch (Exception e) {
            log.error("[OSS下载] 下载附件失败: {}", attachmentUrl, e);
            return null;
        }
    }

    /**
     * 从URL中提取文件路径
     */
    private String extractPathFromUrl(String url) {
        if (!StringUtils.hasText(url)) {
            return null;
        }
        
        try {
            // 如果URL包含域名，提取路径部分
            if (url.contains("://")) {
                return URLUtil.getPath(url);
            }
            // 如果已经是路径，直接返回
            return url.startsWith("/") ? url.substring(1) : url;
        } catch (Exception e) {
            log.error("[提取路径] 从URL提取路径失败: {}", url, e);
            return null;
        }
    }

    /**
     * 获取实际发送的邮箱地址
     * 如果配置了 debugEmail，则所有邮件都发送到 debugEmail
     * 否则发送到原始邮箱地址
     */
    private String getActualEmailAddress(String originalEmail) {
        if (StringUtils.hasText(stockWarnConfig.getDebugEmail())) {
            log.info("[库存告警邮件发送] 使用调试邮箱，原始邮箱：{} -> 调试邮箱：{}", 
                originalEmail, stockWarnConfig.getDebugEmail());
            return stockWarnConfig.getDebugEmail();
        }
        return originalEmail;
    }
}
