package com.daddylab.supplier.item.infrastructure.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 库存告警配置
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Data
@Component
@ConfigurationProperties(prefix = "stock.warn")
public class StockWarnConfig {

    /**
     * 调试邮箱地址，当配置此邮箱时，所有库存告警邮件都会发送到此邮箱
     * 用于测试环境或开发环境，避免向真实用户发送测试邮件
     */
    private String debugEmail;
}
