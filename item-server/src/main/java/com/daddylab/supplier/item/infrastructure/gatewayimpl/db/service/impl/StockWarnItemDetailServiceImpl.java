package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockWarnItemDetail;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockWarnItemDetailMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockWarnItemDetailService;
import org.springframework.stereotype.Service;

/**
 * 库存告警商品明细表 服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Service
public class StockWarnItemDetailServiceImpl extends ServiceImpl<StockWarnItemDetailMapper, StockWarnItemDetail> implements IStockWarnItemDetailService {

}
