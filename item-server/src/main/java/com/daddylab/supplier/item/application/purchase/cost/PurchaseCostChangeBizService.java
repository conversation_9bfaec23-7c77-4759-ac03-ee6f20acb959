package com.daddylab.supplier.item.application.purchase.cost;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.purchase.cost.dto.CostFormDto;
import com.daddylab.supplier.item.application.purchase.cost.dto.CostPageQuery;
import com.daddylab.supplier.item.application.purchase.cost.dto.CostPageVo;
import com.daddylab.supplier.item.application.purchase.cost.dto.MultiSkuPriceFullDTO;
import com.daddylab.supplier.item.common.validation.annotations.NullOrNotBlank;
import com.daddylab.supplier.item.domain.daddylabWorkbench.WorkbenchCallbackEvent;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PurchaseCostChangeProcessForm;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface PurchaseCostChangeBizService {

  PageResponse<CostPageVo> page(CostPageQuery pageQuery);

  void export(CostPageQuery pageQuery);

  SingleResponse<CostFormDto> viewForm(Long id);

  SingleResponse<Long> save(CostFormDto dto);

  SingleResponse<Boolean> submit(Long id);

  SingleResponse<Boolean> delete(Long id);

  List<MultiSkuPriceFullDTO> importExcel(MultipartFile file, Integer type);

  PurchaseCostChangeProcessForm buildWorkbenchForm(Long id);

  void workbenchCallback(WorkbenchCallbackEvent event);

  /**
   * 撤回审核
   *
   * @param id
   * @return
   */
  SingleResponse<Boolean> cancel(Long id);

  SingleResponse<Boolean> updateStatus(Long id, Integer status);
}
