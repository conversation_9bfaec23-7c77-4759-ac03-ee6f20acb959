package com.daddylab.supplier.item.controller.item.dto;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.GoodsType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.SkuSplitType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/20 3:48 下午
 * @description
 */
@Data
@ApiModel("sku属性封装")
public class ItemSkuListDto implements Serializable {

    private static final long serialVersionUID = 465138107026033577L;
    @ApiModelProperty("sku id。新建不填，编辑必填。")
    Long id;

    @ApiModelProperty("商品id id。新建不填，编辑必填。")
    private Long itemId;

    @ApiModelProperty("商品sku")
    private String skuCode;

    @ApiModelProperty("商品条形码")
    private String barCode;

    @Valid
    @ApiModelProperty("属性列表list")
    List<ItemAttrDto> attrList;

    @ApiModelProperty("采购成本")
    String procurement;

    public String getProcurement() {
        return procurement.replaceAll(" ", "");
    }

    @ApiModelProperty("采购成本价格开始时间")
    Long procurementStartDt;

    @ApiModelProperty("采购成本价格结束时间")
    Long procurementEndDt;

    /**
     *
     */
    @ApiModelProperty("销售价/产品日销价")
    String sales;

    @ApiModelProperty("供货商制定sku")
    String specifiedSkuCode;

    @ApiModelProperty("sku库存")
    Long stockCount;

    @ApiModelProperty("单位")
    String unit;

    @ApiModelProperty("供应商id")
    Long providerId;

    @ApiModelProperty("仓库编号")
    String warehouseNo;

    @ApiModelProperty("税率")
    BigDecimal taxRate;

    @ApiModelProperty("采购税率")
    BigDecimal purchaseTaxRate;

    @ApiModelProperty("拆分类型")
    private SkuSplitType splitType;

    /**
     * 商品SKU额外拓展属性
     */
    @ApiModelProperty("商品SKU额外拓展属性")
    private LinkedHashMap<String, Object> props;

    /**
     * 平台佣金
     */
    @ApiModelProperty("平台佣金")
    private SkuExtraPriceDto platformCommission;

    @ApiModelProperty("货品类型。FRESH生鲜 APPLIANCE 大家电 FURNITURE 大家具 INSTALLATION_REQUIRED 需要安装")
    private GoodsType goodsType;

    @ApiModelProperty("是否删除")
    private Boolean isDel;

    /**
     * 合同销售价
     */
    @ApiModelProperty("合同销售价")
    private SkuExtraPriceDto contractSalePrice;
    
    /**
     * 重量(kg)
     */
    @ApiModelProperty("重量(kg)")
    private BigDecimal weight;
    
    /**
     * 长度(cm)
     */
    @ApiModelProperty("长度(cm)")
    private BigDecimal length;
    
    /**
     * 宽度(cm)
     */
    @ApiModelProperty("宽度(cm)")
    private BigDecimal width;
    
    /**
     * 高度(cm)
     */
    @ApiModelProperty("高度(cm)")
    private BigDecimal height;
    
    /**
     * 体积(m³)
     */
    @ApiModelProperty("体积(m³)")
    private BigDecimal volume;

    public String buildSpecifications() {
        if (CollectionUtil.isEmpty(attrList)) {
            return "";
        }
        List<String> list = new LinkedList<>();
        for (ItemAttrDto itemAttrDto : attrList) {
            list.add(itemAttrDto.getName() + ":" + itemAttrDto.getValue());
        }
        return StrUtil.join("|", list);
    }


}