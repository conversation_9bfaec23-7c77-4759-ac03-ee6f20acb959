package com.daddylab.supplier.item.common.trans;

import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuWithPriceVO;
import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ComposeSkuDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/1/7 8:53 下午
 * @description
 */
@Mapper
public interface SkuTransMapper {

  SkuTransMapper INSTANCE = Mappers.getMapper(SkuTransMapper.class);

  @Mappings({
    @Mapping(target = "corpBizType", ignore = true),
    @Mapping(target = "specificationStr", source = "specifications"),
    @Mapping(target = "specifications", ignore = true),
    @Mapping(target = "procurement", ignore = true)
  })
  ComposeSkuWithPriceVO toVoWithPrice(ComposeSkuDO dto);

  @Mappings({
    @Mapping(target = "corpBizType", ignore = true),
    @Mapping(target = "specificationStr", source = "specifications"),
    @Mapping(target = "specifications", ignore = true)
  })
  ComposeSkuVO toVoNoPrice(ComposeSkuDO dto);

  @Mappings({
    @Mapping(target = "id", source = "skuId"),
    @Mapping(target = "value", source = "attrValue"),
    @Mapping(target = "name", source = "attrName"),
  })
  ItemAttrDto toAttrDto(SkuAttrRefDO param);

  List<ItemAttrDto> toAttrDtos(List<SkuAttrRefDO> param);
}
