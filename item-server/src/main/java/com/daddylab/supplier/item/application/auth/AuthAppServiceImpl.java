package com.daddylab.supplier.item.application.auth;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTPayload;
import cn.hutool.jwt.JWTUtil;
import com.alibaba.cola.exception.Assert;
import com.alibaba.cola.exception.ExceptionFactory;
import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.auth.entity.SysMenu;
import com.daddylab.supplier.item.domain.auth.gateway.AuthGateway;
import com.daddylab.supplier.item.domain.auth.gateway.LoginGateway;
import com.daddylab.supplier.item.domain.auth.gateway.UserContextGateway;
import com.daddylab.supplier.item.domain.auth.types.LoginState;
import com.daddylab.supplier.item.domain.auth.types.TicketSession;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext.UserInfo;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AuthAppServiceImpl implements AuthAppService {

    @Autowired
    UserGateway userGateway;
    @Autowired
    UserContextGateway userContextGateway;
    @Qualifier("UserContextGatewayDbImpl")
    @Autowired
    UserContextGateway userContextDbGateway;

    @Autowired
    private AuthGateway authGateway;
    @Autowired
    private LoginGateway loginGateway;

    @Autowired
    private RefreshConfig refreshConfig;

    private static String standardizeUri(String uri) {
        final String prefix = "/supplier/item";
        return StringUtil.trimEnd(StringUtil.trimStart(uri, prefix), "/");
    }

    public static void main(String[] args){
        final String key = "QgUr43KWkZjUpslP1wy9qTZHc9TErYQw";
        final byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
        final String token = JWT.create()
                                .setKey(keyBytes)
                                .setIssuer("erp")
                                .setPayload("uid", "7142127")
                                .setExpiresAt(
                        Date.from(
                                LocalDateTime.now()
                                             .plus(60, ChronoUnit.MINUTES)
                                             .atZone(ZoneId.systemDefault())
                                             .toInstant()))
                                .sign();
        System.out.println(token);
        final JWT jwt = JWTUtil.parseToken(token);
        System.out.println(Long.parseLong(jwt.getPayload("uid").toString()));
        jwt.setKey(keyBytes);
        System.out.println(jwt.validate(0));
        System.out.println("https://p-gray.daddylab.com/erp/login?ticket=" + token);
//        System.out.println("http://p.dlab.cn/erp/login?ticket=" + token);
    }

    @Override
    public Optional<LoginState> casAuthLogin(String service, String ticket) {
        if (StringUtil.isNotBlank(refreshConfig.getInternalAuth())) {
            Optional<LoginState> internalAuthState = internalAuth(ticket);
            if (internalAuthState.isPresent()) return internalAuthState;
        }
        Optional<LoginState> mockLoginState = mockLogin(ticket);
        if (mockLoginState.isPresent()) return mockLoginState;

        final TicketSession ticketSession = authGateway.ticketValidate(service, ticket);
        log.info("CAS登陆验证 resp:{} ticket:{}", ticketSession, ticket);

        if (ticketSession != null) {
            return setLoginState(ticketSession.getUserId());
        }
        return Optional.empty();
    }

    @NonNull
    private Optional<LoginState> internalAuth(String ticket) {
        try {
            final JWT jwt = JWT.of(ticket);
            jwt.setKey(refreshConfig.getInternalAuth().getBytes(StandardCharsets.UTF_8));
            final JSONObject payloads = jwt.getPayloads();
            if ("erp".equals(payloads.getStr(JWTPayload.ISSUER)) && jwt.validate(0)) {
                final Long uid = Long.parseLong(jwt.getPayload("uid").toString());
                return setLoginState(uid);
            }
        } catch (Exception ignored) {
        }
        return Optional.empty();
    }

    @NonNull
    private Optional<LoginState> setLoginState(Long userId) {
        //注册当前用户登录状态
        loginGateway.login(userId);

        //登录成功，构建用户信息上下文
        loadUserContext(userId, true);

        //返回用户登录状态
        final LoginState loginState = LoginState.builder()
                .loginId(userId)
                .token(loginGateway.getTokenValue())
                .tokenExpire(loginGateway.getTokenTimeout())
                .userInfo(UserContext.getUserInfo().orElse(null))
                .build();
        return Optional.of(loginState);
    }

    @XxlJob("setLoginState")
    public void setLoginStateJob() {
        final String jobParam = XxlJobHelper.getJobParam();
        final long userId = Long.parseLong(jobParam);
        final Optional<LoginState> loginState = setLoginState(userId);
        log.info("setLoginState:{}", loginState.orElse(null));
    }


    /**
     * 测试开发使用，模拟登录
     *
     * @param ticket 直接传用户ID
     * @return 登录状态
     */
    private Optional<LoginState> mockLogin(String ticket) {
        if (ApplicationContextUtil.isActiveProfile("local", "dev", "test")
                && ReUtil.isMatch("^\\d+$", ticket)) {
            final Long userId = Long.parseLong(ticket);
            log.debug("MOCK登陆>用户ID:" + userId);

            loginGateway.login(userId);
            loadDbUserContext(userId, true);
            return Optional.of(LoginState.builder()
                    .loginId(userId)
                    .token(loginGateway.getTokenValue())
                    .tokenExpire(loginGateway.getTokenTimeout())
                    .userInfo(UserContext.getUserInfo().orElse(null))
                    .build()
            );
        }
        return Optional.empty();
    }

    /**
     * 构建 用户登信息上下文
     *
     * @param userId          用户id
     * @param refreshUserInfo 刷新用户信息
     */
    @Override
    public void loadUserContext(Long userId, boolean refreshUserInfo) {
        final UserContext.UserInfo userContext = userContextGateway.getUserContext(userId, refreshUserInfo);
        UserContext.setUserHolder(userContext);
    }

    /**
     * 构建 用户登信息上下文
     *
     * @param userId          用户id
     * @param refreshUserInfo 刷新用户信息
     */
    public void loadDbUserContext(Long userId, boolean refreshUserInfo) {
        final UserContext.UserInfo userContext = userContextDbGateway.getUserContext(userId, refreshUserInfo);
        UserContext.setUserHolder(userContext);
    }

    @Override
    public void logout() {
        UserContext.remove();
        loginGateway.logout();
    }

    @Override
    public void verify(Long userId, String uri) {
        final String userIdStr = String.valueOf(userId);
        Assert.isTrue(StringUtils.isNotBlank(userIdStr), "用户ID不应该为空");

        //如果用户角色为空，访问拒绝
        List<Long> roleIds = UserContext.getRoleIds();
        if (CollUtil.isEmpty(roleIds)) {
            throw ExceptionFactory.bizException(ErrorCode.NO_ACCESS.getCode(), "当前用户" + userIdStr + "未被分配角色");
        }

        final boolean canAccessUri = UserContext.hasPermission(standardizeUri(uri));
        if (!canAccessUri) {
            String errorMsg = "当前用户" + userId + "未被分配" + uri + "的访问权限";
            throw ExceptionFactory.bizException(ErrorCode.NO_ACCESS.getCode(), errorMsg);
        }
    }

    @Override
    public UserContext.UserInfo getCurrentUserInfo() {
        final Optional<UserContext.UserInfo> userInfo = UserContext.getUserInfo();
        return userInfo.map(it -> {
            UserInfo copy = new UserInfo();
            BeanUtils.copyProperties(it, copy, "menus");
            final Map<Long, List<String>> aclResourceFilter = refreshConfig.getAclResourceFilter();
            if (aclResourceFilter != null) {
                final List<String> resourceFilters = aclResourceFilter.get(it.getUserId());
                if (resourceFilters != null) {
                    for (String resourceFilter : resourceFilters) {
                        copy.getResourceCodes().forEach((resourceType,resourceCodes) -> {
                            if (resourceFilter.startsWith("-")) {
                                resourceCodes.remove(resourceFilter.substring(1));
                            } else {
                                resourceCodes.add(resourceFilter);
                            }
                        });
                    }
                }
            }
            return copy;
        }).orElseThrow(() -> ExceptionFactory.bizException(ErrorCode.NO_LOGIN.getCode(), ErrorCode.NO_LOGIN.getMsg()));
    }

    @Override
    public List<SysMenu> getCurrentUserMenus(Boolean filterNoAuth) {
        final Optional<UserContext.UserInfo> userInfo = UserContext.getUserInfo();
        return userInfo.map(it -> it.getMenus(filterNoAuth))
                .orElseThrow(() -> ExceptionFactory.bizException(ErrorCode.NO_LOGIN.getCode(), ErrorCode.NO_LOGIN.getMsg()));
    }


}
