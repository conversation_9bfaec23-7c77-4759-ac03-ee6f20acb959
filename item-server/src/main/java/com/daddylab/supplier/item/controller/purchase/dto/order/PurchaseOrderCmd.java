package com.daddylab.supplier.item.controller.purchase.dto.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.Command;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.purchasePayable.dto.ArtificialPayOrderDetailSaveDto;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.messageRobot.enums.MessageRobotCode;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationProviderDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OrganizationMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICombinationItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IComposeSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
import com.daddylab.supplier.item.infrastructure.utils.Alert;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.types.contract.ContractDropdownItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;
import org.springframework.util.Assert;

/**
 * <AUTHOR> up
 * @date 2022/3/28 10:21 上午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("保存采购订单信息请求参数")
public class PurchaseOrderCmd extends Command {
  private static final long serialVersionUID = -7547184067861061187L;

  @ApiModelProperty(value = "采购订单id")
  private Long id;

  private String no;

  @ApiModelProperty(value = "采购类型。1：标准采购，2：工厂采购")
  @NotNull(message = "采购类型不得为空")
  private Integer type;

  @ApiModelProperty(value = "供应商id")
  @NotNull(message = "供应商不得为空")
  private Long providerId;

  @ApiModelProperty(value = "采购组织")
  @NotNull(message = "采购组织不得为空")
  private Long organizationId;

  @ApiModelProperty(value = "采购部门")
  private Long departmentId;

  @ApiModelProperty(value = "采购组")
  //    @NotNull(message = "采购组不得为空")
  private Long groupId;

  @ApiModelProperty(value = "合同编号")
  private String contractNo;

  @ApiModelProperty(value = "合同期限开始时间")
  private Long contractStartTime;

  @ApiModelProperty(value = "合同期限结束时间")
  private Long contractEndTime;

  @ApiModelProperty(value = "合同id")
  private Long contractId;

  @ApiModelProperty(value = "采购员id")
  @NotNull(message = "采购员不得为空")
  private Long buyerUserId;

  @ApiModelProperty(value = "采购员名称")
  @NotNull(message = "采购员名称不得为空")
  private String buyerUserName;

  @ApiModelProperty(value = "到货方式。1.一次性到货，2.分批到货")
  @NotNull(message = "到货方式不得为空")
  private Integer arrivalType;

  @ApiModelProperty(
      value = "付款条件。1:款到发货、2:预付10%、3:预付20%、4:预付30%、5:预付40%、6:预付50%、7:预付60%、8:货到付款、9:月结")
  @NotNull(message = "付款条件不得为空")
  private Integer payType;

  @ApiModelProperty(value = "采购日期")
  @NotNull(message = "采购日期不得为空")
  private Long purchaseDate;

  @ApiModelProperty(value = "备注")
  @Length(max = 200)
  private String remark;

  @ApiModelProperty("采购订单明细对象")
  @Valid
  List<PurchaseOrderDetailCmd> detailCmdList;

  @ApiModelProperty private Integer businessLine;

  private Integer source;

  /** 采购总数量 */
  private Integer totalPurchaseQuantity;

  /** 采购总金额(税前) */
  private String totalPurchaseTaxAmount;

  /** 采购总金额(税后) */
  private String totalPurchaseAmount;

  /** 总税额 */
  private String totalTaxAmount;

  private static final DecimalFormat df = new DecimalFormat("0000");

  private String getOrderNo() {
    // 新建单生成no,按照采购首字母大写CG+年后2位+月+日+0001，例如：CG+22+01+09+0001，CG2201090001
    String codePrefix = "CG" + cn.hutool.core.date.DateUtil.today().substring(2).replace("-", "");
    String initOrderNo = codePrefix + "0001";
    AtomicReference<String> reference = new AtomicReference<>(initOrderNo);

    IPurchaseOrderService purchaseOrderService = SpringUtil.getBean(IPurchaseOrderService.class);
    Optional<PurchaseOrder> optional = purchaseOrderService.getLatestCode(codePrefix);
    optional.ifPresent(
        purchaseOrder -> {
          String no = optional.get().getNo();
          String s = no.replaceAll(codePrefix, "");
          reference.set(codePrefix + df.format(Long.parseLong(s) + 1));
        });

    return reference.get();
  }

  private void amountStatistics() {
    // 计算采购订单总数量
    this.totalPurchaseQuantity =
        detailCmdList.stream().mapToInt(PurchaseOrderDetailCmd::getPurchaseQuantity).sum();
    // 采购总金额(税前)
    this.totalPurchaseTaxAmount =
        detailCmdList.stream()
            .map(PurchaseOrderDetailCmd::getTotalPriceTax)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .setScale(6, RoundingMode.DOWN)
            .toString();
    // 采购总金额(税后)
    this.totalPurchaseAmount =
        detailCmdList.stream()
            .map(PurchaseOrderDetailCmd::getAfterTaxAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .setScale(6, RoundingMode.DOWN)
            .toString();
    // 总税额
    this.totalTaxAmount =
        detailCmdList.stream()
            .map(PurchaseOrderDetailCmd::getTaxQuota)
            .reduce(BigDecimal.ZERO, BigDecimal::add)
            .setScale(6, RoundingMode.DOWN)
            .toString();
  }

  public void initialization() {
    if (Objects.isNull(this.id)) {
      this.no = getOrderNo();
    }

    initCmd();
    amountStatistics();
  }

  public void initializationBySys() {
    if (Objects.isNull(this.id)) {
      this.no = getOrderNo();
    }

    detailCmdList.forEach(PurchaseOrderDetailCmd::initialization);
    amountStatistics();
  }

  public void initializationForFixed() {
    if (Objects.isNull(this.id)) {
      this.no = getOrderNo();
    }

    detailCmdList.forEach(PurchaseOrderDetailCmd::initializationForFixed);
    amountStatistics();
  }

  private void initCmd() {
    Assert.state(CollUtil.isNotEmpty(detailCmdList), "采购明细不得为空");

    CopyOnWriteArrayList<String> errorPriceCodeList = new CopyOnWriteArrayList<>();
    CopyOnWriteArrayList<String> errorSkuCodeList = new CopyOnWriteArrayList<>();
    detailCmdList.parallelStream()
        .forEach(
            detail -> {
              // 明细初始化
              detail.initialization();
              // 验证sku价格是否超标
              if (detail.getTaxPrice().compareTo(new BigDecimal("99999999")) > 0) {
                errorPriceCodeList.add(detail.getItemSkuCode());
              }
              if (detail.getItemSkuCode().length() > 30) {
                errorSkuCodeList.add(detail.getItemSkuCode());
              }
            });
    Assert.state(
        CollectionUtils.isEmpty(errorPriceCodeList),
        "skuCode单价非法。" + StrUtil.join(",", errorPriceCodeList));
    Assert.state(
        CollectionUtils.isEmpty(errorSkuCodeList),
        "skuCode非法(可能是脏数据)。" + StrUtil.join(",", errorSkuCodeList));

    Map<String, Long> collect =
        detailCmdList.stream()
            .map(val -> val.getItemSkuCode() + "_" + val.getTaxPrice() + "_" + val.getIsGift())
            .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));
    collect.forEach(
        (k, v) -> {
          if (v > 1) {
            String msg = "采购明细数据重复，SKU编码+税后单价+赠品 三者构成为唯一约束。";
            String skuCode = k.split("_")[0];
            throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, msg + "重复编码：" + skuCode);
          }
        });

    // 验证skuCode和provider的关系。
    List<String> codeList =
        detailCmdList.stream()
            .map(PurchaseOrderDetailCmd::getItemSkuCode)
            .collect(Collectors.toList());
    // 单个商品
    List<String> skuCodeList =
        codeList.stream().filter(val -> !val.startsWith("MU")).collect(Collectors.toList());
    List<String> errorProviderCodeList = new LinkedList<>();
    if (CollectionUtils.isNotEmpty(skuCodeList)) {
      IItemSkuService itemSkuService = SpringUtil.getBean(IItemSkuService.class);
      List<ItemSku> list =
          itemSkuService
              .lambdaQuery()
              .eq(ItemSku::getProviderId, this.providerId)
              .select(ItemSku::getSkuCode, ItemSku::getProviderSpecifiedCode)
              .list();
      List<String> passSkuCodeList = new LinkedList<>();
      list.forEach(
          val -> {
            passSkuCodeList.add(val.getSkuCode());
            passSkuCodeList.add(val.getProviderSpecifiedCode());
          });
      errorProviderCodeList =
          skuCodeList.stream()
              .filter(code -> !passSkuCodeList.contains(code))
              .collect(Collectors.toList());
    }
    Assert.state(
        CollectionUtils.isEmpty(errorProviderCodeList),
        "providerId:"
            + this.providerId
            + "，skuCode非法，以下sku不属于此供应商。"
            + StrUtil.join(",", errorProviderCodeList));
    // 组合商品
    List<String> combinationCodeList =
        codeList.stream().filter(val -> val.startsWith("MU")).collect(Collectors.toList());
    if (CollectionUtils.isNotEmpty(combinationCodeList)) {
      Map<String, List<Long>> errorMap = new HashMap<>(8);
      ICombinationItemService combinationItemService =
          SpringUtil.getBean(ICombinationItemService.class);
      List<CombinationProviderDO> combinationProviderList =
          combinationItemService.queryProviderList(combinationCodeList);
      Map<String, String> combinationCodeMap =
          combinationProviderList.stream()
              .collect(
                  Collectors.toMap(
                      CombinationProviderDO::getItemCode,
                      CombinationProviderDO::getProviderIdList));
      combinationCodeList.forEach(
          code -> {
            String providerList = combinationCodeMap.get(code);
            Set<Long> detailDbIdList =
                Arrays.stream(providerList.split(","))
                    .map(String::trim)
                    .map(Long::valueOf)
                    .collect(Collectors.toSet());
            List<Long> errorProviderList =
                detailDbIdList.stream()
                    .filter(val -> !val.equals(providerId))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(errorProviderList)) {
              errorMap.put(code, errorProviderList);
            }
          });

      if (MapUtils.isNotEmpty(errorMap)) {
        String msg =
            "组合商品数据告警，以下组合商品，在订单中下属单品均属于一个供应商，采购单生成校验时，发现下属单品供应商不一致。" + JsonUtil.toJson(errorMap);
        Alert.text(MessageRobotCode.GLOBAL, msg);
      }
    }
  }

  /**
   * @param providerId
   * @param type 采购类型。1：标准采购，2：工厂采购，3：辅助功能，为了冲销原本的应付单，生成后续修正过的出入库单
   * @param remark 备注
   * @return
   */
  public static PurchaseOrderCmd buildCmdOne(
      Long buyerUserId, Long providerId, Integer type, String remark) {
    PurchaseOrderCmd cmd = new PurchaseOrderCmd();
    cmd.setType(Objects.isNull(type) ? 2 : type);
    cmd.setProviderId(providerId);
    cmd.setOrganizationId(SpringUtil.getBean(OrganizationMapper.class).getRootOrganizationId());
    cmd.setDepartmentId(0L);
    cmd.setGroupId(0L);
    cmd.setContractNo("");
    cmd.setBuyerUserId(buyerUserId);
    cmd.setArrivalType(1);
    cmd.setPayType(9);
    cmd.setPurchaseDate(com.daddylab.supplier.item.infrastructure.utils.DateUtil.currentTime());
    cmd.setRemark(remark);
    cmd.setSource(1);
    return cmd;
  }

  public static List<PurchaseOrderDetailCmd> buildDetailListCmd(
      String warehouseNo, List<WdtOrderDetailWrapper> wrapperList) {

    List<PurchaseOrderDetailCmd> detailCmdList = new LinkedList<>();
    wrapperList.forEach(
        wrapper -> {
          boolean isSingle = StringUtils.isBlank(wrapper.getSuiteNo());
          if (isSingle) {
            PurchaseOrderDetailCmd detailCmd = new PurchaseOrderDetailCmd();
            String skuCode = wrapper.getSkuCode();
            ItemSku itemSku = SpringUtil.getBean(ItemSkuGateway.class).getBySkuCode(skuCode);
            Item item = SpringUtil.getBean(ItemGateway.class).getItem(itemSku.getItemId());
            detailCmd.setItemSkuCode(skuCode);
            detailCmd.setItemId(item.getId());
            detailCmd.setSpecifications(
                SpringUtil.getBean(ItemSkuGateway.class).getSkuAttrListStr(skuCode));
            detailCmd.setBarCode(itemSku.getBarCode());
            detailCmd.setUnit(StringUtils.isBlank(itemSku.getUnit()) ? "个" : itemSku.getUnit());
            detailCmd.setPurchaseQuantity(wrapper.getQuantity());
            detailCmd.setTaxPrice(wrapper.getPrice());
            BigDecimal taxRate =
                Objects.isNull(itemSku.getPurchaseTaxRate())
                    ? new BigDecimal("0.13")
                    : itemSku.getTaxRate();
            detailCmd.setTaxRate(taxRate);
            detailCmd.setIsGift(wrapper.getIsGift());
            detailCmd.setWarehouseNo(warehouseNo);
            detailCmdList.add(detailCmd);
          } else {
            PurchaseOrderDetailCmd detailCmd = new PurchaseOrderDetailCmd();
            String suiteNo = wrapper.getSuiteNo();
            // 判断这组合商品code是否存在于erp组合商品表中
            CombinationItem combinationItem =
                SpringUtil.getBean(ICombinationItemService.class).getByItemCode(suiteNo);
            boolean isCombinationItem = Objects.nonNull(combinationItem);

            ItemSku itemSku = new ItemSku();
            boolean isItemSku = false;

            if (!isCombinationItem) {
              return;
            }

            String unit = "个";
            detailCmd.setWarehouseNo(warehouseNo);

            detailCmd.setItemSkuCode(suiteNo);
            if (isCombinationItem) {
              detailCmd.setItemId(combinationItem.getId());
            } else {
              detailCmd.setItemId(itemSku.getId());
            }
            if (isCombinationItem) {
              String shortVoStr =
                  SpringUtil.getBean(IComposeSkuService.class)
                      .getShortVoStr(combinationItem.getId());
              detailCmd.setSpecifications(shortVoStr);
            } else {
              detailCmd.setSpecifications(
                  SpringUtil.getBean(ItemSkuGateway.class).getSkuAttrListStr(suiteNo));
            }
            if (isCombinationItem) {
              detailCmd.setBarCode(combinationItem.getBarCode());
            } else {
              detailCmd.setBarCode(itemSku.getBarCode());
            }
            detailCmd.setUnit(unit);
            detailCmd.setPurchaseQuantity(wrapper.getQuantity());
            detailCmd.setTaxPrice(wrapper.getPrice());
            if (isCombinationItem) {
              detailCmd.setTaxRate(new BigDecimal("0.13"));
            } else {
              BigDecimal taxRate =
                  Objects.isNull(itemSku.getPurchaseTaxRate())
                      ? new BigDecimal("0.13")
                      : itemSku.getTaxRate();
              detailCmd.setTaxRate(taxRate);
            }
            detailCmd.setIsGift(wrapper.getIsGift());
            detailCmd.setWarehouseNo(warehouseNo);
            detailCmdList.add(detailCmd);
          }
        });
    return detailCmdList;
  }

  //  public static PurchaseOrderCmd of(
  //      Long providerId, List<WdtOrderDetailWrapper> wrapperList, Long purchaseDate) {
  //    Assert.state(
  //        CollectionUtils.isNotEmpty(wrapperList),
  //        "providerId:" + providerId + "，供应商关联到的wdt商品信息列表为空，系统无法生成采购订单表");
  //
  //    PurchaseOrderCmd cmd = buildCmdOne(providerId, 2, "");
  //    cmd.setPurchaseDate(purchaseDate);
  //
  //    List<PurchaseOrderDetailCmd> detailCmdList = new LinkedList<>();
  //    Map<String, List<WdtOrderDetailWrapper>> warehouseNoMap =
  //
  // wrapperList.stream().collect(Collectors.groupingBy(WdtOrderDetailWrapper::getWarehouseNo));
  //    warehouseNoMap.forEach(
  //        (warehouseNo, matchWrapperList) -> {
  //          List<PurchaseOrderDetailCmd> purchaseOrderDetailCmdList =
  //              buildDetailListCmd(warehouseNo, matchWrapperList);
  //          detailCmdList.addAll(purchaseOrderDetailCmdList);
  //        });
  //
  //    cmd.setDetailCmdList(detailCmdList);
  //    cmd.initializationBySys();
  //
  //    return cmd;
  //  }

  public static PurchaseOrderCmd sysPurchaseCmdBuild(
      Long buyerUserId,
      Long providerId,
      Integer businessLine,
      List<WdtOrderDetailWrapper> wrapperList,
      Long targetMonthLastTime,
      String warehouseNo,
      ContractDropdownItem contractDropdownItem) {
    PurchaseOrderCmd cmd = buildCmdOne(buyerUserId, providerId, 2, "");
    cmd.setPurchaseDate(targetMonthLastTime);
    List<PurchaseOrderDetailCmd> detailCmdList = demoList(wrapperList, warehouseNo);
    cmd.setDetailCmdList(detailCmdList);
    cmd.initializationBySys();
    cmd.setBusinessLine(businessLine);
    if (Objects.nonNull(contractDropdownItem)) {
      cmd.setContractStartTime(contractDropdownItem.getStartTime());
      cmd.setContractEndTime(contractDropdownItem.getEndTime());
    }
    return cmd;
  }

  public static List<PurchaseOrderDetailCmd> demoList(
      List<WdtOrderDetailWrapper> wrapperList, String warehouseNo) {
    List<PurchaseOrderDetailCmd> detailCmdList = new LinkedList<>();
    wrapperList.forEach(
        wrapper -> {
          PurchaseOrderDetailCmd detailCmd = new PurchaseOrderDetailCmd();
          String skuCode = wrapper.getSkuCode();

          IItemSkuService bean = SpringUtil.getBean(IItemSkuService.class);
          LambdaQueryWrapper<ItemSku> queryWrapper = Wrappers.lambdaQuery();
          queryWrapper
              .eq(ItemSku::getSkuCode, skuCode)
              .or()
              .eq(ItemSku::getProviderSpecifiedCode, skuCode);
          ItemSku itemSku = bean.getOne(queryWrapper);

          Item item = SpringUtil.getBean(ItemGateway.class).getItem(itemSku.getItemId());
          detailCmd.setItemSkuCode(skuCode);
          detailCmd.setItemId(item.getId());
          detailCmd.setSpecifications(
              SpringUtil.getBean(ItemSkuGateway.class).getSkuAttrListStr(skuCode));
          detailCmd.setBarCode(itemSku.getBarCode());
          detailCmd.setUnit(StringUtils.isBlank(itemSku.getUnit()) ? "个" : itemSku.getUnit());
          detailCmd.setPurchaseQuantity(wrapper.getQuantity());
          detailCmd.setTaxPrice(wrapper.getPrice());

          BigDecimal finalTaxRate =
              Objects.isNull(itemSku.getPurchaseTaxRate())
                  ? Objects.isNull(itemSku.getTaxRate())
                      ? new BigDecimal("0.13")
                      : itemSku.getTaxRate()
                  : itemSku.getPurchaseTaxRate();
          detailCmd.setTaxRate(finalTaxRate);

          detailCmd.setIsGift(item.getIsGift());
          detailCmd.setWarehouseNo(warehouseNo);
          detailCmdList.add(detailCmd);
        });
    return detailCmdList;
  }

  public static PurchaseOrderCmd forFixedPayOrder(
      Long providerId,
      List<ArtificialPayOrderDetailSaveDto> detailSaveDtoList,
      Long fixedTime,
      String warehouseNo) {
    PurchaseOrderCmd cmd = buildCmdOne(-1L, providerId, 2, "人工修正付款单后生成的新采购订单");
    cmd.setPurchaseDate(fixedTime);
    List<PurchaseOrderDetailCmd> detailCmdList = listForFixedPay(detailSaveDtoList, warehouseNo);
    cmd.setDetailCmdList(detailCmdList);
    cmd.initializationBySys();
    return cmd;
  }

  private static List<PurchaseOrderDetailCmd> listForFixedPay(
      List<ArtificialPayOrderDetailSaveDto> detailList, String warehouseNo) {
    return detailList.stream()
        .map(
            detail -> {
              PurchaseOrderDetailCmd detailCmd = new PurchaseOrderDetailCmd();
              String skuCode = detail.getSkuCode();

              IItemSkuService bean = SpringUtil.getBean(IItemSkuService.class);
              LambdaQueryWrapper<ItemSku> queryWrapper = Wrappers.lambdaQuery();
              queryWrapper
                  .eq(ItemSku::getSkuCode, skuCode)
                  .or()
                  .eq(ItemSku::getProviderSpecifiedCode, skuCode);
              ItemSku itemSku = bean.getOne(queryWrapper);

              Item item = SpringUtil.getBean(ItemGateway.class).getItem(itemSku.getItemId());
              detailCmd.setItemSkuCode(skuCode);
              detailCmd.setItemId(item.getId());
              detailCmd.setSpecifications(
                  SpringUtil.getBean(ItemSkuGateway.class).getSkuAttrListStr(skuCode));
              detailCmd.setBarCode(itemSku.getBarCode());
              detailCmd.setUnit(StringUtils.isBlank(itemSku.getUnit()) ? "个" : itemSku.getUnit());
              detailCmd.setPurchaseQuantity(detail.getFixedQuantity());
              //            BigDecimal price = detail.getFixedTotalAmount().divide(new
              // BigDecimal(Math.abs(detail.getFixedQuantity()))
              //                    , 6, RoundingMode.HALF_UP);
              detailCmd.setTaxPrice(detail.getPrice());

              BigDecimal finalTaxRate =
                  Objects.isNull(itemSku.getPurchaseTaxRate())
                      ? Objects.isNull(itemSku.getTaxRate())
                          ? new BigDecimal("0.13")
                          : itemSku.getTaxRate()
                      : itemSku.getPurchaseTaxRate();
              detailCmd.setTaxRate(finalTaxRate);

              detailCmd.setIsGift(item.getIsGift());
              detailCmd.setWarehouseNo(warehouseNo);
              return detailCmd;
            })
        .collect(Collectors.toList());
  }
}
