package com.daddylab.supplier.item.controller.test;

import static com.daddylab.supplier.item.common.GlobalConstant.SEVEN_UP;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.application.common.event.StockInOrOutEvent;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.CombinationItemSaveCmd;
import com.daddylab.supplier.item.application.message.wechat.MsgEvent;
import com.daddylab.supplier.item.application.message.wechat.MsgEventListener;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.message.wechat.v2.statics.StaticsMsgBizService;
import com.daddylab.supplier.item.application.order.settlement.OrderSettlementBizService;
import com.daddylab.supplier.item.application.order.settlement.dto.SysBillPageQuery;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonUtil;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PurchaseBillService;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.enums.ProcessTypeEnum;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.daddylabWorkbench.DaddylabWorkbenchGateway;
import com.daddylab.supplier.item.domain.daddylabWorkbench.WorkbenchCallbackEvent;
import com.daddylab.supplier.item.domain.dataFetch.DataFetchManager;
import com.daddylab.supplier.item.domain.dataFetch.FetchDataType;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OrderLogisticsTrace;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PurchaseOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockInOrder;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.GoodsType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBizLevelDivisionService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IOrderLogisticsTraceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockInOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.DaddylabWorkbenchFeignClient;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplateImpl;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR> up
 * @date 2022年09月23日 4:55 PM
 */
@Slf4j
@RestController
@RequestMapping("/dataScript")
@Api(hidden = true)
public class DataScriptController {

    @Resource
    DateScriptService dateScriptService;

    @Resource
    DataFetchManager dataFetchManager;

    @Resource
    PurchaseBillService purchaseBillService;

    @Resource
    MsgEventListener msgEventListener;

    @Resource
    MsgSender msgSender;

    @Resource
    OrderSettlementBizService orderSettlementBizService;

    @Resource
    IStockInOrderService iStockInOrderService;

    @Resource
    IOrderLogisticsTraceService iOrderLogisticsTraceService;

    @PostMapping(value = "/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入Excel")
    @Auth(noAuth = true)
    public Response itemImportExcel(@RequestParam("file") MultipartFile file, GoodsType goodsType) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
//                dateScriptService.updateSkuCostPrice(file.getInputStream());
//                dateScriptService.clearCombinationCostPrice(file.getInputStream());
//                dateScriptService.updateUnit(file.getInputStream());
//                dateScriptService.updatePurchaseTaxRate(file.getInputStream());
//                dateScriptService.syncSkuWarehouseNo(file.getInputStream());
//                dateScriptService.handlerGoodsType(file.getInputStream(), goodsType);
//                dateScriptService.exportAfterSales(file.getInputStream());
//                dateScriptService.saveStockOutOrder(file.getInputStream());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importExcel0", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入SaleOut")
    @Auth(noAuth = true)
    public Response importExcel0(@RequestParam("file") MultipartFile file, GoodsType goodsType) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                dateScriptService.saveSaleOutStock(file.getInputStream());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importExcel1", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入SaleReturn")
    @Auth(noAuth = true)
    public Response importExcel1(@RequestParam("file") MultipartFile file, GoodsType goodsType) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                dateScriptService.saveSaleReturnStock(file.getInputStream());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importExcel2", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入StockOut")
    @Auth(noAuth = true)
    public Response importExcel2(@RequestParam("file") MultipartFile file, GoodsType goodsType) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                dateScriptService.saveStockOutOrder(file.getInputStream());
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        return Response.buildSuccess();
    }

    @PostMapping(value = "/importExcel3", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入StockIn")
    @Auth(noAuth = true)
    public Response importExcel3(@RequestParam("file") MultipartFile file, GoodsType goodsType) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            try {
                dateScriptService.saveStockInOrder(file.getInputStream());
            } catch (Exception e) {
                log.error("saveStockInOrder import excel fail", e);
                throw new RuntimeException(e);
            }
        });
        return Response.buildSuccess();
    }

    @GetMapping(value = "/common")
    @Auth(noAuth = true)
    public Response common() {

        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            dateScriptService.fillContractTime();
//            String runningMonth = LocalDateTime.now().plusMonths(-1).format(DateTimeFormatter.ofPattern("yyyyMM"));
//            SpringUtil.getBean(SpecialTreatment.class).doPriceProcess(TimeBO.of(runningMonth));
        });

        return Response.buildSuccess();
    }

    @GetMapping(value = "/kdyCallback")
    @Auth(noAuth = true)
    public Response kdyCallback() {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            dateScriptService.kdyCallback();
        });

//        SpringUtil.getBean(AfterSaleLogisticsBizServiceImpl.class).scanOrderLogisticsTraceTest();

//        SpringUtil.getBean(AfterSaleLogisticsBizService.class).scanOrderLogisticsTrace(LocalDateTime.now());

        return Response.buildSuccess();
    }


    @PostMapping(value = "/fetch")
    @Auth(noAuth = true)
    public Response fetch(Integer dataType, String startTime, String endTime) {
        final FetchDataType fetchDataType = IEnum.getEnumByValue(FetchDataType.class, dataType);
        dataFetchManager.reFetch(fetchDataType, DateUtil.parse(startTime), DateUtil.parse(endTime));
        return Response.buildSuccess();
    }

    @PostMapping(value = "/msgSender")
    @Auth(noAuth = true)
    public Response msgSender(String msgJson) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            msgEventListener.listener(JsonUtil.parse(msgJson, MsgEvent.class));
        });
        return Response.buildSuccess();
    }

    @GetMapping(value = "/retryMsgSender")
    @Auth(noAuth = true)
    public Response retryMsgSender(Long start, Long end) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            msgSender.retryFailedList(start, end);
        });
        return Response.buildSuccess();
    }

    @GetMapping(value = "/clearSkuUnit")
    @Auth(noAuth = true)
    public Response clearSkuUnit(Long itemSkuId) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            dateScriptService.clearSkuUnit(itemSkuId);
        });
        return Response.buildSuccess();
    }

    /**
     * 导出阶梯价格。
     *
     * @param isSingle true:spu。false:sku
     * @return
     */
    @GetMapping(value = "/price")
    @Auth(noAuth = true)
    public SingleResponse<String> exportCombinationSkuPrice(Boolean isSingle) {
        String s = dateScriptService.exportCombinationSkuPrice(isSingle);
        return SingleResponse.of(s);
    }

    @GetMapping(value = "/syncItem")
    @Auth(noAuth = true)
    public SingleResponse<String> syncItem(String skuCodes) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            dateScriptService.syncItem(skuCodes);
        });
        return SingleResponse.of("ok");
    }

    @GetMapping(value = "/updateSkuUnit")
    @Auth(noAuth = true)
    public SingleResponse<Boolean> updateSkuUnit(String skuCode, String unit) {
        return SingleResponse.of(dateScriptService.updateSkuUnit(skuCode, unit));
    }

    @Resource
    PurchasePayableBizService purchasePayableBizService;

    @GetMapping(value = "/hedgeHandler")
    @Auth(noAuth = true)
    public SingleResponse<String> hedgeHandler(Long id) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> {
            purchasePayableBizService.hedgeHandler(id);
        });
        return SingleResponse.of("ok");
    }


    @Autowired
    IPurchaseOrderService iPurchaseOrderService;

    @GetMapping(value = "/updatePurchaseOrder")
    @Auth(noAuth = true)
    public SingleResponse<String> updatePurchaseOrder(Long id, Integer state) {
        iPurchaseOrderService.lambdaUpdate().set(PurchaseOrder::getState, state)
                .eq(PurchaseOrder::getId, id).update();
        return SingleResponse.of("ok");
    }


    /**
     * 临时脚本。
     */
    @GetMapping(value = "skuPriceAxis")
    @Auth(noAuth = true)
    public void skuPriceAxis() {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> dateScriptService.skuPriceAxis());
    }


    /**
     * 自动批量结算
     * 只处理 系统创建的待结算状态的数据
     *
     * @param pageQuery 10位时间戳，结算月1号0点。
     */
    @GetMapping(value = "batchSettlement")
    @Auth(noAuth = true)
    public void batchSettlement(@RequestBody SysBillPageQuery pageQuery) {
        ThreadUtil.execute(PoolEnum.COMMON_POOL, () -> orderSettlementBizService.sysBillBatchSettlement(pageQuery));
    }

//    /**
//     * 【采购结算】批量导出
//     * @param orderPersonnel
//     * @param timeStamp
//     */
//    @GetMapping(value = "exportSettlementPlus")
//    @Auth(noAuth = true)
//    public void exportSettlementPlus(Long orderPersonnel, Long timeStamp) {
//        cn.hutool.core.thread.ThreadUtil.execute(() -> {
//            orderSettlementBizService.exportSettlementPlus(orderPersonnel, timeStamp);
//        });
//    }

    /**
     * 修正采购结算 发货数量 数据
     */
    @GetMapping(value = "fixDeliverNum")
    @Auth(noAuth = true)
    public void fixDeliverNum() {
        cn.hutool.core.thread.ThreadUtil.execute(() -> {
            dateScriptService.fixSettlementDetailSkuDeliverQuantity();
        });
    }

    /**
     * 临时恢复一批数据
     * 已结算状态，人工建立 的采购结算单关联的原始单据。
     */
    @GetMapping(value = "recoverSysOrderSettlementTmp")
    @Auth(noAuth = true)
    public void recoverSysOrderSettlementTmp() {
        cn.hutool.core.thread.ThreadUtil.execute(() -> {
            dateScriptService.recoverSysOrderSettlementTmp();
        });
    }

    @GetMapping(value = "startStockInOrder")
    @Auth(noAuth = true)
    public void startStockInOrder(Long stockInOrderId) {
        StockInOrder stockInOrder = iStockInOrderService.getById(stockInOrderId);
        final StockInOrOutEvent stockInOrOutEvent = StockInOrOutEvent
                .ofNotice(ProcessTypeEnum.IN_STOCK_PAYABLE, stockInOrder.getId(), stockInOrder.getBuyerUserId(), stockInOrder.getCreatedUid());
        EventBusUtil.post(stockInOrOutEvent, true);
    }

    @GetMapping(value = "pushStockOrder")
    @Auth(noAuth = true)
    public Response pushStockOrder(String purchaseOrderNo) {
        dateScriptService.pushKingDee(purchaseOrderNo);
        return Response.buildSuccess();
    }

    @GetMapping(value = "pushStockInOrder")
    @Auth(noAuth = true)
    public Response pushStockInOrder(String stockInOrderNo) {
        dateScriptService.pushStockInOrder(stockInOrderNo);
        return Response.buildSuccess();
    }


    @GetMapping(value = "deleteStockOrder")
    @Auth(noAuth = true)
    public Response deleteStockOrder(String no) {
        dateScriptService.deleteStockOrder(no);
        return Response.buildSuccess();
    }


    @GetMapping(value = "pushStockOutOrder")
    @Auth(noAuth = true)
    public Response pushStockOutOrder(String stockOutOrderNo) {
        dateScriptService.pushStockOutOrder(stockOutOrderNo);
        return Response.buildSuccess();
    }

    @GetMapping(value = "updateSingleSkuUnit")
    @Auth(noAuth = true)
    public Response updateSingleSkuUnit(String skuCode, String unit) {
        dateScriptService.updateSkuUnit0(skuCode, unit);
        return Response.buildSuccess();
    }


    @GetMapping(value = "testCombination")
    @Auth(noAuth = true)
    public void testCombination() {
        String json = "{\n" +
                "    \"id\": null,\n" +
                "    \"name\": \"老爸定制 短袖T恤黑色猫咪款M+老爸评测 定制雨伞\",\n" +
                "    \"barCode\": \"\",\n" +
                "    \"type\": \"COMMON\",\n" +
                "    \"weight\": null,\n" +
                "    \"procurementPrice\": 67,\n" +
                "    \"procurementPriceStart\": 1697424945,\n" +
                "    \"salesPrice\": 79,\n" +
                "    \"salesPriceStart\": 1697424945,\n" +
                "    \"composeSkuCmdList\": [\n" +
                "        {\n" +
                "            \"skuId\": 32634,\n" +
                "            \"skuCode\": \"100164710\",\n" +
                "            \"count\": 1,\n" +
                "            \"barCode\": \"6974721070342\",\n" +
                "            \"costProportion\": 0.522388,\n" +
                "            \"salesProportion\": 0.522388,\n" +
                "            \"itemId\": 53134,\n" +
                "            \"itemCode\": \"1001647\"\n" +
                "        },\n" +
                "        {\n" +
                "            \"skuId\": 31045,\n" +
                "            \"skuCode\": \"100145301\",\n" +
                "            \"count\": 1,\n" +
                "            \"barCode\": \"6974721070250\",\n" +
                "            \"costProportion\": 0.477612,\n" +
                "            \"salesProportion\": 0.477612,\n" +
                "            \"itemId\": 52937,\n" +
                "            \"itemCode\": \"1001453\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"uniqueCode\": null,\n" +
                "    \"calculateProcurementPrice\": 67,\n" +
                "    \"calculateSalesPrice\": 0\n" +
                "}";
        CombinationItemSaveCmd cmd = JsonUtil.parse(json, CombinationItemSaveCmd.class);

        CombinationItemBizService bean = SpringUtil.getBean(CombinationItemBizService.class);
        try {
            bean.save(cmd);
        } catch (Exception e) {
            log.error("save combination fail", e);
        }
    }


    @GetMapping(value = "modifyCategory")
    @Auth(noAuth = true)
    public Response modifyCategory(@RequestParam("itemId") Long itemId, @RequestParam("newCategoryId") Long newCategoryId) {
        CategoryGateway categoryGateway = SpringUtil.getBean(CategoryGateway.class);
        categoryGateway.modifyItemCategory(itemId, newCategoryId);
        return Response.buildSuccess();
    }

    @GetMapping(value = "refreshDeeKingSession")
    @Auth(noAuth = true)
    public Response refreshDeeKingSession() {
        String SESSION = "kingDee_session";
        RedisUtil.del(SESSION);

        final ReqTemplateImpl bean = SpringUtil.getBean(ReqTemplateImpl.class);
        final String sessionId = bean.getSessionId();
        return SingleResponse.of(sessionId);
    }


    @GetMapping(value = "staticsMsg")
    @Auth(noAuth = true)
    public Response staticsMsg() {
        StaticsMsgBizService bean = SpringUtil.getBean(StaticsMsgBizService.class);
        bean.handle();
        return Response.buildSuccess();
    }

    @GetMapping(value = "delLogistics")
    @Auth(noAuth = true)
    public Response delLogistics() {
        List<String> warehouseNos = iOrderLogisticsTraceService.lambdaQuery()
                .select(OrderLogisticsTrace::getStockoutWarehouseNo)
                .groupBy(OrderLogisticsTrace::getStockoutWarehouseNo)
                .list().stream().map(OrderLogisticsTrace::getStockoutWarehouseNo)
                .collect(Collectors.toList());

        String d1 = DateUtil.format(LocalDateTime.now(), DateUtil.DEFAULT_DATE);
        String d2 = DateUtil.format(LocalDateTime.now().plusDays(-1), DateUtil.DEFAULT_DATE);
        String d3 = DateUtil.format(LocalDateTime.now().plusDays(-2), DateUtil.DEFAULT_DATE);

        for (String warehouseNo : warehouseNos) {
            RedisUtil.del(warehouseNo + "_" + d1);
            RedisUtil.del(warehouseNo + "_" + d2);
            RedisUtil.del(warehouseNo + "_" + d3);
        }

        return Response.buildSuccess();
    }

    @GetMapping(value = "bizDivisionRefresh")
    @Auth(noAuth = true)
    public Response bizDivisionRefresh() {
        ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL).execute(() -> {
            SpringUtil.getBean(IBizLevelDivisionService.class).historyDataHandler();
            final CommonUtil commonUtil = SpringUtil.getBean(CommonUtil.class);
            commonUtil.remind("202503", ListUtil.of(SEVEN_UP), "新的业务分级数据已刷新");
        });
        return Response.buildSuccess();
    }

}
