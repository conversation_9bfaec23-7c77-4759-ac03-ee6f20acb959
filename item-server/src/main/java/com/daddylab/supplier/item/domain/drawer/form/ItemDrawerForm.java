package com.daddylab.supplier.item.domain.drawer.form;

import cn.hutool.core.collection.CollectionUtil;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerAttrImages;
import com.daddylab.supplier.item.domain.drawer.vo.ItemDrawerSkuImage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.LaunchItemType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * Class  ItemDrawerForm
 *
 * @Date 2022/5/31下午4:18
 * <AUTHOR>
 */
@Data
@ApiModel(value = "ItemDrawerForm", description = "商品抽屉表单")
public class ItemDrawerForm implements Serializable {

    private static final long serialVersionUID = 5846932721722595609L;
    @NotNull(message = "抽屉id不能为空")
    @ApiModelProperty(value = "抽屉id")
    private Long id;

    @NotNull(message = "商品ID不能为空")
    @ApiModelProperty(value = "商品ID")
    private Long itemId;

    @Length(max = 50, message = "产品标准名最多{max}个字符")
    @ApiModelProperty(value = "产品标准名")
    private String standardName;
    
    @Length(max = 50, message = "淘宝标题最多{max}个字符")
    @ApiModelProperty(value = "淘宝标题")
    private String tbTitle;

    @ApiModelProperty(value = "平台商品信息")
    private List<ItemDrawerPlatformLinkVO> platformLinks;

    @Length(max = 120, message = "首页文案最多{max}个字符")
    @ApiModelProperty(value = "首页文案")
    private String homeCopy;

    @Size(max = 20, message = "商品图片最多{max}张")
    @ApiModelProperty(value = "商品图片")
    private List<ItemDrawerImageForm> images;

    @Size(max = 20, message = "商品图片（其他尺寸）最多{max}张")
    @ApiModelProperty(value = "商品图片（其他尺寸）")
    private List<ItemDrawerImageForm> otherImages;

    @ApiModelProperty(value = "预计上架时间Id")
    private Long planId;

    @ApiModelProperty(value = "采购负责人")
    private Long buyerId;

    @ApiModelProperty(value = "产品负责人")
    private Long principalId;

    @ApiModelProperty(value = "Qc负责人")
    private String qcIds;

    @Valid
    @ApiModelProperty(value = "详情图片(视频)")
    private List<ItemDrawerImageForm> detailImages;

    @Valid
    @ApiModelProperty(value = "主图视频")
    private List<ItemDrawerImageForm> mainImageVideo;

    @ApiModelProperty(value = "是否添加规格图片（请注意，在已上传规格图片的情况下，去掉此项勾选将会移除全部规格图片）")
    public Boolean skuImagesAdded;

    @Valid
    @ApiModelProperty(value = "规格图片")
    private List<ItemDrawerSkuImage> skuImages;

    @ApiModelProperty(value = "合并上新商品ID")
    private List<Long> mergeItemIds;

    @ApiModelProperty(value = "原商品ID")
    private Long rawItemId;

    public List<ItemDrawerSkuImage> getSkuImages() {
        //如果前端勾选不添加规格图片，则将所有规格的图片置空
        if (skuImagesAdded != null && !skuImagesAdded) {
            skuImages.forEach(v -> v.setUrl(null));
        }
        return skuImages;
    }

    @ApiModelProperty(value = "是否添加属性图片（请注意，在已上传属性图片的情况下，去掉此项勾选将会移除全部图片）")
    public Boolean attrImagesAdded;

    @Valid
    @ApiModelProperty(value = "属性图片")
    private List<ItemDrawerAttrImages> attrImages;

    public List<ItemDrawerAttrImages> getAttrImages() {
        if (attrImagesAdded != null && !attrImagesAdded && CollectionUtil.isNotEmpty(attrImages)) {
            attrImages.forEach(
                    attrImage -> attrImage.getItemAttrs().forEach(attr -> attr.setUrl(null)));
        } else if (CollectionUtil.isNotEmpty(attrImages)) {
            boolean hasAttrImagesAdded = false;
            for (ItemDrawerAttrImages attrImage : attrImages) {
                if (!attrImage.getImagesAdded() || hasAttrImagesAdded) {
                    attrImage.getItemAttrs().forEach(attr -> attr.setUrl(null));
                }
                hasAttrImagesAdded = hasAttrImagesAdded || attrImage.getImagesAdded();
            }
        }
        return attrImages;
    }

    @ApiModelProperty("商品类型 INNER 内部 OUTSOURCE 外包")
    private LaunchItemType type;

    @ApiModelProperty("确认重新审核")
    private Boolean confirmReAudit;

    @ApiModelProperty("抖音链接内容")
    private String douYinLinkContent;

    @ApiModelProperty("快手链接内容")
    private String kuaiShouLinkContent;

}
