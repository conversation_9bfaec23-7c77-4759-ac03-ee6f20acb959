package com.daddylab.supplier.item.domain.stockWarn.service.impl;

import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnInfo;
import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnSummary;
import com.daddylab.supplier.item.domain.stockWarn.service.StockWarnDomainService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryWarnStock;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.DadStaff;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.WdtStockSpec;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Warehouse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryWarnStockService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IDadStaffService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWdtStockSpecService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IWarehouseService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.domain.auth.gateway.UserContextGateway;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.email.EmailService;
import com.daddylab.supplier.item.domain.stockWarn.dto.StockWarnExcelDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 库存告警领域服务实现
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Slf4j
@Service
public class StockWarnDomainServiceImpl implements StockWarnDomainService {

    @Autowired
    private IWdtStockSpecService wdtStockSpecService;

    @Autowired
    private ICategoryWarnStockService categoryWarnStockService;

    @Autowired
    private IWarehouseService warehouseService;

    @Autowired
    private ICategoryService categoryService;

    @Autowired
    private IItemService itemService;

    @Autowired
    private UserContextGateway userContextGateway;

    @Autowired
    private IDadStaffService dadStaffService;

    @Autowired
    private OssGateway ossGateway;

    @Autowired
    private EmailService emailService;

    @Override
    public List<StockWarnInfo> checkStockWarn() {
        log.info("[库存告警检查] 开始检查库存告警");

        List<StockWarnInfo> warnInfos = new ArrayList<>();

        try {
            // 1. 获取所有类目警戒库存配置
            List<CategoryWarnStock> categoryWarnStocks = categoryWarnStockService.list();
            if (CollectionUtils.isEmpty(categoryWarnStocks)) {
                log.info("[库存告警检查] 没有配置类目警戒库存");
                return warnInfos;
            }

            // 2. 获取所有旺店通库存数据
            List<WdtStockSpec> stockSpecs = wdtStockSpecService.list();
            if (CollectionUtils.isEmpty(stockSpecs)) {
                log.info("[库存告警检查] 没有旺店通库存数据");
                return warnInfos;
            }

            // 3. 按类目分组检查库存
            Map<Long, BigDecimal> categoryWarnStockMap =
                    categoryWarnStocks.stream().collect(Collectors.toMap(
                            CategoryWarnStock::getCategoryId, CategoryWarnStock::getWarnStock));

            // 4. 获取所有类目信息（全量加载）
            Map<Long, Category> categoryMap = categoryService.list().stream()
                    .collect(Collectors.toMap(Category::getId, category -> category));

            // 5. 获取所有仓库信息（全量加载）
            Map<String, Warehouse> warehouseMap = warehouseService.list().stream()
                    .collect(Collectors.toMap(Warehouse::getNo, warehouse -> warehouse));

            // 6. 获取所有商品信息（全量加载）
            Map<String, Item> goodsMap = itemService.list().stream()
                    .collect(Collectors.toMap(Item::getCode, item -> item));

            // 7. 检查每个商品的库存
            for (WdtStockSpec stockSpec : stockSpecs) {
                // 参数校验
                if (!StringUtils.hasText(stockSpec.getGoodsNo())
                        || !StringUtils.hasText(stockSpec.getWarehouseNo())) {
                    continue;
                }

                Item item = goodsMap.get(stockSpec.getGoodsNo());
                if (item == null) {
                    continue;
                }

                // 获取商品对应的类目
                Long categoryId = item.getCategoryId();
                if (categoryId == null || !categoryWarnStockMap.containsKey(categoryId)) {
                    continue;
                }

                BigDecimal warnStock = categoryWarnStockMap.get(categoryId);
                BigDecimal currentStock = stockSpec.getAvailableStock();

                // 检查库存是否低于警戒线
                if (currentStock != null && currentStock.compareTo(warnStock) < 0) {
                    StockWarnInfo warnInfo = buildStockWarnInfo(stockSpec, categoryId, categoryMap,
                            warehouseMap, warnStock, currentStock);
                    if (warnInfo != null) {
                        warnInfos.add(warnInfo);
                    }
                }
            }

            log.info("[库存告警检查] 检查完成，发现 {} 个商品库存告警", warnInfos.size());

        } catch (Exception e) {
            log.error("[库存告警检查] 检查库存告警失败", e);
            throw e;
        }

        return warnInfos;
    }

    @Override
    public List<StockWarnSummary> summarizeWarnByOrderPersonnel(List<StockWarnInfo> warnInfos) {
        log.info("[库存告警汇总] 开始按订单员汇总告警信息");

        if (CollectionUtils.isEmpty(warnInfos)) {
            log.info("[库存告警汇总] 没有告警信息需要汇总");
            return new ArrayList<>();
        }

        Map<Long, List<StockWarnInfo>> groupedByOrderPersonnel = warnInfos.stream()
                .filter(info -> !CollectionUtils.isEmpty(info.getOrderPersonnelIds()))
                .flatMap(info -> info.getOrderPersonnelIds().stream().map(personnelId -> {
                    StockWarnInfo copy = createStockWarnInfoCopy(info, personnelId);
                    return new AbstractMap.SimpleEntry<>(personnelId, copy);
                })).collect(Collectors.groupingBy(Map.Entry::getKey,
                        Collectors.mapping(Map.Entry::getValue, Collectors.toList())));

        // 获取所有员工信息（全量加载）
        Map<Long, DadStaff> staffMap = getAllStaffMap();

        List<StockWarnSummary> summaries =
                groupedByOrderPersonnel.entrySet().stream().map(entry -> {
                    StockWarnSummary summary = new StockWarnSummary();
                    summary.setOrderPersonnelId(entry.getKey());
                    summary.setWarnItems(entry.getValue());
                    summary.setWarnItemCount(entry.getValue().size());

                    // 从缓存中获取员工信息
                    DadStaff staffVO = staffMap.get(entry.getKey());
                    if (staffVO != null) {
                        summary.setOrderPersonnelName(staffVO.getNickname());
                        summary.setOrderPersonnelEmail(staffVO.getEmail());
                    } else {
                        log.warn("[库存告警汇总] 订单员ID {} 对应的员工信息不存在", entry.getKey());
                        summary.setOrderPersonnelName("未知");
                        summary.setOrderPersonnelEmail("");
                    }

                    return summary;
                }).collect(Collectors.toList());

        log.info("[库存告警汇总] 汇总完成，共 {} 个订单员需要发送告警", summaries.size());
        return summaries;
    }

    @Override
    public String generateAndUploadExcel(StockWarnSummary summary) {
        log.info("[生成Excel] 开始为订单员 {} 生成Excel文件", summary.getOrderPersonnelId());

        if (summary == null || CollectionUtils.isEmpty(summary.getWarnItems())) {
            log.warn("[生成Excel] 告警汇总信息为空，跳过Excel生成");
            return null;
        }

        ByteArrayOutputStream outputStream = null;
        ByteArrayInputStream inputStream = null;

        try {
            // 生成Excel内容
            outputStream = new ByteArrayOutputStream();
            generateExcelContent(summary, outputStream);

            // 生成安全的文件名
            String safeFileName = generateSafeFileName(summary.getOrderPersonnelName());
            String fileName = String.format("%s_%s.xlsx", safeFileName,
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));

            // 上传到OSS
            inputStream = new ByteArrayInputStream(outputStream.toByteArray());
            String ossUrl = ossGateway.put(false, "stock-warn/" + fileName, inputStream);

            log.info("[生成Excel] Excel文件生成并上传成功，URL：{}", ossUrl);
            return ossUrl;

        } catch (Exception e) {
            log.error("[生成Excel] 生成Excel文件失败", e);
            throw e;
        } finally {
            // 确保资源被正确关闭
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                log.warn("[生成Excel] 关闭流资源时发生异常", e);
            }
        }
    }

    @Override
    public void sendWarnEmail(StockWarnSummary summary, String ossUrl) {
        log.info("[发送邮件] 开始为订单员 {} 发送告警邮件", summary.getOrderPersonnelId());

        try {
            String subject = String.format("仓库商品库存告警【%s-%s】", summary.getOrderPersonnelName(),
                    LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));

            String content = String.format(
                    "仓库负责人 【%s】你好，\n\n" + "上一周期内，你所负责的部分商品库存告警，请联系供应商确认最新库存，并进行更新，谢谢（见附件）",
                    summary.getOrderPersonnelName());

            emailService.sendSimpleMail(summary.getOrderPersonnelEmail(), subject, content);
            log.info("[发送邮件] 告警邮件发送成功，收件人：{}", summary.getOrderPersonnelEmail());

        } catch (Exception e) {
            log.error("[发送邮件] 发送告警邮件失败", e);
            throw e;
        }
    }

    /**
     * 构建库存告警信息
     */
    private StockWarnInfo buildStockWarnInfo(WdtStockSpec stockSpec, Long categoryId,
            Map<Long, Category> categoryMap, Map<String, Warehouse> warehouseMap,
            BigDecimal warnStock, BigDecimal currentStock) {
        try {
            StockWarnInfo warnInfo = new StockWarnInfo();
            warnInfo.setItemCode(stockSpec.getGoodsNo());
            warnInfo.setItemName(stockSpec.getGoodsName());
            warnInfo.setCategoryId(categoryId);

            // 安全获取类目名称
            Category category = categoryMap.get(categoryId);
            if (category != null) {
                warnInfo.setCategoryName(category.getName());
            } else {
                warnInfo.setCategoryName("未知类目");
            }

            warnInfo.setCurrentStock(currentStock);
            warnInfo.setWarnStock(warnStock);
            warnInfo.setWarehouseId(stockSpec.getWarehouseId());

            // 安全获取仓库名称
            Warehouse warehouse = warehouseMap.get(stockSpec.getWarehouseNo());
            if (warehouse != null) {
                warnInfo.setWarehouseName(warehouse.getName());

                // 获取仓库的订单员信息
                if (StringUtils.hasText(warehouse.getOrderPersonnel())) {
                    List<Long> orderPersonnelIds = warehouse.selectOrderPersonnelIds();
                    warnInfo.setOrderPersonnelIds(orderPersonnelIds);
                }
            } else {
                warnInfo.setWarehouseName("未知仓库");
            }

            return warnInfo;
        } catch (Exception e) {
            log.error("[构建告警信息] 构建库存告警信息失败", e);
            return null;
        }
    }

    /**
     * 创建库存告警信息副本
     */
    private StockWarnInfo createStockWarnInfoCopy(StockWarnInfo info, Long personnelId) {
        StockWarnInfo copy = new StockWarnInfo();
        copy.setItemCode(info.getItemCode());
        copy.setItemName(info.getItemName());
        copy.setCategoryId(info.getCategoryId());
        copy.setCategoryName(info.getCategoryName());
        copy.setCurrentStock(info.getCurrentStock());
        copy.setWarnStock(info.getWarnStock());
        copy.setWarehouseId(info.getWarehouseId());
        copy.setWarehouseName(info.getWarehouseName());
        copy.setOrderPersonnelIds(Arrays.asList(personnelId));
        return copy;
    }

    /**
     * 获取所有员工信息（全量加载）
     */
    private Map<Long, DadStaff> getAllStaffMap() {
        return dadStaffService.list().stream()
                .collect(Collectors.toMap(DadStaff::getUid, Function.identity()));
    }


    /**
     * 生成安全的文件名
     */
    private String generateSafeFileName(String name) {
        if (!StringUtils.hasText(name)) {
            return "unknown";
        }

        // 移除或替换文件名中的非法字符
        return name.replaceAll("[\\\\/:*?\"<>|]", "_").replaceAll("\\s+", "_").substring(0,
                Math.min(name.length(), 50)); // 限制长度
    }

    /**
     * 根据商品编号获取类目ID（原方法保留，用于兼容）
     */
    private Long getCategoryIdByGoods(String goodsNo) {
        try {
            // 根据商品编号查询商品信息
            Item item = itemService.getByCode(goodsNo);
            if (item != null) {
                return item.getCategoryId();
            }
            log.warn("[获取商品类目] 商品编号 {} 对应的商品不存在", goodsNo);
            return null;
        } catch (Exception e) {
            log.error("[获取商品类目] 根据商品编号 {} 获取类目ID失败", goodsNo, e);
            return null;
        }
    }

    /**
     * 生成Excel内容 使用 EasyExcel 生成 Excel 文件
     */
    private void generateExcelContent(StockWarnSummary summary,
            ByteArrayOutputStream outputStream) {
        log.info("[生成Excel] 开始生成Excel内容，包含 {} 个告警商品", summary.getWarnItemCount());

        ExcelWriter excelWriter = null;
        try {
            // 将告警商品信息转换为Excel DTO
            List<StockWarnExcelDto> excelData = summary.getWarnItems().stream()
                    .map(this::convertToExcelDto).filter(Objects::nonNull) // 过滤空值
                    .collect(Collectors.toList());

            if (excelData.isEmpty()) {
                log.warn("[生成Excel] 没有有效的Excel数据");
                return;
            }

            // 使用EasyExcel生成Excel文件
            excelWriter = EasyExcel.write(outputStream, StockWarnExcelDto.class).build();
            WriteSheet writeSheet =
                    EasyExcel.writerSheet("库存告警明细").head(StockWarnExcelDto.class).build();

            excelWriter.write(excelData, writeSheet);
            excelWriter.finish();

            log.info("[生成Excel] Excel文件生成完成，包含 {} 条记录", excelData.size());

        } catch (Exception e) {
            log.error("[生成Excel] 生成Excel文件失败", e);
            throw new RuntimeException("生成Excel文件失败", e);
        } finally {
            // 确保ExcelWriter被正确关闭
            if (excelWriter != null) {
                try {
                    excelWriter.finish();
                } catch (Exception e) {
                    log.warn("[生成Excel] 关闭ExcelWriter时发生异常", e);
                }
            }
        }
    }

    /**
     * 将告警商品信息转换为Excel DTO
     */
    private StockWarnExcelDto convertToExcelDto(StockWarnInfo warnInfo) {
        if (warnInfo == null) {
            return null;
        }

        try {
            StockWarnExcelDto dto = new StockWarnExcelDto();
            dto.setItemCode(
                    StringUtils.hasText(warnInfo.getItemCode()) ? warnInfo.getItemCode() : "未知");
            dto.setItemName(
                    StringUtils.hasText(warnInfo.getItemName()) ? warnInfo.getItemName() : "未知商品");
            dto.setCategoryName(
                    StringUtils.hasText(warnInfo.getCategoryName()) ? warnInfo.getCategoryName()
                            : "未知类目");
            dto.setCurrentStock(warnInfo.getCurrentStock());
            dto.setWarnStock(warnInfo.getWarnStock());
            dto.setWarehouseName(
                    StringUtils.hasText(warnInfo.getWarehouseName()) ? warnInfo.getWarehouseName()
                            : "未知仓库");

            // 计算库存差异
            if (warnInfo.getCurrentStock() != null && warnInfo.getWarnStock() != null) {
                dto.setStockDiff(warnInfo.getCurrentStock().subtract(warnInfo.getWarnStock()));
            }

            // 设置告警级别
            if (warnInfo.getCurrentStock() != null && warnInfo.getWarnStock() != null) {
                BigDecimal diff = warnInfo.getCurrentStock().subtract(warnInfo.getWarnStock());
                if (diff.compareTo(BigDecimal.ZERO) < 0) {
                    dto.setWarnLevel("紧急");
                } else if (warnInfo.getWarnStock().compareTo(BigDecimal.ZERO) > 0 && diff
                        .compareTo(warnInfo.getWarnStock().multiply(new BigDecimal("0.1"))) < 0) {
                    dto.setWarnLevel("警告");
                } else {
                    dto.setWarnLevel("提醒");
                }
            } else {
                dto.setWarnLevel("未知");
            }

            return dto;
        } catch (Exception e) {
            log.error("[转换Excel DTO] 转换告警信息失败", e);
            return null;
        }
    }
}
