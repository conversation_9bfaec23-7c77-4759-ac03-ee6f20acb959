package com.daddylab.supplier.item.infrastructure.gatewayimpl.category;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.exception.BizException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ChangePropertyObj;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.google.common.collect.ImmutableList;
import joptsimple.internal.Strings;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/9/29 5:21 下午
 * @description
 */
@Service
public class CategoryGatewayImpl implements CategoryGateway {

    @Autowired
    ICategoryService iCategoryService;

    @Autowired
    ICategoryAttrService iCategoryAttrService;

//    @Autowired
//    KingDeeGateway kingDeeGateway;

    @Autowired
    IItemService iItemService;

    @Autowired
    IItemSkuService itemSkuService;

    @Autowired
    OperateLogDomainService operateLogDomainService;

    @Autowired
    IItemAttrService iItemAttrService;

    @Autowired
    IItemSkuAttrRefService iItemSkuAttrRefService;

    @Override
    public Category getById(Long id) {
        return iCategoryService.getById(id);
    }

    @Override
    public Category getRootCategory(Long id) {
        final Category category = iCategoryService.getById(id);
        if (category.getLevel() == 1) {
            return category;
        }
        if (category.getRootId() == 0) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "非根品类，但是rootId为空,queryId:" + id);
        }
        final Category root = iCategoryService.getById(category.getRootId());
        if (Objects.isNull(root)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "根品类查询失败,queryId:" + id);
        }
        if (StringUtils.isBlank(root.getShortName())) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "根品类简称不得为空,queryId:" + id);
        }
        return root;
    }

    @Override
    public Boolean isNameRepeat(String name, Integer level) {
        QueryWrapper<Category> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Category::getName, name)
                .eq(Objects.nonNull(level), Category::getLevel, level);
        final int integer = iCategoryService.count(queryWrapper);
        return integer > 0;
    }

    @Override
    public Long saveDo(Category category) {
        iCategoryService.save(category);

        QueryWrapper<Category> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Category::getName, category.getName()).eq(Category::getLevel, category.getLevel());
        return iCategoryService.getOne(queryWrapper).getId();
    }


    @Override
    public Set<CategoryAttr> getAttrListByCategoryId(Long categoryId) {
        List<CategoryAttr> categoryAttrs = getListByCategoryId(categoryId);
        Set<CategoryAttr> categoryAttrSet = new HashSet<>(categoryAttrs);

        Category category = iCategoryService.getById(categoryId);
        Long parentId = category.getParentId();
        while (parentId != 0) {
            categoryAttrSet.addAll(getListByCategoryId(parentId));
            category = iCategoryService.getById(parentId);
            if (Objects.isNull(category)) {
                parentId = 0L;
            } else {
                parentId = category.getParentId();
            }
        }
        return categoryAttrSet;
    }

    @Override
    public List<Category> getUpperCategoryList(Long categoryId) {
        List<Category> list = new LinkedList<>();

        Category category = iCategoryService.getById(categoryId);
        if(Objects.isNull(category)){
            return list;
        }
        list.add(category);
        Long parentId = category.getParentId();
        while (parentId != 0) {
            category = iCategoryService.getById(parentId);
            list.add(category);
            if (Objects.isNull(category)) {
                parentId = 0L;
            } else {
                parentId = category.getParentId();
            }
        }
        return list.stream().sorted(Comparator.comparing(Category::getLevel)).collect(Collectors.toList());
    }
    
    private List<CategoryAttr> getListByCategoryId(Long categoryId) {
        QueryWrapper<CategoryAttr> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(CategoryAttr::getCategoryId, categoryId);
        return iCategoryAttrService.list(queryWrapper);
    }

    @Override
    public void removeById(Long categoryId) {
        final Category category = iCategoryService.getById(categoryId);
        if (Objects.isNull(category)) return;

        iCategoryService.removeByIdWithTime(categoryId);
    }

    @Override
    public boolean canDelete(Long categoryId) {
        QueryWrapper<Item> queryWrapper = new QueryWrapper<Item>();
        queryWrapper.lambda().eq(Item::getCategoryId, categoryId);
        return iItemService.count(queryWrapper) == 0;
    }

    @Override
    public void removeAttrById(Long attrId) {
        iCategoryAttrService.removeById(attrId);
    }

    @Override
    public boolean canDeleteAttr(Long attrId) {
        QueryWrapper<ItemAttr> queryWrapper = new QueryWrapper<ItemAttr>();
        queryWrapper.lambda().eq(ItemAttr::getAttrId, attrId);
        return iItemAttrService.count(queryWrapper) == 0;
    }

    @Override
    public Boolean isShotNameRepeat(String abb) {
        QueryWrapper<Category> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Category::getShortName, abb);
        return iCategoryService.count(queryWrapper) > 0;
    }

    @Override
    public Boolean isLastLevel(Long id) {
        QueryWrapper<Category> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Category::getParentId, id);
        return iCategoryService.count(queryWrapper) == 0;
    }

    @Override
    public Boolean isHaveAttr(String path) {
        QueryWrapper<Category> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(Category::getPath, path.replaceAll("/", ""));
        final List<Category> list = iCategoryService.list(queryWrapper);
        final List<Long> idList = list.stream().map(Category::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(idList)) {
            return false;
        }

        QueryWrapper<CategoryAttr> wrapper = new QueryWrapper<>();
        wrapper.lambda().in(CategoryAttr::getCategoryId, idList);
        return iCategoryAttrService.count(wrapper) > 0;
    }

    @Override
    public void setKingDeeId(Long id, String kingDeeId) {
        LambdaUpdateWrapper<Category> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Category::getKingDeeId, kingDeeId).eq(Category::getId, id);
        iCategoryService.update(updateWrapper);
    }

    @Override
    public void removeKingDeeId(Long id) {
        LambdaUpdateWrapper<Category> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(Category::getKingDeeId, Strings.EMPTY).eq(Category::getId, id);
        iCategoryService.update(updateWrapper);
    }

    @Override
    public Map<Long, String> names(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        QueryWrapper<Category> query = new QueryWrapper<>();
        query.lambda().in(Category::getId, ids).select(Category::getId, Category::getName);
        return iCategoryService.list(query).stream()
                .collect(Collectors.toMap(Category::getId, Category::getName));
    }

    @Override
    public Map<Long, String> paths(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyMap();
        }
        QueryWrapper<Category> query = new QueryWrapper<>();
        query.lambda().in(Category::getId, ids).select(Category::getId, Category::getPath);
        return iCategoryService.list(query).stream()
                .collect(Collectors.toMap(Category::getId, Category::getPath));
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean modifyItemCategory(Long itemId, Long newCategoryId) {
        final Item item = iItemService.getById(itemId);
        if (item == null) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品不存在");
        }
        final Long categoryId = item.getCategoryId();
        if (NumberUtil.isZeroOrNull(categoryId)) {
            throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "商品数据异常，当前无类目");
        }
        if (Objects.equals(categoryId, newCategoryId)) {
            return true;
        }

        final Category category = iCategoryService.getById(categoryId);
        final Category newCategory = iCategoryService.getById(newCategoryId);
        operateLogDomainService.addOperatorLog(UserContext.getUserId(), OperateLogTarget.ITEM, itemId,
                String.format("将商品类目从 %s 修改为 %s", category.getPath(), newCategory.getPath()),
                ImmutableList.of(
                        new ChangePropertyObj("categoryId", categoryId, newCategoryId)
                ));
        final Item itemUpdateObj = new Item();
        itemUpdateObj.setId(itemId);
        itemUpdateObj.setCategoryId(newCategoryId);
        iItemService.updateById(itemUpdateObj);

        final ItemSku itemSkuUpdateObj = new ItemSku();
        itemSkuUpdateObj.setCategoryId(newCategoryId);
        final LambdaUpdateWrapper<ItemSku> itemSkuUpdateWrapper = Wrappers.<ItemSku>lambdaUpdate()
                .eq(ItemSku::getItemId, itemId);
        itemSkuService.update(itemSkuUpdateObj, itemSkuUpdateWrapper);

        modifyItemAndSkuAttr(itemId, categoryId, newCategoryId);
        return true;
    }

    @Override
    public void modifyItemAndSkuAttr(Long itemId, Long categoryId, Long newCategoryId)
            throws BizException {
        final List<CategoryAttr> categoryAttrs = iCategoryAttrService.lambdaQuery()
                .in(CategoryAttr::getCategoryId, ImmutableList.of(categoryId, newCategoryId))
                .list();
        final List<CategoryAttr> oldCategoryAttrs = categoryAttrs.stream()
                .filter(categoryAttr -> categoryAttr.getCategoryId().equals(categoryId)).collect(
                        Collectors.toList());
        final List<CategoryAttr> newCategoryAttrs = categoryAttrs.stream()
                .filter(categoryAttr -> categoryAttr.getCategoryId().equals(newCategoryId)).collect(
                        Collectors.toList());
        if (oldCategoryAttrs.size() != newCategoryAttrs.size()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "新老类目的属性数量不同，不能修改");
        }
        final HashMap<Long, Long> attrMap = new HashMap<>();
        oldCategoryAttrs.forEach(oldCategoryAttr -> {
            newCategoryAttrs.stream().filter(
                    newCategoryAttr -> oldCategoryAttr.getName().equals(newCategoryAttr.getName())
            ).findFirst().ifPresent(newCategoryAttr -> attrMap.put(oldCategoryAttr.getId(),
                    newCategoryAttr.getId()));
        });
        if (attrMap.size() != oldCategoryAttrs.size()) {
            throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "新老类目的属性名称不匹配，不能修改");
        }
        final LambdaUpdateWrapper<ItemAttr> itemAttrQuery = Wrappers.lambdaUpdate();
        itemAttrQuery.eq(ItemAttr::getItemId, itemId);
        final List<ItemAttr> itemAttrs = iItemAttrService.list(itemAttrQuery);
        //如果当前商品属性包含新类目属性ID，说明无需处理
        if (itemAttrs.stream().map(ItemAttr::getAttrId).anyMatch(attrMap::containsValue)) {
            return;
        }
        iItemAttrService.remove(itemAttrQuery);
        final HashMap<Long, Long> itemAttrMap = new HashMap<>();
        final Long createdAt = DateUtil.currentTime();
        for (ItemAttr itemAttr : itemAttrs) {
            final Long id = itemAttr.getId();
            itemAttr.setId(null);
            itemAttr.setAttrId(attrMap.get(itemAttr.getAttrId()));
            itemAttr.setCategoryId(newCategoryId);
            itemAttr.setCreatedAt(createdAt);
            itemAttr.setCreatedUid(0L);
            itemAttr.setUpdatedAt(0L);
            itemAttr.setUpdatedUid(0L);
            iItemAttrService.save(itemAttr);
            itemAttrMap.put(id, itemAttr.getId());
        }

        final LambdaQueryWrapper<ItemSkuAttrRef> itemSkuAttrRefQuery = Wrappers.lambdaQuery();
        itemSkuAttrRefQuery.eq(ItemSkuAttrRef::getItemId, itemId);
        final List<ItemSkuAttrRef> itemSkuAttrRefs = iItemSkuAttrRefService.list(itemSkuAttrRefQuery);
        iItemSkuAttrRefService.remove(itemSkuAttrRefQuery);

        List<ItemSkuAttrRef> collect = itemSkuAttrRefs.stream().map(itemSkuAttrRef -> {
            itemSkuAttrRef.setId(null);
            itemSkuAttrRef.setItemAttrId(itemAttrMap.get(itemSkuAttrRef.getItemAttrId()));
            itemSkuAttrRef.setCreatedAt(createdAt);
            itemSkuAttrRef.setCreatedUid(0L);
            itemSkuAttrRef.setUpdatedAt(0L);
            itemSkuAttrRef.setUpdatedUid(0L);
            return itemSkuAttrRef;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            iItemSkuAttrRefService.saveBatch(collect);
        }
//        for (ItemSkuAttrRef itemSkuAttrRef : itemSkuAttrRefs) {
//
//            iItemSkuAttrRefService.save(itemSkuAttrRef);
//        }
    }

    @Override
    public Category getByPath(String path) {
        return iCategoryService.lambdaQuery().eq(Category::getPath, path).last("limit 1").one();
    }

    @Override
    public Category getByNameAndLevel(String name, Integer level) {
        LambdaQueryChainWrapper<Category> queryWrapper = iCategoryService.lambdaQuery()
                .eq(Category::getName, name);
        if (level != null) {
            queryWrapper.eq(Category::getLevel, level);
        }
        return queryWrapper.last("limit 1").one();
    }
}
