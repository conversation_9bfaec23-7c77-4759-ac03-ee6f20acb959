package com.daddylab.supplier.item.controller.categoryWarnStock;

import com.alibaba.cola.dto.Response;
import com.daddylab.supplier.item.application.categoryWarnStock.CategoryWarnStockBizService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

@Slf4j
@Api(value = "类目警戒库存配置", tags = "类目警戒库存配置")
@RestController
@RequestMapping("/categoryWarnStock")
public class CategoryWarnStockController {

    @Resource
    private CategoryWarnStockBizService categoryWarnStockBizService;

    @PostMapping(value = "/importExcel", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @ApiOperation("导入类目警戒库存Excel")
    public Response importExcel(@ApiParam(name = "file", value = "文件", required = true)
                                @RequestParam("file") MultipartFile file) {
        try {
            categoryWarnStockBizService.importExcel(file.getInputStream());
            return Response.buildSuccess();
        } catch (IOException e) {
            log.error("导入类目警戒库存Excel失败", e);
            throw new RuntimeException("导入Excel失败，读取文件异常");
        }
    }
}