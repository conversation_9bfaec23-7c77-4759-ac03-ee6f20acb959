package com.daddylab.supplier.item.controller.test;

import static com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum.SAL_OUTSTOCK;
import static com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum.SAL_RETURNSTOCK;
import static java.util.stream.Collectors.toList;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.exception.ExceptionFactory;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.ark.sailor.item.common.base.vo.Result;
import com.daddylab.ark.sailor.item.domain.query.item.ItemPageQuery;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemListVO;
import com.daddylab.ark.sailor.item.enums.ItemAuditStatusEnum;
import com.daddylab.ark.sailor.item.enums.ItemShelfStatusEnum;
import com.daddylab.supplier.item.application.afterSaleLogistics.AfterSaleLogisticsBizService;
import com.daddylab.supplier.item.application.afterSaleLogistics.AfterSaleLogisticsListener;
import com.daddylab.supplier.item.application.afterSaleLogistics.dto.AfterSaleLogisticsPageQuery;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterBizService;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterPageQuery;
import com.daddylab.supplier.item.application.afterSalesRegister.AfterSalesRegisterPageVO;
import com.daddylab.supplier.item.application.item.ItemSyncBanniuBizService;
import com.daddylab.supplier.item.application.item.ItemSyncWdtBizService;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ProportionCmd;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ProportionVO;
import com.daddylab.supplier.item.application.message.wechat.MsgSender;
import com.daddylab.supplier.item.application.order.settlement.OrderSettlementBizService;
import com.daddylab.supplier.item.application.order.settlement.dto.SettlementOrderPageQuery;
import com.daddylab.supplier.item.application.order.settlement.dto.SettlementOrderPageVo;
import com.daddylab.supplier.item.application.order.settlement.dto.SkuDeliverNumDo;
import com.daddylab.supplier.item.application.order.settlement.sys.DetailStaticDo;
import com.daddylab.supplier.item.application.order.settlement.sys.OrderSettlementSysService;
import com.daddylab.supplier.item.application.payment.PaymentOrderBizService;
import com.daddylab.supplier.item.application.payment.dto.PaymentPageQuery;
import com.daddylab.supplier.item.application.payment.dto.PaymentPageVo;
import com.daddylab.supplier.item.application.provider.ProviderBizServiceImpl;
import com.daddylab.supplier.item.application.purchase.order.factory.ErpPurchaseOrderHandler;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.TimeBO;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.WrapperType;
import com.daddylab.supplier.item.application.saleItem.query.NewGoodsQueryPage;
import com.daddylab.supplier.item.application.saleItem.service.impl.NewGoodsBizServiceImpl;
import com.daddylab.supplier.item.application.saleItem.vo.NewGoodsGroupVO;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.application.stockOutOrder.StockOutOrderBizService;
import com.daddylab.supplier.item.common.GlobalConstant;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.item.dto.ExportItemProviderRow;
import com.daddylab.supplier.item.controller.purchase.PurchaseOrderController;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageQuery;
import com.daddylab.supplier.item.controller.purchase.dto.order.PurchaseOrderPageVO;
import com.daddylab.supplier.item.controller.test.dto.*;
import com.daddylab.supplier.item.domain.banniu.BanniuCommonService;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.contract.ContractGateway;
import com.daddylab.supplier.item.domain.fileStore.gateway.FileGateway;
import com.daddylab.supplier.item.domain.fileStore.vo.FileStub;
import com.daddylab.supplier.item.domain.fileStore.vo.UploadFileAction;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.AbnormalityListObj;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.GoodsType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.OrderSettlementStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PayApplyStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PaymentOrderStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.ArkSailorItemFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.dto.KingDeeResp;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqJsonUtil;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.oss.OssConfig;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.oss.OssGatewayImpl;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.daddylab.supplier.item.infrastructure.utils.RedisUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.daddylab.supplier.item.types.contract.ContractDropdownItem;
import com.daddylab.supplier.item.types.itemDrawer.enums.ItemDrawerAuditTaskState;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyCallbackEvent;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyTrackItem;
import com.daddylab.supplier.item.types.kuaidaoyun.KdyTrackList;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR> up
 * @date 2022年09月23日 4:57 PM
 */
@Slf4j
@Service
public class DateScriptService {
  @Resource CombinationItemMapper combinationItemMapper;
  @Resource ReqTemplate reqTemplate;
  @Resource IItemSkuService iItemSkuService;
  @Resource IBaseUnitService baseUnitService;
  @Resource IPurchaseOrderService purchaseOrderService;
  @Resource StockOutOrderBizService stockOutOrderBizService;
  @Resource IProviderService iProviderService;
  @Autowired ICategoryService iCategoryService;
  @Resource IItemSkuPriceService iItemSkuPriceService;
  @Resource IItemProcurementService iItemProcurementService;
  @Resource IWdtOrderDetailWrapperService iWdtOrderDetailWrapperService;
  @Resource ItemSkuGateway itemSkuGateway;
  @Resource IBankAccountService iBankAccountService;
  @Resource CombinationItemBizService combinationItemBizService;
  @Resource IComposeSkuService iComposeSkuService;
  @Resource NewGoodsBizServiceImpl newGoodsBizService;
  @Resource INewGoodsService iNewGoodsService;
  @Resource IItemLaunchPlanItemRefService iItemLaunchPlanItemRefService;
  @Resource IStockInOrderService stockInOrderService;
  @Resource IStockOutOrderService stockOutOrderService;
  @Resource IWarehouseService warehouseService;
  @Resource PurchaseOrderController purchaseOrderController;
  @Autowired ILogService logService;
  @Autowired ErpPurchaseOrderHandler purchaseOrderHandler;
  @Resource IPurchaseSingleSkuCombinationPriceService singleSkuCombinationPriceService;
  @Resource IPurchaseRandomSkuCombinationPriceService randomSkuCombinationPriceService;
  @Resource FileGateway fileGateway;

  @Resource
  @Qualifier("UserGatewayCacheImpl")
  UserGateway userGateway;

  @Resource OssGateway ossGateway;
  @Resource AfterSalesRegisterBizService afterSalesRegisterBizService;

  @Resource ItemSyncWdtBizService itemSyncWdtBizService;
  @Resource ItemSyncBanniuBizService itemSyncBanniuBizService;

  @Resource ITmpAfterSalesSupplementService iTmpAfterSalesSupplementService;

  @Resource AfterSaleLogisticsBizService afterSaleLogisticsBizService;
  @Resource IOrderLogisticsAbnormalityLogService abnormalityLogService;
  @Resource IWdtOrderDetailService wdtOrderDetailService;
  @Resource WdtOrderMapper wdtOrderMapper;

  @Resource IStockInOrderDetailService iStockInOrderDetailService;

  @Resource IStockOutOrderDetailService iStockOutOrderDetailService;

  @Resource ContractGateway contractGateway;

  public void syncSkuWarehouseNo(InputStream inputStream) {
    List<SkuExcelDto> sheetRowsRaw =
        EasyExcel.read(inputStream).headRowNumber(1).head(SkuExcelDto.class).sheet(0).doReadSync();
    List<SkuExcelDto> sheetRows = sheetRowsRaw.stream().distinct().collect(toList());
    log.info("批量导入价格预处理，总计{}行，去重后{}行", sheetRowsRaw.size(), sheetRows.size());

    List<String> errorCodes = new LinkedList<>();
    for (SkuExcelDto sheetRow : sheetRows) {
      try {
        Response response;
        String code = sheetRow.getSkuCode();
        ItemSku itemSku = itemSkuGateway.getBySkuCode(code);
        if (StringUtil.isBlank(itemSku.getKingDeeId())) {
          response = kingDeeTemplate.handler(ApiEnum.SAVE_SKU, itemSku.getId(), "");
        } else {
          response =
              kingDeeTemplate.handler(ApiEnum.UPDATE_SKU, itemSku.getId(), itemSku.getKingDeeId());
        }
        if (!response.isSuccess()) {
          errorCodes.add(code);
          log.error("syncSkuWarehouseNoFail.skuCode:{}", sheetRow.getSkuCode());
        } else {
          log.info("syncSkuWarehouseNoSuccess.skuCode:{}", sheetRow.getSkuCode());
        }
        TimeUnit.MILLISECONDS.sleep(500);
      } catch (Exception e) {
        log.error("处理异常，sheetRow:{}", JsonUtil.toJson(sheetRow), e);
      }
    }
    if (CollUtil.isNotEmpty(errorCodes)) {
      RedisUtil.set("syncSkuWarehouseNo_skuCode", JsonUtil.toJson(errorCodes));
    }
    log.info("组合商品价格同步完成");
  }

  /** 处理商品单位 */
  public void clearItemUnit() {
    ItemSkuGateway itemSkuGateway = SpringUtil.getBean(ItemSkuGateway.class);

    IItemProcurementService bean = SpringUtil.getBean(IItemProcurementService.class);
    bean.lambdaQuery()
        .eq(ItemProcurement::getBaseUnitId, 0)
        .select()
        .list()
        .forEach(
            itemProcurement -> {
              List<ItemSku> skuList = itemSkuGateway.getSkuList(itemProcurement.getItemId());
              if (CollUtil.isNotEmpty(skuList)) {
                ItemSku itemSku = skuList.get(0);
                Optional<BaseUnit> optionalBaseUnit =
                    baseUnitService
                        .lambdaQuery()
                        .eq(BaseUnit::getName, itemSku.getUnit())
                        .select()
                        .oneOpt();
                if (optionalBaseUnit.isPresent()) {
                  Long id = optionalBaseUnit.get().getId();
                  itemProcurement.setBaseUnitId(id);
                  bean.updateById(itemProcurement);
                }
              }
            });
  }

  public void clearSkuUnit(Long itemSkuId) {
    AtomicInteger count = new AtomicInteger();
    List<ItemSku> list =
        iItemSkuService
            .lambdaQuery()
            .ge(Objects.nonNull(itemSkuId), ItemSku::getId, itemSkuId)
            .orderByAsc(ItemSku::getId)
            .list();

    Map<String, String> kdIdAndNameMap =
        baseUnitService.lambdaQuery().list().stream()
            .collect(Collectors.toMap(BaseUnit::getKingDeeId, BaseUnit::getName));

    for (ItemSku itemSku : list) {
      try {
        clearSkuUnitHandler(itemSku, kdIdAndNameMap);
        TimeUnit.MILLISECONDS.sleep(200);
      } catch (Exception e) {
        log.error(
            "clearSkuUnitHandler error,skuCode:{},skuId:{}",
            itemSku.getSkuCode(),
            itemSku.getId(),
            e);
        break;
      } finally {
        count.getAndIncrement();
        log.info("处理sku单位处理完成，累计:{}", count.get());
      }
    }
  }

  private void clearSkuUnitHandler(ItemSku itemSku, Map<String, String> kdIdAndNameMap)
      throws Exception {
    String skuCode = itemSku.getSkuCode();
    String unitId = reqTemplate.querySkuUnit(skuCode);
    if (StrUtil.isBlank(unitId) && StrUtil.isNotBlank(itemSku.getProviderSpecifiedCode())) {
      unitId = reqTemplate.querySkuUnit(itemSku.getProviderSpecifiedCode());
    }
    if (StrUtil.isNotBlank(unitId)) {
      String name = kdIdAndNameMap.get(unitId);
      if (StrUtil.hasBlank(name) && !name.equals(itemSku.getUnit())) {
        itemSku.setUnit(name);
        iItemSkuService.updateById(itemSku);
      }
    }
  }

  public void fillComposeSkuCostProportion() {
    List<ComposeSku> list =
        iComposeSkuService
            .lambdaQuery()
            .isNull(ComposeSku::getCostProportion)
            .or()
            .eq(ComposeSku::getCostProportion, BigDecimal.ZERO)
            .select()
            .list();
    Map<Long, List<ComposeSku>> collect =
        list.stream().collect(Collectors.groupingBy(ComposeSku::getCombinationId));
    collect.forEach(
        (combinationId, composeSkuList) -> {
          try {
            Map<String, ComposeSku> composeSkuMap =
                composeSkuList.stream().collect(Collectors.toMap(ComposeSku::getSkuCode, v -> v));

            List<String> skuCodeList =
                composeSkuList.stream().map(ComposeSku::getSkuCode).collect(toList());
            Map<String, ItemSku> skuCodeMap =
                iItemSkuService
                    .lambdaQuery()
                    .in(ItemSku::getSkuCode, skuCodeList)
                    .select()
                    .list()
                    .stream()
                    .collect(Collectors.toMap(ItemSku::getSkuCode, v -> v));

            List<ProportionCmd> cmdList = new LinkedList<>();
            composeSkuList.forEach(
                val -> {
                  ProportionCmd cmd = new ProportionCmd();
                  ItemSku itemSku = skuCodeMap.get(val.getSkuCode());
                  if (Objects.nonNull(itemSku)) {
                    cmd.setSkuCode(val.getSkuCode());
                    cmd.setCount(val.getCount());
                    cmd.setCostPrice(itemSku.getCostPrice());
                    cmd.setSalePrice(itemSku.getSalePrice());
                    cmdList.add(cmd);
                  }
                });
            if (CollUtil.isNotEmpty(cmdList)) {
              List<ProportionVO> proportionList =
                  combinationItemBizService.calculateProportion(cmdList);
              List<ComposeSku> updateList =
                  proportionList.stream()
                      .map(
                          val -> {
                            ComposeSku composeSku = composeSkuMap.get(val.getSkuCode());
                            if (Objects.nonNull(composeSku)) {
                              composeSku.setCostProportion(val.getCostProportion());
                              composeSku.setSaleProportion(val.getSalesProportion());
                            }
                            return composeSku;
                          })
                      .filter(Objects::nonNull)
                      .collect(toList());
              iComposeSkuService.updateBatchById(updateList);
            }

            log.info("组合装下属单品的占比数据处理完成。combinationId:{}", combinationId);
          } catch (Exception e) {
            log.error("组合装下属单品的占比数据处理异常。combinationId:{}", combinationId, e);
          }
        });
  }

  public void updateSingleBuyerPrice() {
    iNewGoodsService
        .lambdaQuery()
        .isNull(NewGoods::getSingleBuyPrice)
        .or()
        .eq(NewGoods::getSingleBuyPrice, "")
        .select()
        .list()
        .forEach(
            val -> {
              BigDecimal singleBuyPrice = newGoodsBizService.getSingleBuyPrice(val.getDailyPrice());
              val.setSingleBuyPrice(singleBuyPrice);
              iNewGoodsService.updateById(val);
              log.info("newGoods id:{},price:{}", val.getId(), singleBuyPrice);
            });
  }

  public void updateItemLaunchPlanActivityItem() {
    iItemLaunchPlanItemRefService
        .lambdaQuery()
        .isNull(ItemLaunchPlanItemRef::getActivePeriodStart)
        .or()
        .eq(ItemLaunchPlanItemRef::getActivePeriodStart, 0)
        .or()
        .isNull(ItemLaunchPlanItemRef::getActivePeriodEnd)
        .or()
        .eq(ItemLaunchPlanItemRef::getActivePeriodEnd, 0)
        .select(ItemLaunchPlanItemRef::getItemId)
        .list()
        .forEach(
            val -> {
              try {
                List<NewGoods> list =
                    iNewGoodsService
                        .lambdaQuery()
                        .eq(NewGoods::getItemId, val.getItemId())
                        .orderByDesc(NewGoods::getActivePeriodStart)
                        .last("limit 1")
                        .list();
                if (CollUtil.isNotEmpty(list)) {
                  NewGoods newGoods = list.get(0);
                  // 长期有效，将计划的活动时间都置为0,前端设置，当活动时间都为0时，表示长期。
                  if (newGoods.getIsLongTerm()) {
                    iItemLaunchPlanItemRefService
                        .lambdaUpdate()
                        .set(ItemLaunchPlanItemRef::getActivePeriodStart, 0)
                        .set(ItemLaunchPlanItemRef::getActivePeriodEnd, 0)
                        .eq(ItemLaunchPlanItemRef::getItemId, newGoods.getItemId())
                        .update();
                  } else {
                    boolean canUpdate1 =
                        Objects.nonNull(newGoods.getActivePeriodStart())
                            && newGoods.getActivePeriodStart() > 0;
                    boolean canUpdate2 =
                        Objects.nonNull(newGoods.getActivePeriodEnd())
                            && newGoods.getActivePeriodEnd() > 0;
                    if (canUpdate1 && canUpdate2) {
                      iItemLaunchPlanItemRefService
                          .lambdaUpdate()
                          .set(
                              ItemLaunchPlanItemRef::getActivePeriodStart,
                              newGoods.getActivePeriodStart())
                          .set(
                              ItemLaunchPlanItemRef::getActivePeriodEnd,
                              newGoods.getActivePeriodEnd())
                          .eq(ItemLaunchPlanItemRef::getItemId, newGoods.getItemId())
                          .update();
                    }
                  }
                }
                log.info("更新上新计划活动时间完成,itemId:{}", val.getItemId());
              } catch (Exception e) {
                log.error("更新上新计划活动时间异常,itemId:{}", val.getItemId(), e);
              }
            });
  }

  public void stockInAndOutOrderHandler() {
    List<String> warehouseName = getWarehouseName();
    List<String> matchWarehouseName = new LinkedList<>();
    List<String> purchaseOrderNoList = new LinkedList<>();
    warehouseService
        .lambdaQuery()
        .in(Warehouse::getName, warehouseName)
        .select()
        .list()
        .forEach(
            val -> {
              String no = val.getNo();

              PurchaseOrderPageQuery query = new PurchaseOrderPageQuery();
              query.setWarehouseNo(no);
              query.setStartDt(1669824000L);
              query.setStartEnd(1672502399L);
              PageResponse<PurchaseOrderPageVO> page = purchaseOrderController.page(query);
              if (CollUtil.isEmpty(page.getData())) {
                return;
              }
              String purchaseOrderNo = page.getData().get(0).getNo();
              if (StrUtil.isBlank(purchaseOrderNo)) {
                return;
              }

              List<StockInOrder> list1 =
                  stockInOrderService
                      .lambdaQuery()
                      .eq(StockInOrder::getPurchaseOrderNo, purchaseOrderNo)
                      .select()
                      .list();
              boolean can1 = CollUtil.isEmpty(list1);
              List<StockOutOrder> list2 =
                  stockOutOrderService
                      .lambdaQuery()
                      .eq(StockOutOrder::getPurchaseOrderNo, purchaseOrderNo)
                      .select()
                      .list();
              boolean can2 = CollUtil.isEmpty(list2);

              if (can1 && can2) {
                purchaseOrderNoList.add(purchaseOrderNo);
                matchWarehouseName.add(val.getName());
              }
            });

    warehouseName.removeAll(matchWarehouseName);
    log.info("剩余未完成处理的仓库编码:{}", JsonUtil.toJson(warehouseName));

    ErpPurchaseOrderHandler bean1 = SpringUtil.getBean(ErpPurchaseOrderHandler.class);
    bean1.erpStockInOrOutOrderGeneratorCompensate(purchaseOrderNoList);
  }

  private List<String> getWarehouseName() {
    String s =
        "AKAK面包干仓--子账号\n"
            + "cemoy精华油WMS仓\n"
            + "JBL儿童耳机仓\n"
            + "Jollybaby布书仓--线下\n"
            + "MZ启惠玩具-2e51fab\n"
            + "阿罗拉儿童摩托车仓\n"
            + "爱登WMS仓\n"
            + "百科绘本仓\n"
            + "宝宝餐椅仓\n"
            + "北京联信WMS仓\n"
            + "北京拍店电子商务有限公司杭州分公司-6ab6c5c6\n"
            + "车达芝士香肠WMS仓\n"
            + "吹风机仓\n"
            + "磁力拼装枪仓\n"
            + "大白鹅仓--子账号\n"
            + "大卤兮兮-ef4a8b8\n"
            + "德尔玛WM仓--新仓\n"
            + "风雪户外仓\n"
            + "格润安线下仓\n"
            + "花印卸妆膏仓--子账号\n"
            + "华盛水产-7eb1e2e2\n"
            + "惠尔顿安全座椅仓\n"
            + "吉速-31a10a8\n"
            + "绞肉机仓\n"
            + "扭扭车仓\n"
            + "轻婵仓--子账号\n"
            + "乳胶漆仓\n"
            + "深圳流山仓\n"
            + "升降晾衣架仓\n"
            + "书礼行知文化仓\n"
            + "她炽格子衫薄仓\n"
            + "天虹坚果礼盒仓--线下\n"
            + "天虹牌-a4849bf\n"
            + "听甜蓝莓汁WMS仓\n"
            + "听甜蓝莓汁仓--线下\n"
            + "为家美电饭煲-仓--子账号\n"
            + "无名小厨WMS仓\n"
            + "线下表格暂时过渡仓\n"
            + "新安怡仓--子账号\n"
            + "新贝WMS仓\n"
            + "一次性口罩仓\n"
            + "桌面饮水机WMS仓";
    String[] split = s.split("\n");
    return Arrays.asList(split);
  }

  public void updatePurchaseTaxRate(InputStream inputStream) {
    List<ExportItemProviderRow> sheetRowsRaw =
        EasyExcel.read(inputStream)
            .headRowNumber(1)
            .head(ExportItemProviderRow.class)
            .sheet(0)
            .doReadSync();
    List<ExportItemProviderRow> sheetRows = sheetRowsRaw.stream().distinct().collect(toList());
    log.info("批量处理sku采购税率，总计{}行，去重后{}行", sheetRowsRaw.size(), sheetRows.size());

    for (ExportItemProviderRow sheetRow : sheetRows) {
      iItemSkuService
          .lambdaUpdate()
          .set(ItemSku::getPurchaseTaxRate, sheetRow.getPurchaseTaxRate())
          .eq(ItemSku::getSkuCode, sheetRow.getSkuCode())
          .update();
      // 更新商品采购税率
      String skuCode = sheetRow.getSkuCode();
      List<ItemSku> list = iItemSkuService.lambdaQuery().eq(ItemSku::getSkuCode, skuCode).list();
      if (CollUtil.isNotEmpty(list)) {
        ItemSku itemSku = list.get(0);
        Long itemId = itemSku.getItemId();

        iItemProcurementService
            .lambdaUpdate()
            .set(ItemProcurement::getPurchaseRate, sheetRow.getPurchaseTaxRate())
            .eq(ItemProcurement::getItemId, itemId)
            .update();
      }
    }

    log.info("批量处理sku采购税率完成");
  }

  public void stockOrderHandlerTmp() {
    List<String> purchaseOrderNos =
        logService
            .lambdaQuery()
            .gt(Log::getCreatedAt, 1680591600L)
            .in(Log::getType, ListUtil.of(-99, -98))
            .notLike(Log::getError, "金蝶")
            .list()
            .stream()
            .map(Log::getReq)
            .collect(toList());

    purchaseOrderHandler.erpStockInOrOutOrderGeneratorCompensate(purchaseOrderNos);
  }

  public String exportCombinationSkuPrice(Boolean isSingle) {
    File file = new File("price" + ".xlsx");
    if (isSingle) {
      ExcelWriter writer = ExcelUtil.getWriter(file);
      List<PurchaseSingleSkuCombinationPrice> list =
          singleSkuCombinationPriceService.lambdaQuery().list();
      writer.write(list, true);
      writer.close();
    } else {
      ExcelWriter writer = ExcelUtil.getWriter(file);
      List<PurchaseRandomSkuCombinationPrice> list =
          randomSkuCombinationPriceService.lambdaQuery().list();
      writer.write(list, true);
      writer.close();
    }
    UploadFileAction action = UploadFileAction.ofFile(file);
    FileStub fileStub = fileGateway.uploadFile(action);
    FileUtil.del(file);
    String url = fileStub.getUrl();

    final OssConfig ossConfig = new OssConfig();
    ossConfig.setEndpoint("oss-cn-hangzhou.aliyuncs.com");
    ossConfig.setAccessKeyId("LTAI5t7qeNHcvUXKwcab3gWp");
    ossConfig.setAccessKeySecret("******************************");
    ossConfig.setPublicBucket("daddyoss");
    ossConfig.setPrivateBucket("daddyoss-private");
    ossConfig.setPublicUrl("https://daddyoss.oss-cn-hangzhou.aliyuncs.com");
    ossConfig.setPrivateUrl("https://daddyoss-private.oss-cn-hangzhou.aliyuncs.com");
    ossConfig.setPrefixDir("/Upload/supplier/item/");
    OssGatewayImpl ossGateway = new OssGatewayImpl(ossConfig);
    String s1 = url.replaceAll("https://daddyoss-private.oss-cn-hangzhou.aliyuncs.com", "");
    return ossGateway.generatePresignedUrl(true, s1);
  }

  /*public static void main(String[] args) {
      String url = "https://daddyoss-private.oss-cn-hangzhou.aliyuncs.com/Upload/supplier/item/Upload/supplier/item/1722123298242396160.xlsx";
      String s1 = url.replaceAll("https://daddyoss-private.oss-cn-hangzhou.aliyuncs.com", "");
      System.out.println(s1);
  }*/

  @Autowired KingDeeTemplate kingDeeTemplate;

  public void syncItem(String skuCodes) {
    List<String> list = Arrays.asList(skuCodes.split(","));
    List<Long> itemIdList =
        iItemSkuService.lambdaQuery().in(ItemSku::getSkuCode, list).list().stream()
            .map(ItemSku::getItemId)
            .collect(toList());
    itemIdList.forEach(
        id -> {
          try {
            kingDeeTemplate.syncItem(id);
          } catch (Exception e) {
            log.error("item sync error,id:{}", id, e);
          }
        });
  }

  public Boolean updateSkuUnit(String skuCode, String unit) {
    return iItemSkuService
        .lambdaUpdate()
        .set(ItemSku::getUnit, unit)
        .eq(ItemSku::getSkuCode, skuCode)
        .update();
  }

  @Resource IItemService iItemService;

  @Resource MsgSender msgSender;

  public void skuPriceAxis() {
    int size = 500;
    Long minId = 0L;
    Integer count = iItemSkuService.lambdaQuery().count();
    int pages = count % size == 0 ? count / size : (count / size + 1);
    String fileName = "skuPriceAxis.xlsx";
    File excelFile = new File(fileName);
    com.alibaba.excel.ExcelWriter excelWriter = EasyExcel.write(excelFile).build();
    WriteSheet writeSheet = EasyExcel.writerSheet("价格轴").build();

    try {
      for (int i = 0; i < pages; i++) {

        List<ItemSku> thisList =
            iItemSkuService
                .lambdaQuery()
                .gt(ItemSku::getId, minId)
                .orderByAsc(ItemSku::getId)
                .last("limit " + size)
                .list();
        minId = thisList.get(thisList.size() - 1).getId();

        List<Long> skuIdList = thisList.stream().map(ItemSku::getId).collect(toList());
        Map<Long, List<ItemSkuPrice>> collect =
            iItemSkuPriceService.lambdaQuery().in(ItemSkuPrice::getSkuId, skuIdList).list().stream()
                .collect(Collectors.groupingBy(ItemSkuPrice::getSkuId));
        List<List<String>> thisExcelList = new LinkedList<>();
        thisList.forEach(
            itemSku -> {
              List<String> list = new LinkedList<>();
              Long itemId = itemSku.getItemId();
              Item item = iItemService.getById(itemId);
              String itemName = Objects.isNull(item) ? "" : item.getName();
              list.add(itemName);
              list.add(itemSku.getSkuCode());

              List<ItemSkuPrice> itemSkuPrices = collect.get(itemSku.getId());
              if (CollUtil.isNotEmpty(itemSkuPrices)) {
                itemSkuPrices.stream()
                    .sorted(Comparator.comparing(ItemSkuPrice::getId).reversed())
                    .forEach(
                        val -> {
                          list.add(
                              Objects.nonNull(val.getPrice()) ? val.getPrice().toString() : "");
                          Long startTime = val.getStartTime();
                          Long endTime = val.getEndTime();
                          String sT =
                              Objects.nonNull(startTime) && startTime > 0
                                  ? DateUtil.parseTimeStamp(startTime, DateUtil.DEFAULT_FORMAT)
                                  : "";
                          String et =
                              Objects.nonNull(endTime) && endTime > 0
                                  ? DateUtil.parseTimeStamp(endTime, DateUtil.DEFAULT_FORMAT)
                                  : "";
                          list.add(sT + "-" + et);
                        });
              }
              thisExcelList.add(list);
            });
        if (CollUtil.isNotEmpty(thisExcelList)) {
          excelWriter.write(thisExcelList, writeSheet);
        }
      }
    } catch (Exception e) {
      log.error("skuPriceAxis excelWriter error", e);
    } finally {
      excelWriter.finish();
    }

    UploadFileAction action = UploadFileAction.ofFile(excelFile);
    String url = fileGateway.uploadFile(action).getUrl();
    log.info("skuPriceAxis excel url:{}", url);

    WechatMsg wechatMsg = new WechatMsg();
    wechatMsg.setTitle("\uD83E\uDD17尊敬的陛下~~~");
    wechatMsg.setContent(StrUtil.format("sku价格轴excel生成完毕。url:{}", url));
    wechatMsg.setRecipient(GlobalConstant.SEVEN_UP);
    msgSender.sendMsgWithText(wechatMsg);

    if (!excelFile.delete()) {
      log.info("skuPriceAxis excel delete fail");
    }
  }

  @Resource IOrderSettlementFormService iOrderSettlementFormService;

  @Resource IOrderSettlementDetailService iOrderSettlementDetailService;

  @Resource OrderSettlementSysService orderSettlementSysService;

  public void updateStatic() {
    List<OrderSettlementForm> list =
        iOrderSettlementFormService
            .lambdaQuery()
            .eq(OrderSettlementForm::getCreatedUid, 0)
            .and(
                ww -> {
                  ww.eq(OrderSettlementForm::getNo, "").or().isNull(OrderSettlementForm::getNo);
                })
            .list();
    for (OrderSettlementForm form : list) {
      try {
        Long id = form.getId();
        List<OrderSettlementDetail> list1 =
            iOrderSettlementDetailService
                .lambdaQuery()
                .eq(OrderSettlementDetail::getFormId, id)
                .list();
        String temporaryStaticDo = orderSettlementSysService.getTemporaryStaticDo(list1);
        form.setStaticInfo(temporaryStaticDo);
        iOrderSettlementFormService.updateById(form);
        log.error("order settlement updateStatic success. formId:{}", form.getId());
      } catch (Exception e) {
        log.error("order settlement updateStatic error. formId:{}", form.getId(), e);
      }
    }
  }

  @Resource IPurchaseOrderService iPurchaseOrderService;

  public void orderSettlementRestart() {

    Set<String> finishedNos =
        iOrderSettlementFormService
            .lambdaQuery()
            .ge(OrderSettlementForm::getSettlementStartDate, 1690819200L)
            .le(OrderSettlementForm::getSettlementEndDate, 1693497599L)
            .ne(OrderSettlementForm::getNo, "")
            .isNotNull(OrderSettlementForm::getNo)
            .eq(OrderSettlementForm::getStatus, 1)
            .list()
            .stream()
            .map(OrderSettlementForm::getPurchaseOrderNo)
            .collect(Collectors.toSet());
    log.info("orderSettlementRestart finishedNos size:{}", finishedNos.size());

    // all purchaseOrderNos
    Set<String> allPurchaseOrderNos =
        iPurchaseOrderService
            .lambdaQuery()
            .eq(PurchaseOrder::getPurchaseDate, 1693411200L)
            .eq(PurchaseOrder::getType, 2)
            .list()
            .stream()
            .map(PurchaseOrder::getNo)
            .collect(Collectors.toSet());

    List<String> collect =
        allPurchaseOrderNos.stream().filter(no -> !finishedNos.contains(no)).collect(toList());
    log.info("orderSettlementRestart restartPurchaseOrderNos size:{}", collect.size());

    for (String purchaseOrderNo : collect) {
      orderSettlementSysService.autoCreateOrderSettlementInfo(TimeBO.of("202308"), purchaseOrderNo);
    }
  }

  @Resource BanniuCommonService banniuCommonService;

  @SneakyThrows
  public void banniuBuyer() {
    banniuCommonService.syncBuyer();
  }

  public void demoQueryChain() {
    try {
      List<WdtOrderDetailWrapper> list =
          iWdtOrderDetailWrapperService
              .lambdaQuery()
              .eq(WdtOrderDetailWrapper::getProviderId, 0L)
              .eq(WdtOrderDetailWrapper::getOperateTime, "202309")
              .eq(WdtOrderDetailWrapper::getType, 99)
              .list();
      Integer count =
          iWdtOrderDetailWrapperService
              .lambdaQuery()
              .eq(WdtOrderDetailWrapper::getProviderId, 0L)
              .eq(WdtOrderDetailWrapper::getOperateTime, "202309")
              .eq(WdtOrderDetailWrapper::getType, 99)
              .count();

      List<WdtOrderDetailWrapper> list1 =
          iWdtOrderDetailWrapperService
              .lambdaQuery()
              .eq(WdtOrderDetailWrapper::getProviderId, 0L)
              .eq(WdtOrderDetailWrapper::getOperateTime, "202309")
              .eq(WdtOrderDetailWrapper::getType, 99)
              .eq(WdtOrderDetailWrapper::getWarehouseNo, "CK100")
              .list();
      Integer count1 =
          iWdtOrderDetailWrapperService
              .lambdaQuery()
              .eq(WdtOrderDetailWrapper::getProviderId, 0L)
              .eq(WdtOrderDetailWrapper::getOperateTime, "202309")
              .eq(WdtOrderDetailWrapper::getType, 99)
              .eq(WdtOrderDetailWrapper::getWarehouseNo, "CK100")
              .count();
    } catch (Exception e) {
      log.error("demoQueryChain", e);
    }
  }

  public void orderSettlement() {
    List<OrderSettlementForm> list =
        iOrderSettlementFormService
            .lambdaQuery()
            .eq(OrderSettlementForm::getStatus, OrderSettlementStatus.CONFIRMED)
            .list();
    if (CollUtil.isEmpty(list)) {
      log.info("orderSettlement查询为空");
      return;
    }
    List<String> purchaseOrderNos =
        list.stream().map(OrderSettlementForm::getPurchaseOrderNo).collect(toList());
    log.info("orderSettlement 已结算 purchaseOrderNos size:{}", purchaseOrderNos.size());

    TimeBO timeBO = TimeBO.of("202308");
    List<PurchaseOrder> list1 =
        iPurchaseOrderService
            .lambdaQuery()
            .eq(PurchaseOrder::getPurchaseDate, timeBO.getPurchaseDateTime())
            .eq(PurchaseOrder::getType, 2)
            .eq(PurchaseOrder::getBuyerUserId, -1)
            .notIn(PurchaseOrder::getNo, purchaseOrderNos)
            .list();
    log.info("需要处理的采购订单数据。size:{}", list1.size());
    for (PurchaseOrder purchaseOrder : list1) {
      orderSettlementSysService.autoCreateOrderSettlementInfo(timeBO, purchaseOrder.getNo());
    }
  }

  @Resource OrderSettlementBizService orderSettlementBizService;

  @Resource WdtOrderDetailWrapperMapper wdtOrderDetailWrapperMapper;

  public void fixSettlementDetailSkuDeliverQuantity() {
    List<OrderSettlementForm> list =
        iOrderSettlementFormService
            .lambdaQuery()
            .eq(OrderSettlementForm::getCreatedUid, 0)
            .eq(OrderSettlementForm::getStatus, OrderSettlementStatus.CONFIRMED)
            .list();
    int formSize = list.size();
    int count = 0;
    log.info("fixSettlementDetail.form list size:{}", formSize);
    for (OrderSettlementForm form : list) {
      try {
        List<OrderSettlementDetail> detailList =
            iOrderSettlementDetailService
                .lambdaQuery()
                .eq(OrderSettlementDetail::getFormId, form.getId())
                .list();
        log.info(
            "fixSettlementDetail. formId:{},detail list size:{}", form.getId(), detailList.size());
        if (CollUtil.isEmpty(detailList)) {
          continue;
        }
        String warehouseNo = form.getWarehouseNo();
        Long sProviderId = form.getPProviderId();
        Long settlementStartDate = form.getSettlementStartDate();
        String operateMonth =
            DateUtil.parseTimeStamp(settlementStartDate, DatePattern.SIMPLE_MONTH_PATTERN);
        List<SkuDeliverNumDo> skuDeliverNumDos =
            wdtOrderDetailWrapperMapper.selectSkuDeliverNum(
                warehouseNo, operateMonth, sProviderId, WrapperType.TOTAL.getValue());
        log.info("fixSettlementDetail. skuDeliverNumDos size:{}", skuDeliverNumDos.size());

        AtomicBoolean needUpdate = new AtomicBoolean(false);
        for (OrderSettlementDetail detail : detailList) {
          String code = detail.getSkuCode();
          BigDecimal temporaryPrice = detail.getTemporaryPrice();
          skuDeliverNumDos.stream()
              .filter(val -> val.getSkuCode().equals(code) && val.getPrice().equals(temporaryPrice))
              .findFirst()
              .ifPresent(
                  pp -> {
                    if (!pp.getDeliverNum().equals(detail.getDeliverQuantity())) {
                      detail.setDeliverQuantity(pp.getDeliverNum());
                      needUpdate.set(true);
                    }
                  });
        }
        if (needUpdate.get()) {
          iOrderSettlementDetailService.updateBatchById(detailList);
        }
        count++;
        log.info(
            "fixSettlementDetail.finishedId:{},successCount:{},totalSize:{}",
            form.getId(),
            count,
            formSize);
      } catch (Exception e) {
        log.error("fixSettlementDetail error.finishedId:{}", form.getId(), e);
      }
    }
  }

  @Resource OrderSettlementFormMapper orderSettlementFormMapper;

  /**
   * select * from order_settlement_form where created_uid != 0 and settlement_start_date =
   * 1693497600
   */
  public void recoverSysOrderSettlementTmp() {
    List<OrderSettlementForm> list =
        iOrderSettlementFormService
            .lambdaQuery()
            .ne(OrderSettlementForm::getCreatedUid, 0)
            .eq(OrderSettlementForm::getStatus, OrderSettlementStatus.CONFIRMED)
            .eq(OrderSettlementForm::getSettlementStartDate, 1693497600L)
            .list();
    for (OrderSettlementForm form : list) {
      String relateId = form.getRelateId();
      if (!StringUtils.hasText(relateId)) {
        continue;
      }
      List<Long> ids =
          ListUtil.of(relateId.split(",")).stream().map(Long::valueOf).collect(toList());
      orderSettlementFormMapper.recoverData(ids);
      orderSettlementFormMapper.recoverDataDetail(ids);
    }
  }

  @Resource ProviderBizServiceImpl providerBizServiceImpl;

  @Resource ReqJsonUtil reqJsonUtil;

  public void syncProvider() {
    List<Provider> list =
        iProviderService
            .lambdaQuery()
            .ge(Provider::getUpdatedAt, 1696089600L)
            .or(ww -> ww.ge(Provider::getCreatedAt, 1696089600L))
            .orderByAsc(Provider::getId)
            .list();
    log.info("syncProvider listSize:{}", list.size());
    list.forEach(
        val -> {
          try {
            Response handler =
                kingDeeTemplate.handler(ApiEnum.UPDATE_PROVIDER, val.getId(), val.getKingDeeId());
            log.info("syncProvider res:{}.providerNo:{}", handler.isSuccess(), val.getProviderNo());
          } catch (Exception e) {
            // ignore
          } finally {
            try {
              TimeUnit.MILLISECONDS.sleep(500);
            } catch (InterruptedException e) {
              // ignore
            }
          }
        });
  }

  public void queryKingDeeProviderBankInfo() {
    List<Provider> list = iProviderService.lambdaQuery().list();

    for (Provider provider : list) {
      String providerNo = provider.getProviderNo();
      if (StrUtil.isNotBlank(providerNo)) {
        Map<String, String> stringStringMap;
        try {
          stringStringMap = reqTemplate.queryProviderBankInfo(providerNo);
          log.info(
              "查询金蝶供应商信息返回。kingDeeProviderBankRes:{},providerNo:{}",
              JsonUtil.toJson(stringStringMap),
              providerNo);
        } catch (Exception e) {
          log.error("查询金蝶供应商信息失败。providerNo:{}", providerNo, e);
          continue;
        }
        if (CollUtil.isNotEmpty(stringStringMap) && stringStringMap.size() == 2) {
          String bankCode = stringStringMap.get("bankCode");
          String bankName = stringStringMap.get("bankName");

          List<BankAccount> accountByProviderList =
              iBankAccountService.getAccountByProviderId(provider.getId());
          // 供应商之前不存在财务信息，走新增处理。
          if (CollUtil.isEmpty(accountByProviderList)) {
            BankAccount bankAccount = new BankAccount();
            bankAccount.setProviderId(provider.getId());
            bankAccount.setBankCard(bankCode);
            bankAccount.setBankDeposit(bankName);
            bankAccount.setDescription(StrUtil.EMPTY);
            iBankAccountService.save(bankAccount);
            log.info("新增财务信息。providerNo:{}", providerNo);
          } else {
            Optional<BankAccount> sameBankCodeOpt =
                accountByProviderList.stream()
                    .filter(val -> val.getBankCard().equals(bankCode))
                    .findFirst();
            if (sameBankCodeOpt.isPresent()) {
              BankAccount bankAccount = sameBankCodeOpt.get();
              if (!bankAccount.getBankDeposit().equals(bankName)) {
                bankAccount.setBankDeposit(bankName);
                iBankAccountService.updateById(bankAccount);
                log.info("更新银行名称。providerNo:{}", providerNo);
              }
            } else {
              // 新增
              BankAccount bankAccount = new BankAccount();
              bankAccount.setProviderId(provider.getId());
              bankAccount.setBankCard(bankCode);
              bankAccount.setBankDeposit(bankName);
              bankAccount.setDescription(StrUtil.EMPTY);
              iBankAccountService.save(bankAccount);
              log.info("添加新的财务信息。providerNo:{}", providerNo);
            }
          }
        }
        log.info("拉去金蝶信息，处理供应商财务信息完成。queryBankInfo.providerNo:{}", providerNo);
      }
    }
  }

  public void exportProviderBankInfo() {
    ProviderMapper providerMapper = SpringUtil.getBean(ProviderMapper.class);
    List<ProviderExportInfoDto> providerBankInfoDtoList =
        providerMapper.getProviderBankInfoDtoList();
    log.info("导出供应商相关信息。fileSize:{}", providerBankInfoDtoList.size());

    for (ProviderExportInfoDto providerBankInfoDto : providerBankInfoDtoList) {
      Long mainChargerUserId = providerBankInfoDto.getMainChargerUserId();
      Long secondChargerUserId = providerBankInfoDto.getSecondChargerUserId();
      List<Long> userIds = ListUtil.of(mainChargerUserId, secondChargerUserId);
      if (CollUtil.isNotEmpty(userIds)) {
        Map<Long, StaffInfo> longStaffInfoMap = userGateway.batchQueryStaffInfoByIds(userIds);

        StaffInfo orDefault = longStaffInfoMap.get(mainChargerUserId);
        String n1 = Objects.nonNull(orDefault) ? orDefault.getNickname() : "";
        providerBankInfoDto.setMainChargerUser(n1);

        StaffInfo staffInfo = longStaffInfoMap.get(secondChargerUserId);
        String n2 = Objects.nonNull(staffInfo) ? staffInfo.getNickname() : "";
        providerBankInfoDto.setSecondChargerUser(n2);
        //            }
      }

      String fileName = DateUtil.currentTime() + ".xlsx";
      java.io.File file = new java.io.File(fileName);
      EasyExcel.write(file, ProviderExportInfoDto.class)
          .sheet("供应商数据")
          .doWrite(providerBankInfoDtoList);
      UploadFileAction uploadFileAction = UploadFileAction.ofFile(file);
      FileStub fileStub = fileGateway.uploadFile(uploadFileAction);
      String s = ossGateway.generatePresignedUrl(true, URLUtil.getPath(fileStub.getUrl()));
      log.info("导出供应商相关信息完成。url:{}", s);
    }
  }

  public void pushStockInOrder(String no) {
    List<StockInOrder> list =
        stockInOrderService
            .lambdaQuery()
            .eq(StockInOrder::getNo, no)
            .orderByDesc(StockInOrder::getId)
            .list();
    Assert.isTrue(CollUtil.isNotEmpty(list), "编码非法");

    StockInOrder stockInOrder = list.get(0);
    Response handler;
    if (org.apache.commons.lang3.StringUtils.isBlank(stockInOrder.getKingDeeId())) {
      handler = kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_IN_ORDER, stockInOrder.getId(), "");
    } else {
      handler =
          kingDeeTemplate.handler(
              ApiEnum.UPDATE_STOCK_IN_ORDER, stockInOrder.getId(), stockInOrder.getKingDeeId());
    }
    operateLogGateway.addOperatorLog(
        UserContext.getUserId(),
        OperateLogTarget.STOCK_ORDER,
        stockInOrder.getId(),
        "重新推送" + handler.isSuccess(),
        null);
    Assert.state(handler.isSuccess(), "同步采购入库单失败");
  }

  public void deleteStockOrder(String no) {
    if (StringUtils.isEmpty(no)) {
      return;
    }

    if (no.startsWith("CGRK")) {
      final List<StockInOrder> list =
          stockInOrderService.lambdaQuery().eq(StockInOrder::getNo, no).list();
      if (CollUtil.isNotEmpty(list)) {
        final List<Long> idList = list.stream().map(StockInOrder::getId).collect(toList());
        iStockInOrderDetailService
            .lambdaUpdate()
            .in(StockInOrderDetail::getStockInOrderId, idList)
            .remove();
        stockInOrderService.lambdaUpdate().in(StockInOrder::getId, idList).remove();
      }
    }
    if (no.startsWith("CGCK")) {
      final List<StockOutOrder> list =
          stockOutOrderService.lambdaQuery().eq(StockOutOrder::getNo, no).list();
      if (CollUtil.isNotEmpty(list)) {
        final List<Long> idList = list.stream().map(StockOutOrder::getId).collect(toList());
        iStockOutOrderDetailService
            .lambdaUpdate()
            .in(StockOutOrderDetail::getStockOutOrderId, idList)
            .remove();
        stockOutOrderService.lambdaUpdate().in(StockOutOrder::getId, idList).remove();
      }
    }
  }

  public void pushStockOutOrder(String no) {
    List<StockOutOrder> list =
        stockOutOrderService
            .lambdaQuery()
            .eq(StockOutOrder::getNo, no)
            .orderByDesc(StockOutOrder::getId)
            .list();
    Assert.isTrue(CollUtil.isNotEmpty(list), "编码非法");

    StockOutOrder stockOutOrder = list.get(0);
    Response handler;
    if (org.apache.commons.lang3.StringUtils.isBlank(stockOutOrder.getKingDeeId())) {
      handler = kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_OUT_ORDER, stockOutOrder.getId(), "");
    } else {
      handler =
          kingDeeTemplate.handler(
              ApiEnum.SAVE_STOCK_OUT_ORDER, stockOutOrder.getId(), stockOutOrder.getKingDeeId());
    }
    operateLogGateway.addOperatorLog(
        UserContext.getUserId(),
        OperateLogTarget.STOCK_OUT_ORDER,
        stockOutOrder.getId(),
        "重新推送" + handler.isSuccess(),
        null);
    Assert.state(handler.isSuccess(), "同步采购出库单失败");
  }

  public void pushKingDee(String purchaseOrderNo) {
    List<StockInOrder> list =
        stockInOrderService
            .lambdaQuery()
            .eq(StockInOrder::getPurchaseOrderNo, purchaseOrderNo)
            .orderByDesc(StockInOrder::getId)
            .list();
    if (CollUtil.isNotEmpty(list)) {
      for (StockInOrder stockInOrder : list) {
        Response handler;
        if (org.apache.commons.lang3.StringUtils.isBlank(stockInOrder.getKingDeeId())) {
          handler = kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_IN_ORDER, stockInOrder.getId(), "");
        } else {
          handler =
              kingDeeTemplate.handler(
                  ApiEnum.UPDATE_STOCK_IN_ORDER, stockInOrder.getId(), stockInOrder.getKingDeeId());
        }
        Assert.state(handler.isSuccess(), "同步采购入库单失败");
      }
    }

    List<StockOutOrder> list2 =
        stockOutOrderService
            .lambdaQuery()
            .eq(StockOutOrder::getPurchaseOrderNo, purchaseOrderNo)
            .orderByDesc(StockOutOrder::getId)
            .list();
    if (CollUtil.isNotEmpty(list2)) {
      for (StockOutOrder stockOutOrder : list2) {
        Response response;
        if (StrUtil.isBlank(stockOutOrder.getKingDeeId())) {
          response =
              kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_OUT_ORDER, stockOutOrder.getId(), "");
        } else {
          response =
              kingDeeTemplate.handler(
                  ApiEnum.SAVE_STOCK_OUT_ORDER,
                  stockOutOrder.getId(),
                  stockOutOrder.getKingDeeId());
        }
        Assert.state(response.isSuccess(), "同步退料出库失败");
      }
    }
  }

  // ---------------------- 采购结算，广进数据脚本处理 ----------------------------------

  @Resource IOperateLogService iOperateLogService;

  @Resource IFileService iFileService;

  public void settlementStatisticsProcessing(List<Long> settlementTimes) {
    List<SettlementStatisticDto> list = new ArrayList<>();

    for (Long settlementTime : settlementTimes) {
      SettlementOrderPageQuery pageQuery = new SettlementOrderPageQuery();
      pageQuery.setPageSize(9999);
      //            pageQuery.setOrderPersonnelIds(ListUtil.of(9007186L));
      pageQuery.setTimes(ListUtil.of(settlementTime));
      PageResponse<SettlementOrderPageVo> settlementOrderPageVoPageResponse =
          orderSettlementBizService.settlementPageQuery(pageQuery);
      List<SettlementOrderPageVo> listData = settlementOrderPageVoPageResponse.getData();

      listData.stream()
          .collect(Collectors.groupingBy(SettlementOrderPageVo::getOrderPersonnel))
          .forEach(
              (orderPersonnel, orderPersonnelList) -> {
                // 仓库数量
                long warehouseCount =
                    orderPersonnelList.stream()
                        .map(SettlementOrderPageVo::getWarehouseNo)
                        .distinct()
                        .count();
                // 结算单数量
                int orderCount = orderPersonnelList.size();
                // 结算单修改数量（操作记录人工导入）
                int manualOrderCount = 0;
                // 差异总数量（暂估结算总数量-结算总数量）
                int totalDiffCount = 0;
                // 总差异金额(暂估总金额-结算总金额)
                BigDecimal totalDiffAmount = BigDecimal.ZERO;

                for (SettlementOrderPageVo val : orderPersonnelList) {
                  boolean manual = false;
                  Integer count =
                      iOperateLogService
                          .lambdaQuery()
                          .eq(OperateLog::getTargetId, val.getId())
                          .eq(OperateLog::getTargetType, OperateLogTarget.ORDER_SETTLEMENT)
                          .like(OperateLog::getMsg, "人工导入")
                          .count();
                  manualOrderCount = manualOrderCount + count;
                  if (count > 0) {
                    manual = true;
                  }

                  Long id = val.getId();
                  OrderSettlementForm orderSettlementForm = iOrderSettlementFormService.getById(id);
                  List<OrderSettlementDetail> details =
                      iOrderSettlementDetailService
                          .lambdaQuery()
                          .eq(OrderSettlementDetail::getFormId, id)
                          .list();

                  int oneDiffCount = 0;
                  BigDecimal oneDiffAmount = BigDecimal.ZERO;
                  for (OrderSettlementDetail detail : details) {

                    int i0 =
                        Objects.isNull(detail.getDeliverQuantity())
                            ? 0
                            : detail.getDeliverQuantity();
                    int i1 =
                        Objects.isNull(detail.getCrossMonthRefundQuantity())
                            ? 0
                            : detail.getCrossMonthRefundQuantity();
                    int i2 =
                        Objects.isNull(detail.getCurrentMonthRefundQuantity())
                            ? 0
                            : detail.getCurrentMonthRefundQuantity();
                    // sku 系统计算 结算数
                    int i = i0 - i1 - i2;
                    BigDecimal tempPrice =
                        Objects.isNull(detail.getTemporaryPrice())
                            ? BigDecimal.ZERO
                            : detail.getTemporaryPrice();
                    // sku 系统计算 结算金额
                    BigDecimal b = tempPrice.multiply(new BigDecimal(i));

                    // 实际计算数量
                    int ri = detail.getSettlementQuantity();
                    // 实际结算金额
                    BigDecimal rPrice =
                        Objects.isNull(detail.getSettlementPrice())
                            ? BigDecimal.ZERO
                            : detail.getSettlementPrice();
                    BigDecimal rp = rPrice.multiply(new BigDecimal(ri));

                    // 查额累计
                    oneDiffCount = oneDiffCount + (i - ri);
                    if ((i - ri) != 0) {
                      log.info(
                          "settlementStatisticsProcessing orderNo:{}.skuCode:{},ti:{},si:{}",
                          orderSettlementForm.getNo(),
                          detail.getSkuCode(),
                          i,
                          ri);
                    }
                    oneDiffAmount = oneDiffAmount.add(b.subtract(rp));
                  }

                  totalDiffCount = totalDiffCount + oneDiffCount;
                  totalDiffAmount = totalDiffAmount.add(oneDiffAmount);
                }

                SettlementStatisticDto settlementStatisticDto = new SettlementStatisticDto();
                settlementStatisticDto.setDate(DateUtil.parseTimeStamp(settlementTime, "yyyy-MM"));
                settlementStatisticDto.setName(orderPersonnel);
                settlementStatisticDto.setWarehouseCount((int) warehouseCount);
                settlementStatisticDto.setOrderCount(orderCount);
                settlementStatisticDto.setManualOrderCount(manualOrderCount);
                settlementStatisticDto.setTotalDiffCount(totalDiffCount);
                settlementStatisticDto.setTotalDiffAmount(totalDiffAmount);
                //
                // settlementStatisticDto.setUnitPriceTotal(unitPriceTotal[0]);
                list.add(settlementStatisticDto);
              });
    }
    log.info("settlementStatisticsProcessing res:{}", JsonUtil.toJson(list));

    File file = new File(RandomUtil.randomString(6) + ".xlsx");
    com.alibaba.excel.ExcelWriter excelWriter = EasyExcelFactory.write(file).build();
    WriteSheet writeSheet = EasyExcel.writerSheet("").head(SettlementStatisticDto.class).build();
    excelWriter.write(list, writeSheet);
    excelWriter.finish();

    UploadFileAction action = UploadFileAction.ofFile(file);
    FileStub fileStub = fileGateway.uploadFile(action);
    com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File sysFile =
        iFileService.getById(fileStub.getFileId());
    String url = ossGateway.generatePresignedUrl(true, sysFile.getPath());
    log.info("settlementStatisticsProcessing excelUrl:{}", url);
  }

  private Map<Integer, BigDecimal> detailDemo(Long formId) {
    Map<Integer, BigDecimal> map = new HashMap<>(2);

    OrderSettlementForm byId = iOrderSettlementFormService.getById(formId);
    String staticInfo = byId.getStaticInfo();
    DetailStaticDo parse = JsonUtil.parse(staticInfo, DetailStaticDo.class);
    // 最终结算总金额
    BigDecimal totalSettlementAmount = parse.getTotalSettlementAmount();

    // 最终结算数量
    //        BigDecimal totalSettlementQuantity = new
    // BigDecimal(parse.getTotalSettlementQuantity());

    BigDecimal tempAmountTotal = BigDecimal.ZERO;
    //        BigDecimal tempQuantityTotal = BigDecimal.ZERO;

    BigDecimal tPrice = BigDecimal.ZERO;
    BigDecimal sPrice = BigDecimal.ZERO;
    List<OrderSettlementDetail> details =
        iOrderSettlementDetailService
            .lambdaQuery()
            .eq(OrderSettlementDetail::getFormId, formId)
            .list();
    for (OrderSettlementDetail detail : details) {
      BigDecimal tempQuantity =
          Objects.isNull(detail.getTemporaryQuantity())
              ? BigDecimal.ZERO
              : new BigDecimal(detail.getTemporaryQuantity());
      BigDecimal tempPrice =
          Objects.isNull(detail.getTemporaryPrice()) ? BigDecimal.ZERO : detail.getTemporaryPrice();
      tempAmountTotal = tempAmountTotal.add(tempQuantity.multiply(tempPrice));
      //            tempQuantityTotal = tempQuantityTotal.add(tempQuantity);

      tPrice = tPrice.add(tempPrice);
      BigDecimal ssPrice =
          Objects.isNull(detail.getSettlementPrice())
              ? BigDecimal.ZERO
              : detail.getSettlementPrice();
      sPrice = sPrice.add(ssPrice);

      //            BigDecimal c1 = Objects.isNull(detail.getCurrentMonthRefundQuantity()) ?
      // BigDecimal.ZERO : new BigDecimal(detail.getCurrentMonthRefundQuantity());
      //            BigDecimal c2 = Objects.isNull(detail.getCrossMonthRefundQuantity()) ?
      // BigDecimal.ZERO : new BigDecimal(detail.getCrossMonthRefundQuantity());

      //            BigDecimal subtract = tempQuantity.subtract(c1).subtract(c2);
      //            tempQuantityTotal = tempQuantityTotal.add(tempQuantity);
      //            BigDecimal multiply = tempPrice.multiply(subtract);
      //            tempAmountTotal = tempAmountTotal.add(multiply);
    }
    //        map.put(1, tempQuantityTotal.subtract(totalSettlementQuantity));
    map.put(2, tempAmountTotal.subtract(totalSettlementAmount));
    map.put(3, tPrice.subtract(sPrice));
    return map;
  }

  public void handlerGoodsType(InputStream inputStream, GoodsType goodsType) {
    List<SkuExcelDto> sheetRowsRaw =
        EasyExcel.read(inputStream).headRowNumber(1).head(SkuExcelDto.class).sheet(0).doReadSync();
    List<SkuExcelDto> sheetRows = sheetRowsRaw.stream().distinct().collect(toList());
    log.info("SKU货品标签总计{}行，去重后{}行", sheetRowsRaw.size(), sheetRows.size());

    for (SkuExcelDto skuExcelDto : sheetRowsRaw) {
      String code = skuExcelDto.getSkuCode();
      ItemSku itemSku = itemSkuGateway.getBySkuCode(code);
      if (Objects.isNull(itemSku)) {
        continue;
      }
      itemSku.setGoodsType(goodsType);
      iItemSkuService.updateById(itemSku);

      Long itemId = itemSku.getItemId();
      try {
        itemSyncWdtBizService.syncItemToWdt(itemId);
        itemSyncBanniuBizService.syncOneByItemId(itemId);
        TimeUnit.SECONDS.sleep(5);
      } catch (Exception e) {
        log.error("同步异常.itemId:{}", itemId, e);
      }
    }
  }

  public void exportAfterSales(InputStream inputStream) {
    List<OrderNoDto> orderNos =
        EasyExcel.read(inputStream).sheet(0).head(OrderNoDto.class).doReadSync();
    Long duration = 1706716800L;
    //        File file = new File(RandomUtil.randomString(6) + ".xlsx");
    //        com.alibaba.excel.ExcelWriter excelWriter = EasyExcelFactory.write(file).build();
    //        WriteSheet afterSalesSheet =
    // EasyExcel.writerSheet("客户售后明细差额汇总").head(AfterSalesRegisterPageVO.class).build();

    List<TmpAfterSalesSupplement> saveList = new LinkedList<>();
    for (OrderNoDto orderNo : orderNos) {
      try {
        String no = orderNo.getOrderNo().replaceAll(",", "").replaceAll("'", "");
        AfterSalesRegisterPageQuery pageQuery = new AfterSalesRegisterPageQuery();
        pageQuery.setSettleDurations(ListUtil.of(duration).toArray(new Long[] {}));
        pageQuery.setOrderNo(no);
        pageQuery.setPageSize(9);
        PageResponse<AfterSalesRegisterPageVO> response =
            afterSalesRegisterBizService.pageQuery(pageQuery);
        if (response.isSuccess()) {
          List<AfterSalesRegisterPageVO> data = response.getData();
          if (CollUtil.isNotEmpty(data)) {
            //                        excelWriter.write(data, afterSalesSheet);
            List<TmpAfterSalesSupplement> collect =
                data.stream()
                    .map(
                        val -> {
                          TmpAfterSalesSupplement tmpAfterSalesSupplement =
                              new TmpAfterSalesSupplement();
                          tmpAfterSalesSupplement.setOrderNumber(val.getOrderNo());
                          tmpAfterSalesSupplement.setSettlementPeriod(val.getSettleMonth());
                          //
                          // tmpAfterSalesSupplement.setCooperationMode(val.getBusinessLineStr());
                          tmpAfterSalesSupplement.setPaymentTime(val.getPayTime());
                          tmpAfterSalesSupplement.setStore(val.getShopName());
                          tmpAfterSalesSupplement.setProductCode(val.getItemCode());
                          tmpAfterSalesSupplement.setProductName(val.getItemName());
                          tmpAfterSalesSupplement.setProductSpecification(val.getSpecifications());
                          tmpAfterSalesSupplement.setAfterSalesQuantity(val.getNum());
                          tmpAfterSalesSupplement.setDeliveryWarehouse(val.getWarehouse());
                          tmpAfterSalesSupplement.setFactoryWarehouseBearsFreight(
                              val.getFactoryUndertakeFreight());
                          tmpAfterSalesSupplement.setFactoryWarehouseBearsPayment(
                              val.getFactoryUndertakeGoodsAmount());
                          tmpAfterSalesSupplement.setFactoryWarehouseBearsOtherCompensation(
                              val.getFactoryUndertakeOtherAmount());
                          tmpAfterSalesSupplement.setExperienceFundCoupon(
                              val.getExperienceFundCoupon());
                          tmpAfterSalesSupplement.setExperienceFundCash(
                              val.getExperienceFundCash());
                          tmpAfterSalesSupplement.setAfterSalesHandlingOpinion(
                              val.getHandleAdvice());
                          tmpAfterSalesSupplement.setExperienceFundBearingReasonOption1(
                              val.getExperienceFundReason1());
                          tmpAfterSalesSupplement.setExperienceFundBearingReasonOption2(
                              val.getExperienceFundReason2());
                          tmpAfterSalesSupplement.setExperienceFundBearingReasonOption3(
                              val.getExperienceFundReason3());
                          tmpAfterSalesSupplement.setProblemDescriptionOption1(val.getDesc1());
                          tmpAfterSalesSupplement.setProblemDescriptionOption2(val.getDesc2());
                          tmpAfterSalesSupplement.setProblemDescriptionOption3(val.getDesc3());
                          tmpAfterSalesSupplement.setRelatedImages(
                              StrUtil.join(StrUtil.COMMA, val.getImages()));
                          return tmpAfterSalesSupplement;
                        })
                    .collect(toList());
            saveList.addAll(collect);
          }
        }
      } catch (Exception e) {
        log.error("【客服售后明细】sheet导出异常.{}", orderNo, e);
      }
    }
    iTmpAfterSalesSupplementService.saveBatch(saveList);

    //        excelWriter.finish();
    //        FileStub fileStub = fileGateway.uploadFile(UploadFileAction.ofFile(file));
    //        com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.File sysFile =
    // iFileService.getById(fileStub.getFileId());
    //        String url = ossGateway.generatePresignedUrl(true, sysFile.getPath());
    //        log.info("2月份补充差异订单:{}", url);
  }

  public void updateSkuUnit0(String skuCode, String unit) {
    iItemSkuService
        .lambdaUpdate()
        .set(ItemSku::getUnit, unit)
        .eq(ItemSku::getSkuCode, skuCode)
        .update();
  }

  @Resource StaffService staffService;

  @Transactional(rollbackFor = Exception.class)
  public void demo() {
    iItemService.getById(11);
    subDemo();
  }

  private void subDemo() {

    Optional<StaffBrief> xixi = staffService.getStaffByLoginName("xixi");
    boolean present = xixi.isPresent();
    System.out.println(present);
  }

  @Resource IPaymentApplyOrderService iPaymentApplyOrderService;

  @Resource OperateLogGateway operateLogGateway;

  @Resource PaymentOrderBizService paymentOrderBizService;

  public void fixPaymentOrderSyncKingDee() {
    PaymentPageQuery query = new PaymentPageQuery();
    query.setFinishPayStartTime(1717171200L);
    query.setFinishPayEndTime(1719763200L);
    PageResponse<PaymentPageVo> page = paymentOrderBizService.page(query);
    List<PaymentPageVo> data = page.getData();
    List<Long> ids = data.stream().map(PaymentPageVo::getId).collect(toList());

    for (Long id : ids) {
      List<OperateLog> logs =
          iOperateLogService
              .lambdaQuery()
              .eq(OperateLog::getTargetId, id)
              .eq(OperateLog::getTargetType, OperateLogTarget.PAYMENT_APPLY_ORDER)
              .orderByDesc(OperateLog::getId)
              .last("limit")
              .list();
      if (CollUtil.isNotEmpty(logs) && logs.get(0).getMsg().contains("OA状态回传：付款成功")) {
        Response sync = paymentOrderBizService.sync(id);
        log.info("fixPaymentOrderSyncKingDee no:{},res:{}", id, sync.isSuccess());
      }
    }
  }

  private Response syncKingDee(Long id) {
    return kingDeeTemplate.handler(ApiEnum.SAVE_PAY_BILL, id, null);
  }

  @Resource IKdyCallbackService kdyCallbackService;

  @Resource AfterSaleLogisticsListener afterSaleLogisticsListener;

  public void kdyCallback() {
    int pageSize = 1000;
    Integer count = kdyCallbackService.lambdaQuery().count();
    int pageIndex = (count % pageSize == 0) ? count / pageSize : (count / pageSize + 1);

    for (int i = 0; i < pageIndex; i++) {
      int offset = i * pageSize;
      List<KdyCallback> list =
          kdyCallbackService.lambdaQuery().last("limit " + offset + "," + pageSize).list();
      for (KdyCallback callback : list) {
        KdyCallbackEvent event = KdyCallbackEvent.of(callback);
        afterSaleLogisticsListener.onKdyCallback(event);
      }
      log.info("fix kdyCallback pageIndex:{}", i);
    }
  }

  @Resource IItemLaunchPlanService iItemLaunchPlanService;

  @Resource IItemDrawerService itemDrawerService;

  @Resource IItemDrawerModuleAuditService iItemDrawerModuleAuditService;

  @Resource IItemLaunchStatsService iItemLaunchStatsService;

  @Resource IItemDrawerLiveVerbalService iItemDrawerLiveVerbalService;

  @Resource IItemDrawerModuleAuditStatsService iItemDrawerModuleAuditStatsService;

  @Resource IPlatformItemService iPlatformItemService;

  @Resource IItemDrawerModuleAuditTaskService iItemDrawerModuleAuditTaskService;

  @Resource ArkSailorItemFeignClient arkSailorItemFeignClient;

  public void pullNewGoodsProcess() {
    List<Long> planIds =
        iItemLaunchPlanService
            .lambdaQuery()
            .ge(ItemLaunchPlan::getCreatedAt, 1704038400L)
            .list()
            .stream()
            .map(ItemLaunchPlan::getId)
            .collect(toList());
    List<Long> itemIds =
        iItemLaunchPlanItemRefService
            .lambdaQuery()
            .in(ItemLaunchPlanItemRef::getPlanId, planIds)
            .list()
            .stream()
            .map(ItemLaunchPlanItemRef::getItemId)
            .collect(toList());
    // 815 itemId
    // 商品编码、采购品名、商品状态、商品资料法务审核时间（最新审核节点）、商品资料QC审核时间（最新审核节点）、直播话术状态、直播话术法务审核时间（最新审核节点）、
    // 直播话术QC审核时间（最新审核节点），是否上架（参照小程序）、上架时间（小程序上架时间）
    List<NewGoodsPullSheet> sheetList = new LinkedList<>();
    int count = 0;

    for (Long itemId : itemIds) {
      try {
        NewGoodsPullSheet sheet = new NewGoodsPullSheet();

        Item item = iItemService.getById(itemId);
        if (Objects.isNull(item)) {
          continue;
        }
        ItemLaunchStats itemLaunchStats = null;
        PlatformItem platformItem = null;
        NewGoodsGroupVO newGoodsGroupVO = null;

        List<ItemLaunchStats> itemLaunchStatsList =
            iItemLaunchStatsService
                .lambdaQuery()
                .eq(ItemLaunchStats::getItemId, itemId)
                .orderByDesc(ItemLaunchStats::getId)
                .list();
        if (CollUtil.isNotEmpty(itemLaunchStatsList)) {
          itemLaunchStats = itemLaunchStatsList.get(0);
        }
        List<ItemDrawerModuleAuditTask> itemDrawerModuleAuditTaskList =
            iItemDrawerModuleAuditTaskService
                .lambdaQuery()
                .eq(ItemDrawerModuleAuditTask::getItemId, itemId)
                .eq(ItemDrawerModuleAuditTask::getType, 2)
                .in(ItemDrawerModuleAuditTask::getNode, ListUtil.of("QC", "LEGAL"))
                .eq(ItemDrawerModuleAuditTask::getAuditStatus, ItemDrawerAuditTaskState.DONE)
                .orderByDesc(ItemDrawerModuleAuditTask::getId)
                .list();
        List<PlatformItem> platformItemList =
            iPlatformItemService
                .lambdaQuery()
                .eq(PlatformItem::getItemId, itemId)
                .eq(PlatformItem::getPlatform, Platform.LAOBASHOP)
                .orderByDesc(PlatformItem::getId)
                .list();
        if (CollUtil.isNotEmpty(platformItemList)) {
          platformItem = platformItemList.get(0);
        }
        NewGoodsQueryPage newGoodsQueryPage = new NewGoodsQueryPage();
        newGoodsQueryPage.setItemCode(item.getCode());
        newGoodsQueryPage.setBusinessLine(ListUtil.of(0, 1, 2, 3));
        newGoodsQueryPage.setShowAll(true);
        newGoodsQueryPage.setUserId(0L);
        newGoodsQueryPage.setHasPricePerm(true);
        PageResponse<NewGoodsGroupVO> newGoodsGroupVOPageResponse =
            newGoodsBizService.queryPageGroupBySpu(newGoodsQueryPage);
        List<NewGoodsGroupVO> newGoodsGroupVOPageResponseData =
            newGoodsGroupVOPageResponse.getData();
        if (CollUtil.isNotEmpty(newGoodsGroupVOPageResponseData)) {
          newGoodsGroupVO = newGoodsGroupVOPageResponseData.get(0);
        }

        sheet.setItemCode(item.getCode());
        sheet.setItemName(item.getName());
        if (Objects.isNull(newGoodsGroupVO)) {
          sheet.setItemStatus("");
          sheet.setLiveVerbalStatus("");
          log.info("itemId pull data newGoods null.itemCode:{}", item.getCode());
        } else {
          Integer status = newGoodsGroupVO.getSpu().getStatus();
          sheet.setItemStatus(status + "|" + itemStatus(status));
          if (CollUtil.isNotEmpty(newGoodsGroupVO.getSpu().getLiveVerbalTrickVos())) {
            String liveVerbalTrick =
                newGoodsGroupVO.getSpu().getLiveVerbalTrickVos().stream()
                    .map(
                        val ->
                            StrUtil.format(
                                "话术名称:{},状态:{}",
                                val.getName(),
                                liveStatus(val.getLiveVerbalTrickStatus())))
                    .collect(Collectors.joining("。"));
            sheet.setLiveVerbalStatus(liveVerbalTrick);
          } else {
            Integer liveVerbalTrickStatus = newGoodsGroupVO.getSpu().getLiveVerbalTrickStatus();
            sheet.setLiveVerbalStatus(liveStatus(liveVerbalTrickStatus));
          }
        }
        sheet.setLegalAuditTime(
            Objects.nonNull(itemLaunchStats)
                ? (itemLaunchStats.getToBeLegalAuditEndTime() > 0
                    ? DateUtil.parseTimeStamp(
                        itemLaunchStats.getToBeLegalAuditEndTime(), DateUtil.DEFAULT_FORMAT)
                    : StrUtil.EMPTY)
                : StrUtil.EMPTY);
        sheet.setQcAuditTime(
            Objects.nonNull(itemLaunchStats)
                ? (itemLaunchStats.getToBeQcAuditEndTime() > 0
                    ? DateUtil.parseTimeStamp(
                        itemLaunchStats.getToBeQcAuditEndTime(), DateUtil.DEFAULT_FORMAT)
                    : StrUtil.EMPTY)
                : StrUtil.EMPTY);
        if (CollUtil.isNotEmpty(itemDrawerModuleAuditTaskList)) {
          Optional<ItemDrawerModuleAuditTask> legal =
              itemDrawerModuleAuditTaskList.stream()
                  .filter(val -> val.getNode().equals("LEGAL"))
                  .findFirst();
          if (legal.isPresent()) {
            sheet.setLiveVerbalLegalAuditTime(
                DateUtil.parseTimeStamp(legal.get().getAuditAt(), DateUtil.DEFAULT_FORMAT));
          } else {
            sheet.setLiveVerbalLegalAuditTime("");
          }
          Optional<ItemDrawerModuleAuditTask> qc =
              itemDrawerModuleAuditTaskList.stream()
                  .filter(val -> val.getNode().equals("QC"))
                  .findFirst();
          if (qc.isPresent()) {
            sheet.setLiveVerbalQcAuditTime(
                DateUtil.parseTimeStamp(qc.get().getAuditAt(), DateUtil.DEFAULT_FORMAT));
          } else {
            sheet.setLiveVerbalQcAuditTime("");
          }
        }
        if (Objects.nonNull(platformItem)
            && org.apache.commons.lang3.StringUtils.isNumeric(platformItem.getOuterItemId())) {
          ItemPageQuery itemPageQuery = new ItemPageQuery();
          itemPageQuery.setItemId(Long.valueOf(platformItem.getOuterItemId()));
          Result<Page<ItemListVO>> pageResult =
              arkSailorItemFeignClient.itemPageQuery(itemPageQuery);
          List<ItemListVO> records = pageResult.getData().getRecords();
          if (CollUtil.isNotEmpty(records)) {
            String shelfStatus = getArkItemStatus(records.get(0));
            Long shelfTime = records.get(0).getShelfTime();
            sheet.setInShelf(shelfStatus);
            sheet.setShelfTime(DateUtil.parseTimeStamp(shelfTime, DateUtil.DEFAULT_FORMAT));
          }
        }

        sheetList.add(sheet);
        count++;
        log.info("itemId pull data running.count:{}", count);
      } catch (Exception e) {
        log.error("itemId pull data fail.itemId:{}", itemId, e);
        throw e;
      }
    }

    File excelFile = null;
    try {
      String fileName = "newGoods_" + DateUtil.currentTime() + ".xlsx";
      excelFile = new File(fileName);
      com.alibaba.excel.ExcelWriter excelWriter =
          EasyExcel.write(excelFile, NewGoodsPullSheet.class).build();
      WriteSheet writeSheet = EasyExcel.writerSheet(0).head(NewGoodsPullSheet.class).build();
      excelWriter.write(sheetList, writeSheet);
      excelWriter.finish();

      UploadFileAction action = UploadFileAction.ofFile(excelFile);
      FileStub fileStub = fileGateway.uploadFile(action);
      OssConfig ossConfig = SpringUtil.getBean(OssConfig.class);
      String url = fileStub.getUrl();
      String path = url.replaceAll(ossConfig.getPrivateUrl(), "");
      String resUrl = ossGateway.generatePresignedUrl(true, path);

      msgSender.sendMsgToSomeone("NewGoods:" + resUrl, ListUtil.of(GlobalConstant.SEVEN_UP));
    } catch (Exception e) {
      log.error("itemId pull data success.file upload fail", e);
    } finally {
      FileUtil.del(excelFile);
    }
  }

  public void exportSettlementOrder() {
    final OrderSettlementBizService orderSettlementBizService1 =
        SpringUtil.getBean(OrderSettlementBizService.class);
    SettlementOrderPageQuery pageQuery = new SettlementOrderPageQuery();
    pageQuery.setPayApplyStatus(PayApplyStatus.WAIT);
    pageQuery.setTimes(
        ListUtil.of(
            1714492800L,
            1711900800L,
            1704038400L,
            1706716800L,
            1709222400L,
            1701360000L,
            1698768000L));
    pageQuery.setPageSize(9999);
    pageQuery.setPageIndex(1);

    final PageResponse<SettlementOrderPageVo> settlementOrderPageVoPageResponse =
        orderSettlementBizService1.settlementPageQuery(pageQuery);

    if (CollUtil.isNotEmpty(settlementOrderPageVoPageResponse.getData())) {
      final Set<SettlementData> dataSet =
          settlementOrderPageVoPageResponse.getData().stream()
              .map(
                  val -> {
                    SettlementData data = new SettlementData();
                    data.setNo(val.getNo());
                    data.setProviderName(val.getProviderName());
                    data.setTime(val.getCycle());
                    return data;
                  })
              .collect(Collectors.toSet());

      File excelFile = null;
      try {
        String fileName = "settlement_" + DateUtil.currentTime() + ".xlsx";
        excelFile = new File(fileName);
        com.alibaba.excel.ExcelWriter excelWriter =
            EasyExcel.write(excelFile, SettlementData.class).build();
        WriteSheet writeSheet = EasyExcel.writerSheet(0).head(SettlementData.class).build();
        excelWriter.write(dataSet, writeSheet);
        excelWriter.finish();

        UploadFileAction action = UploadFileAction.ofFile(excelFile);
        FileStub fileStub = fileGateway.uploadFile(action);
        OssConfig ossConfig = SpringUtil.getBean(OssConfig.class);
        String url = fileStub.getUrl();
        String path = url.replaceAll(ossConfig.getPrivateUrl(), "");
        String resUrl = ossGateway.generatePresignedUrl(true, path);

        msgSender.sendMsgToSomeone("settlement_:" + resUrl, ListUtil.of(GlobalConstant.SEVEN_UP));
      } catch (Exception e) {
        log.error("itemId pull data success.file upload fail", e);
      } finally {
        FileUtil.del(excelFile);
      }
    }
  }

  private static String getArkItemStatus(ItemListVO item) {
    String status;
    if (Objects.equals(item.getAuditStatus(), ItemAuditStatusEnum.AUDITING.getValue())) {
      status = "审核中";
    } else if (Objects.equals(ItemShelfStatusEnum.ON_SHELF.getValue(), item.getShelfStatus())) {
      status = "已上架";
    } else if (Objects.equals(ItemShelfStatusEnum.OFF_SHELF.getValue(), item.getShelfStatus())) {
      status = "已下架";
    } else {
      status = "待上架";
    }
    return status;
  }

  /**
   * 1待选择 2待完善 3待设计 41待法务审核 42待QC审核 5待修改 6待上架 7已上架
   *
   * @param status
   * @return
   */
  private String itemStatus(Integer status) {
    if (status == 1) {
      return "待选择";
    } else if (status == 2) {
      return "待完善";
    } else if (status == 3) {
      return "待设计";
    } else if (status == 41) {
      return "待法务审核";
    } else if (status == 42) {
      return "待QC审核";
    } else if (status == 5) {
      return "待修改";
    } else if (status == 6) {
      return "待上架";
    } else if (status == 7) {
      return "已上架";
    }
    return "";
  }

  /**
   * 0:未进入审核流程/未知 5:待法务审核 10:待QC审核 100:审核完成
   *
   * @return
   */
  private String liveStatus(Integer status) {
    if (status == 0) {
      return "未进入审核流程/未知";
    } else if (status == 5) {
      return "待法务审核";
    } else if (status == 10) {
      return "待QC审核";
    } else if (status == 100) {
      return "审核完成";
    }
    return "";
  }

  public void invoiceExclude() {
    String orderNos =
        "20221226756623663426186313\n"
            + "20221227381149726342695623\n"
            + "20221227381392966342627780\n"
            + "20221230311224906342660800\n"
            + "20230110321780016342619530\n"
            + "20230110331116366342639510\n"
            + "20230208156641346342673259\n"
            + "20230216109114346342644848\n"
            + "20230301101468076342660211\n"
            + "20230301432044986342665567\n"
            + "20230301968755163426998944\n"
            + "20240614404213999443467872\n"
            + "20240810116709803645969190\n"
            + "2251725135544907199\n"
            + "2256132543300888080\n"
            + "3911090295123572039\n"
            + "3997688546732084734\n"
            + "4004239215010331039\n"
            + "20230314254502496342699539\n"
            + "20230317381460336342603652\n"
            + "20230321361254776342652263\n"
            + "20230404454900156342640043\n"
            + "20230405191333296342698014\n"
            + "20230414318508863426726912\n"
            + "20230607266877406342601890\n"
            + "20230522493035386342654389\n"
            + "20231002900413963426629409\n"
            + "20230425524374056342625270\n"
            + "20230527380488563426783163\n"
            + "20230831326131346342651934\n"
            + "20231025489536186342612262\n"
            + "20230709354076686342633832\n"
            + "20230905721029663426395762\n"
            + "20230418290963666342668882\n"
            + "20230526697342663426123494\n"
            + "20230702356416663426654901\n"
            + "20230915490242856342630182\n"
            + "20230506377624906342627714\n"
            + "20230911142934156342619464\n"
            + "20231005148750106342615215\n"
            + "20230816297141266342663321\n"
            + "20230728410428826342648564\n"
            + "20230524288165906342694798\n"
            + "20231017455129696342691958\n"
            + "20230831333122156342698731\n"
            + "20231002210291106342620786\n"
            + "20230601155218536342668749\n"
            + "20230629739862995709254753\n"
            + "20230911143569536342660553\n"
            + "20230618482109276342678146\n"
            + "20230831329832556342610009\n"
            + "20230425531838446342639854\n"
            + "20230627520026616342679315\n"
            + "20230629721074463426659150\n"
            + "20230806455758476342671798\n"
            + "20230829505936346342640376\n"
            + "20230823437118886342651218\n"
            + "20230417179623056342643704\n"
            + "20230603478360756342677563\n"
            + "20230921299729656342663623\n"
            + "4004428212948226844\n"
            + "20230417870453763426277647\n"
            + "20230526636827163426262331\n"
            + "20230913312953706342683207\n"
            + "20231115361321976342650701\n"
            + "20231208414046406342632342\n"
            + "20231110532339546342631509\n"
            + "20231103458160636342676270\n"
            + "20231212311480036342618496\n"
            + "20231114272629226342632113\n"
            + "20231214990368463426226244\n"
            + "20240724260630919025603737\n"
            + "20240115493628606342643715\n"
            + "20240111234832926342667702\n"
            + "20231115366801206342626791\n"
            + "2241263571639649892\n"
            + "20231119533925706342616267\n"
            + "20231209353839826342675568\n"
            + "20231129248257096342603726\n"
            + "20231207324038166342621256\n"
            + "20231119367925196342651308\n"
            + "20231108531218066342629544\n"
            + "2261786198161002653\n"
            + "3983107788147206209\n"
            + "20240816294039619723129266\n"
            + "3850094307793595926\n"
            + "3993448178357848142\n"
            + "20240813442067179365879091\n"
            + "3078873255122814633\n"
            + "3083951405754814633\n"
            + "3430544690045814633\n"
            + "3497755248065814633\n"
            + "3513517452030814633\n"
            + "3618478182264814633\n"
            + "3714290175026814633\n"
            + "3803583277231814633\n"
            + "3803745674976814633\n"
            + "3848641274921814633\n"
            + "3905290983834814633\n"
            + "3913626379790814633\n"
            + "3933790920003814633\n"
            + "20240804263482841373907827\n"
            + "4007429856986603207\n"
            + "3905125668377988942\n"
            + "3954291051363988942\n"
            + "2196667886483965851\n"
            + "20240730485600309122509978\n"
            + "20240731526212119122584482\n"
            + "20240801514392299122585533\n"
            + "20240802563844219122528297\n"
            + "20240806529520969122574940\n"
            + "20240808140458029122537698\n"
            + "20240813476460251288867453\n"
            + "20240813385466409872923686\n"
            + "2240977549025716561\n"
            + "20240816194441601249905678\n"
            + "20240709373854584666136175\n"
            + "20240806509720634666136301\n"
            + "4004018139367047939\n"
            + "20240808107734181648394715\n"
            + "4003654143149220237\n"
            + "2244095690377539364\n"
            + "3902720006503906242\n"
            + "3808124497009192407\n"
            + "2253133454487377074\n"
            + "20220518487061423809966388\n"
            + "20220817179680953809966967\n"
            + "20230310205479383809966327\n"
            + "20230416184075343809966099\n"
            + "20230805185189043809966698\n"
            + "20231025392455183809966139\n"
            + "20231206162377113809966978\n"
            + "20231228272919163809966440\n"
            + "20240123485742923809966218\n"
            + "20240311421467543809966047\n"
            + "20240519897088238099661217\n"
            + "20240620320968513809966770\n"
            + "20240701339517438099667858\n"
            + "20240712287067538099665348\n"
            + "20240722479255523809966243\n"
            + "20240724262448237593232259\n"
            + "20240808730732675932321685\n"
            + "3987915087350809115\n"
            + "3999629808056809115\n"
            + "4001781456899451337\n"
            + "3994298066260110937\n"
            + "2250451058376629777\n"
            + "2191066323128314350\n"
            + "2194775472260314350\n"
            + "20240715888876562333446555\n"
            + "3968257069481580906\n"
            + "2231409973025570772\n"
            + "20240710169239492905895435\n"
            + "20240723254034702905895902\n"
            + "20240724220532962905895133\n"
            + "20240728399353732905895598\n"
            + "2240960918710628266\n"
            + "4000787533052717624\n"
            + "2255254323320151578\n"
            + "20240812119417914768979124\n"
            + "20221212536436676342669335\n"
            + "20221124109292776342684267\n"
            + "20221113439282616342660331\n"
            + "20221110490694666342696813\n"
            + "20221110481955116342620426\n"
            + "20221110432050326342659946\n"
            + "20221108288194096342639954\n"
            + "20221026186519356342624750\n"
            + "20221021951706463426063355\n"
            + "20221020201824766342678737\n"
            + "20221017221189596342662323\n"
            + "20221011399402876342653251\n"
            + "2246580986805634559\n"
            + "20240802760881612888673026\n"
            + "20240801269164819815456233\n"
            + "20240721100060282396343946\n"
            + "20240711466367872396343649\n"
            + "20240709360503722396343686\n"
            + "20240709360301302396343549\n"
            + "20240709295067152396343369\n"
            + "20240705279040172396343729\n"
            + "20240702321156852396343279\n"
            + "20240702320320082396343249\n"
            + "20240627840492092396343107\n"
            + "20240620340035402396343824\n"
            + "20240620339497222396343537\n"
            + "20240613110751132396343662\n"
            + "20240609175831172396343165\n"
            + "20240608320979323963431365\n"
            + "20240605309124382396343115\n"
            + "20240603265889042396343842\n"
            + "20240528857583382396343664\n"
            + "20240527861709602396343161\n"
            + "20240527135457382396343938\n"
            + "20240523141452462396343017\n"
            + "20240522124019392396343967\n"
            + "20240521214071742396343115\n"
            + "20240517828758962396343110\n"
            + "20240517827180392396343260\n"
            + "20240515122288552396343648\n"
            + "20240513353200082396343486\n"
            + "20240513110111692396343055\n"
            + "20240511896296223963434402\n"
            + "20240510119212212396343283\n"
            + "20240507430507012396343825\n"
            + "20240505166514942396343862\n"
            + "20240505165859982396343168\n"
            + "20240429538118212396343949\n"
            + "20240429306309372396343074\n"
            + "20240426841066122396343454\n"
            + "20240413450345532396343309\n"
            + "20240412398496642396343321\n"
            + "20240204533673722396343567\n"
            + "20240203535293562396343529\n"
            + "20240202291210072396343492\n"
            + "20240202276861972396343362\n"
            + "20240118396742622396343846\n"
            + "20240117543200132396343370\n"
            + "20240117397018223963435861\n"
            + "20240116213094452396343200\n"
            + "20240116212697762396343697\n"
            + "20240111109566752396343258\n"
            + "20240104909860323963435467\n"
            + "20240104861688723963435368\n"
            + "3883865401678947027\n"
            + "3836611442043312918\n"
            + "20240511219241239717462241\n"
            + "20220519247986166342637835\n"
            + "20220825720822163426606510\n"
            + "20220915131822356342653460\n"
            + "20220823494871996342658687\n"
            + "20220821340795246342669657\n"
            + "20220617255003346342654606\n"
            + "20220712500919756342632835\n"
            + "20220915287807626342631179\n"
            + "20220726515888206342636111\n"
            + "20220920484245726342633177\n"
            + "20220724481836716342631655\n"
            + "20220915286098576342643602\n"
            + "20220531495867136342677583\n"
            + "20220616207468016342668889\n"
            + "20220616853648063426729524\n"
            + "20220821344957286342635004\n"
            + "20220603258808776342633195\n"
            + "2219168930237188698\n"
            + "20220823509743936342611491\n"
            + "20220516506986936342638878\n"
            + "2253419258282064890\n"
            + "2252423424582305566\n"
            + "2252820254097305566\n"
            + "6933016637560264664\n"
            + "6918935052134716870\n"
            + "6925324397516428515\n"
            + "6919423014186980579\n"
            + "5019360239695801571\n"
            + "4997699086080447718\n"
            + "6933282086450042024\n"
            + "1876954322983814633\n"
            + "1837323289303814633\n"
            + "1837414082696814633\n"
            + "1741242134987814633\n"
            + "1613997468589814633\n"
            + "1526824154990814633\n"
            + "1524034047565814633\n"
            + "1436817853233814633\n"
            + "3376756262295814633\n"
            + "3304964415039814633\n"
            + "3281349674672814633\n"
            + "3083448196160814633\n"
            + "3083329180463814633\n"
            + "1506299261869814633\n"
            + "1506293143223814633\n"
            + "2357275538374814633\n"
            + "2327914837536814633\n"
            + "2239598161717814633\n"
            + "2214287174829814633\n"
            + "3634120728284814633\n"
            + "3618032176087814633\n"
            + "3510711505865814633\n"
            + "3510706465353814633\n"
            + "3468000241929814633\n"
            + "3467392272993814633\n"
            + "2333900034714610\n"
            + "2321100122873610\n"
            + "240808-411985548933676\n"
            + "240804-617988759173676\n"
            + "240813-274606364311764";
    List<String> orderNoList = Arrays.asList(orderNos.split("\n"));

    final List<NuoNuoInvoiceProcessRecord> recordList =
        orderNoList.stream()
            .map(
                val -> {
                  NuoNuoInvoiceProcessRecord record = new NuoNuoInvoiceProcessRecord();
                  record.setOrderNo(val);
                  record.setStatus(1);
                  record.setRecord("过滤订单");
                  return record;
                })
            .collect(toList());

    SpringUtil.getBean(INuoNuoInvoiceProcessRecordService.class).saveBatch(recordList);
  }

  public void exportLogistic() {
    AfterSaleLogisticsPageQuery query = new AfterSaleLogisticsPageQuery();
    query.setPayTimeStart(1727712000L);
    List<LogisticsSheet> sheetList = new LinkedList<>();

    for (int i = 1; i < 100; i++) {
      query.setPageIndex(i);
      query.setPageSize(1000);

      final List<AbnormalityListObj> abnormalityListObjs =
          abnormalityLogService.getDaddyBaseMapper().selectAbnormalityList(query);
      if (CollUtil.isEmpty(abnormalityListObjs)) {
        break;
      }

      Map<Long, List<WdtOrderDetail>> orderDetailsGroup = new HashMap<>();
      final List<Long> traceIds =
          abnormalityListObjs.stream()
              .map(AbnormalityListObj::getWdtTradeId)
              .collect(Collectors.toList());
      if (!traceIds.isEmpty()) {
        final List<WdtOrderDetail> allOrderDetails =
            wdtOrderDetailService.lambdaQuery().in(WdtOrderDetail::getTradeId, traceIds).list();
        orderDetailsGroup =
            allOrderDetails.stream().collect(Collectors.groupingBy(WdtOrderDetail::getTradeId));
      }

      final List<Long> abnormalityIds =
          abnormalityListObjs.stream()
              .map(AbnormalityListObj::getAbnormalityId)
              .collect(Collectors.toList());
      final List<OrderLogisticsAbnormalityLog> abnormalityLogs =
          abnormalityLogService
              .lambdaQuery()
              .in(OrderLogisticsAbnormalityLog::getAbnormalityId, abnormalityIds)
              .list();
      final Map<Long, List<OrderLogisticsAbnormalityLog>> logGroup =
          abnormalityLogs.stream()
              .collect(Collectors.groupingBy(OrderLogisticsAbnormalityLog::getAbnormalityId));

      for (AbnormalityListObj datum : abnormalityListObjs) {
        LogisticsSheet sheet = new LogisticsSheet();
        sheet.setLogisticNo(datum.getLogisticsNo());
        sheet.setLogisticCompany(datum.getLogisticsCompanyName());
        sheet.setConsignTime(datum.getConsignTime());
        sheet.setLogisticStatus(getStatus(datum.getLogisticsStatus()));
        sheet.setExceptionStatus(getExceptionStatus(datum.getAbnormalStatus()));
        sheet.setOrderNo(datum.getSrcOrderNo());
        sheet.setOrderStatus(getOrderStatus(datum.getTradeStatus()));

        final List<WdtOrderDetail> wdtOrderDetails =
            orderDetailsGroup.getOrDefault(datum.getWdtTradeId(), ListUtil.empty());
        String collect =
            wdtOrderDetails.stream()
                .map(
                    val ->
                        StrUtil.format(
                            "商品名称:{},规格:{},数量:{}",
                            val.getGoodsName(),
                            val.getSpecName(),
                            val.getNum()))
                .collect(Collectors.joining("。"));
        sheet.setItemInfo(collect);

        final List<OrderLogisticsAbnormalityLog> orderLogisticsAbnormalityLogs =
            logGroup.getOrDefault(datum.getAbnormalityId(), Collections.emptyList());
        if (CollUtil.isNotEmpty(orderLogisticsAbnormalityLogs)) {
          sheet.setExceptionType(orderLogisticsAbnormalityLogs.get(0).getMsg());
          final StaffBrief staffBrief =
              StaffAssembler.INST.toStaffBrief(
                  orderLogisticsAbnormalityLogs.get(0).getCreatedUid());
          sheet.setOperator(staffBrief.getNickname());
          sheet.setOperateTime(
              DateUtil.formatDate(orderLogisticsAbnormalityLogs.get(0).getCreatedAt()));

          final long count =
              orderLogisticsAbnormalityLogs.stream()
                  .filter(val -> val.getCreatedUid() != 0L)
                  .count();
          sheet.setHandlerNum(count);
        }
        sheetList.add(sheet);
      }

      log.info("logistic_export finish sheetList size:{}", sheetList.size());
    }

    String fileName = "logistics_" + DateUtil.currentTime() + ".xlsx";
    File excelFile = new File(fileName);
    com.alibaba.excel.ExcelWriter excelWriter =
        EasyExcel.write(excelFile, LogisticsSheet.class).build();
    WriteSheet writeSheet = EasyExcel.writerSheet(0).head(LogisticsSheet.class).build();
    excelWriter.write(sheetList, writeSheet);
    excelWriter.finish();

    log.info("logistic_export excel finish ");

    UploadFileAction action = UploadFileAction.ofFile(excelFile);
    FileStub fileStub = fileGateway.uploadFile(action);
    OssConfig ossConfig = SpringUtil.getBean(OssConfig.class);
    String url = fileStub.getUrl();
    String path = url.replaceAll(ossConfig.getPrivateUrl(), "");
    String resUrl = ossGateway.generatePresignedUrl(true, path);

    log.info("logistic_export excel finish ");

    msgSender.sendMsgToSomeone("Logistic:" + resUrl, ListUtil.of(GlobalConstant.SEVEN_UP));
  }

  public String getOrderStatus(int statusCode) {
    switch (statusCode) {
      case 4:
        return "线下退款";
      case 5:
        return "已取消";
      case 6:
        return "待确认订单, 导入放入这个状态 待转预订单(待审核)";
      case 7:
        return "待确认订单, 导入时先放到这个状态（此状态不占用库存,可删除,离开这个状态就不能删除了)";
      case 10:
        return "待付款";
      case 12:
        return "待尾款";
      case 15:
        return "等未付";
      case 16:
        return "延时审核";
      case 19:
        return "预订单前处理";
      case 20:
        return "前处理(赠品，合并，拆分)";
      case 21:
        return "委外前处理";
      case 23:
        return "异常预订单";
      case 24:
        return "换货预订单";
      case 25:
        return "待处理预订单";
      case 26:
        return "待激活预订单";
      case 27:
        return "待分配预订单";
      case 30:
        return "待客审";
      case 35:
        return "待财审";
      case 55:
        return "已审核";
      case 95:
        return "已发货";
      case 96:
        return "成本确认（待录入计划成本，订单结算时有货品无计划成本）";
      case 101:
        return "已过账";
      case 110:
        return "已完成";
      default:
        return "未知状态";
    }
  }

  public String getExceptionStatus(Integer status) {
    /** 0:已关闭 1:进行中 2:已处理 */
    switch (status) {
      case 0:
        return "已关闭";
      case 1:
        return "进行中";
      case 2:
        return "已处理";
      default:
        return "unknown";
    }
  }

  /**
   * 快递状态 0 待发货，1 已发货，2 已揽收，3 运输中，4 派送中，5 待取件，6 未签收，7 代签收，8 已签收
   *
   * @param status
   * @return
   */
  private String getStatus(Integer status) {
    switch (status) {
      case 0:
        return "待发货";
      case 1:
        return "已发货";
      case 2:
        return "已揽收";
      case 3:
        return "运输中";
      case 4:
        return "派送中";
      case 5:
        return "待取件";
      case 6:
        return "未签收";
      case 7:
        return "代签收";
      case 8:
        return "已签收";
      default:
        return "unknown";
    }
  }

  public void wdtOrderLogistics(String s, String e) {
    final List<WdtOrderLogisticDto> wdtOrderLogisticDtos =
        wdtOrderMapper.queryWdtOrderLogisticSheet(s, e);
    log.info("logistic_export data size:{}", wdtOrderLogisticDtos.size());
    for (WdtOrderLogisticDto val : wdtOrderLogisticDtos) {
      try {
        val.setTradeStatusStr(getOrderStatus(val.getTradeStatus()));

        final String tracklist = val.getTracklist();
        final KdyTrackList kdyTrackList = JsonUtil.parse(tracklist, KdyTrackList.class);
        if (Objects.nonNull(kdyTrackList)) {
          final Optional<KdyTrackItem> max =
              kdyTrackList.getTrackList().stream()
                  .max(Comparator.comparing(KdyTrackItem::getTrackDate));
          if (max.isPresent()) {
            val.setTracklistStr(max.get().toString());
          } else {
            val.setTracklistStr(tracklist);
          }
        } else {
          val.setTracklistStr(tracklist);
        }
      } catch (Exception exception) {
        log.error("logistic_export error", exception);
        break;
      }
    }

    String fileName = "logistics_" + DateUtil.currentTime() + ".xlsx";
    File excelFile = new File(fileName);
    com.alibaba.excel.ExcelWriter excelWriter =
        EasyExcel.write(excelFile, WdtOrderLogisticDto.class).build();
    WriteSheet writeSheet = EasyExcel.writerSheet(0).head(WdtOrderLogisticDto.class).build();
    excelWriter.write(wdtOrderLogisticDtos, writeSheet);
    excelWriter.finish();

    log.info("logistic_export excel finish ");

    UploadFileAction action = UploadFileAction.ofFile(excelFile);
    FileStub fileStub = fileGateway.uploadFile(action);
    OssConfig ossConfig = SpringUtil.getBean(OssConfig.class);
    String url = fileStub.getUrl();
    String path = url.replaceAll(ossConfig.getPrivateUrl(), "");
    String resUrl = ossGateway.generatePresignedUrl(true, path);

    log.info("logistic_export excel finish ");

    msgSender.sendMsgToSomeone("Logistic:" + resUrl, ListUtil.of(GlobalConstant.SEVEN_UP));
  }

  /** 销售出库单 - 临时保存脚本 */
  public void saveSaleOutStock(InputStream inputStream) {

    List<SaleOutStockDto> sheetRowsRaw =
        EasyExcel.read(inputStream)
            .headRowNumber(1)
            .head(SaleOutStockDto.class)
            .sheet(0)
            .doReadSync();
    List<SaleOutStockDto> sheetRows = sheetRowsRaw.stream().distinct().collect(toList());

    Long auditDate = 1730433600L;

    final Map<String, List<SaleOutStockDto>> collect =
        sheetRows.stream().collect(Collectors.groupingBy(SaleOutStockDto::getWarehouseNo));

    collect.forEach(
        (wNo, list) -> {
          String orderNo = "LS-XSCK" + RandomUtil.randomNumbers(6);

          Map<String, Integer> map = new HashMap<>(128);
          for (SaleOutStockDto saleOutStockDto : list) {
            map.put(saleOutStockDto.getSkuCode(), Math.abs(saleOutStockDto.getCount()));
          }
          try {
            final String s = saveSaleOutStock(map, auditDate, orderNo, wNo);
            final KingDeeResp kingDeeResp = reqTemplate.saveWithRes(s);
            log.info("saveSaleOutStock wn:{},res:{}", wNo, JsonUtil.toJson(kingDeeResp));
            TimeUnit.SECONDS.sleep(2);
          } catch (Exception e) {
            log.error("saveSaleOutStock fail.", e);
          }
        });
  }

  private String saveSaleOutStock(
      Map<String, Integer> paramMap, Long auditDate, String orderNo, String warehouseNo) {

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", SAL_OUTSTOCK.getFormId());
    Map<String, Object> dataMap = new LinkedHashMap<>(8);

    Map<String, Object> modelMap = new LinkedHashMap<>(16);

    String customerId = orderNo.startsWith("LS") ? "K0002" : "K0005";

    modelMap.put("FID", 0);
    modelMap.put("FBillTypeID", Dict.create().set("FNUMBER", "XSCKD01_SYS"));
    modelMap.put("FDate", DateUtil.format(auditDate, "yyyy-MM-dd") + " 00:00:00");
    modelMap.put("FSaleOrgId", Dict.create().set("FNumber", "103"));
    modelMap.put("FCustomerID", Dict.create().set("FNumber", customerId));
    modelMap.put("FReceiverID", Dict.create().set("FNumber", customerId));
    modelMap.put("FStockOrgId", Dict.create().set("FNumber", "103"));
    modelMap.put("FSettleID", Dict.create().set("FNumber", customerId));
    modelMap.put("FPayerID", Dict.create().set("FNumber", customerId));
    modelMap.put("FOwnerTypeIdHead", "BD_OwnerOrg");
    modelMap.put("FCDateOffsetValue", 0);
    modelMap.put("FIsTotalServiceOrCost", false);

    Dict orgDict = Dict.create().set("FNumber", "103");

    List<Map<String, Object>> entityList = new LinkedList<>();
    paramMap.forEach(
        (skuCode, count) -> {
          Map<String, Object> entityMap = new HashMap<>(16);
          entityMap.put("FRowType", "Standard");
          ItemSku itemSku = itemSkuGateway.getBySkuCode(skuCode);

          entityMap.put("FMaterialID", Dict.create().set("FNumber", skuCode));

          String unit = "ge";
          if (Objects.nonNull(itemSku)) {
            unit = iBaseUnitService.getKingDeeNum(itemSku.getUnit());
          }

          entityMap.put("FUnitID", Dict.create().set("FNumber", unit));
          // 实发数量
          entityMap.put("FRealQty", count.floatValue());
          // 是否赠品
          boolean isGift = false;
          if (Objects.nonNull(itemSku)) {
            Long itemId = itemSku.getItemId();
            List<ItemProcurement> list =
                iItemProcurementService.lambdaQuery().eq(ItemProcurement::getItemId, itemId).list();
            if (CollUtil.isNotEmpty(list)) {
              ItemProcurement itemProcurement = list.get(0);
              isGift =
                  Objects.nonNull(itemProcurement.getIsGift()) && itemProcurement.getIsGift() == 1;
            }
          }

          entityMap.put("FIsFree", isGift);
          // 货主类型
          entityMap.put("FOwnerTypeID", "BD_OwnerOrg");
          // 货主id
          entityMap.put("FOwnerID", orgDict);
          // 仓库
          entityMap.put("FStockID", Dict.create().set("FNumber", warehouseNo));

          entityList.add(entityMap);
        });
    modelMap.put("FEntity", entityList);
    if (CollUtil.isEmpty(entityList)) {
      throw ExceptionFactory.bizException(
          ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), "销售出库单请求参数实体列表为空");
    }
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    dataMap.put("Model", modelMap);
    dataMap.put("IsAutoSubmitAndAudit", "true");
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public void saveSaleReturnStock(InputStream inputStream) {
    List<SaleReturnStockDto> sheetRowsRaw =
        EasyExcel.read(inputStream)
            .headRowNumber(1)
            .head(SaleReturnStockDto.class)
            .sheet(0)
            .doReadSync();
    List<SaleReturnStockDto> sheetRows = sheetRowsRaw.stream().distinct().collect(toList());

    final Map<String, List<SaleReturnStockDto>> collect =
        sheetRows.stream().collect(Collectors.groupingBy(SaleReturnStockDto::getWarehouseNo));
    log.info("SaleReturnStockDto size:{}", collect.size());

    Long auditDate = 1730433600L;
    // 1082

    collect.forEach(
        (wno, list) -> {
          try {

            log.info("SaleReturnStockDto warehouseNO:{}", wno);

            Map<String, Integer> map = new HashMap<>(128);
            for (SaleReturnStockDto saleOutStockDto : list) {
              map.put(saleOutStockDto.getSkuCode(), Math.abs(saleOutStockDto.getCount()));
            }

            String orderNo = "LS" + RandomUtil.randomNumbers(6);
            final String s = saveSaleReturnStock(map, wno, auditDate, orderNo);
            try {
              final String res = reqTemplate.save2(s);
              log.info("SaleReturnStockDto wno:{},res:{}", wno, res);
              TimeUnit.SECONDS.sleep(1);
            } catch (Exception e) {
              log.error("SaleReturnStockDto fail.wno:{}", wno, e);
            }
          } catch (Exception e) {
            log.error("SaleReturnStockDto error,wno:{}", wno, e);
          }
        });
  }

  private String saveSaleReturnStock(
      Map<String, Integer> paramMap, String warehouseNo, Long auditDate, String order) {

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", SAL_RETURNSTOCK.getFormId());
    Map<String, Object> dataMap = new LinkedHashMap<>(2);

    Map<String, Object> modelMap = new LinkedHashMap<>(16);
    String dateStr = DateUtil.format(auditDate, "yyyy-MM-dd") + " 00:00:00";
    Dict orgDict = Dict.create().set("FNumber", "103");

    Dict customerDict;
    if (SpringUtil.getActiveProfile().equals("dev")
        || SpringUtil.getActiveProfile().equals("test")) {
      customerDict = Dict.create().set("FNumber", "TEST0720001Shop");
    } else {
      if (order.startsWith("LS")) {
        customerDict = Dict.create().set("FNumber", "K0002");
      } else {
        customerDict = Dict.create().set("FNumber", "K0005");
      }
    }
    modelMap.put("FSaleOrgId", orgDict);
    modelMap.put("FReceiveCustId", customerDict);
    modelMap.put("FSettleCustId", customerDict);
    modelMap.put("FStockOrgId", orgDict);
    modelMap.put("FPayCustId", customerDict);
    modelMap.put("FRetcustId", customerDict);
    modelMap.put("FTransferBizType", Dict.create().set("FNumber", "OverOrgSal"));
    modelMap.put("FBillTypeID", Dict.create().set("FNUMBER", "XSTHD01_SYS"));
    modelMap.put("FDate", dateStr);
    modelMap.put("FID", 0);
    modelMap.put("FOwnerTypeIdHead", "BD_OwnerOrg");
    modelMap.put("FIsTotalServiceOrCost", false);
    //        modelMap.put("FBillNo", order);

    Map<String, Object> subHeadEntityMap = new LinkedHashMap<>(8);
    Dict currID = Dict.create().set("FNumber", "PRE001");
    subHeadEntityMap.put("FSettleCurrID", currID);
    subHeadEntityMap.put("FSettleOrgID", orgDict);
    subHeadEntityMap.put("FLocalCurrID", currID);
    subHeadEntityMap.put("FExchangeTypeID", Dict.create().set("FNumber", "HLTX01_SYS"));
    subHeadEntityMap.put("FExchangeRate", 1.0f);
    modelMap.put("SubHeadEntity", subHeadEntityMap);

    List<Map<String, Object>> entityList = new LinkedList<>();
    paramMap.forEach(
        (skuCode, count) -> {
          Map<String, Object> entityMap = new HashMap<>(16);
          entityMap.put("FRowType", "Standard");
          entityMap.put("FMaterialId", Dict.create().set("FNumber", skuCode));
          ItemSku itemSku = itemSkuGateway.getBySkuCode(skuCode);
          if (Objects.isNull(itemSku)) {
            log.error("saveSaleReturnStock itemSku is empty. skuCode:{}", skuCode);
            return;
          }
          String unit = iBaseUnitService.getKingDeeNum(itemSku.getUnit());
          if (StrUtil.isBlank(unit)) {
            log.error("saveSaleReturnStock unit is empty. skuCode:{}", skuCode);
            return;
          }
          entityMap.put("FUnitID", Dict.create().set("FNumber", unit));
          if (count < 0) {
            entityMap.put("FRealQty", new BigDecimal(count).abs().floatValue());
          } else {
            entityMap.put("FRealQty", count.floatValue());
          }

          Long itemId = itemSku.getItemId();
          boolean isGift = false;
          List<ItemProcurement> list =
              iItemProcurementService.lambdaQuery().eq(ItemProcurement::getItemId, itemId).list();
          if (CollUtil.isEmpty(list)) {
            ItemProcurement itemProcurement = list.get(0);
            if (Objects.nonNull(itemProcurement.getIsGift()) && itemProcurement.getIsGift() == 1) {
              isGift = true;
            }
          }
          entityMap.put("FIsFree", isGift);
          entityMap.put("FOwnerTypeId", "BD_OwnerOrg");
          entityMap.put("FOwnerId", orgDict);
          entityMap.put("FReturnType", Dict.create().set("FNumber", "THLX01_SYS"));
          entityMap.put("FDeliveryDate", dateStr);
          entityMap.put("FStockID", Dict.create().set("FNumber", warehouseNo));

          entityList.add(entityMap);
        });

    modelMap.put("FEntity", entityList);
    if (CollUtil.isEmpty(entityList)) {
      throw ExceptionFactory.bizException(
          ErrorCode.BUSINESS_OPERATE_ERROR.getCode(), "销售退货单请求参数实体列表为空");
    }
    dataMap.put("IsAutoSubmitAndAudit", "false");
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    dataMap.put("Model", modelMap);
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public void saveStockOutOrder(InputStream inputStream) {
    List<StockExcelDto> sheetRowsRaw =
        EasyExcel.read(inputStream)
            .headRowNumber(1)
            .head(StockExcelDto.class)
            .sheet(0)
            .doReadSync();
    List<StockExcelDto> sheetRows = sheetRowsRaw.stream().distinct().collect(toList());

    final Map<String, List<StockExcelDto>> collect =
        sheetRows.stream().collect(Collectors.groupingBy(StockExcelDto::getProviderNo));

    // 1021
    log.info("saveStockOutOrder size:{}", collect.size());

    collect.forEach(
        (providerNo, list) -> {
          final String reqJson = saveStockOutOrder(list, providerNo);

          try {
            final String res = reqTemplate.save2(reqJson);
            TimeUnit.SECONDS.sleep(1);
            log.info("stockOutOrder pNo:{},res:{}", providerNo, res);
          } catch (Exception e) {
            log.error("stockOutOrder fail,pNo:{}", providerNo, e);
          }
        });
  }

  @Resource IBaseUnitService iBaseUnitService;

  private String saveStockOutOrder(List<StockExcelDto> stockOutDtoList, String providerNo) {
    //        StockOutOrder stockOutOrder = iStockOutOrderService.getById(id);
    //        Organization organization =
    // iOrganizationService.getById(stockOutOrder.getPurchaseOrganizationId());
    //        Provider provider = providerGateway.getById(stockOutOrder.getProviderId());

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", ApiEnum.SAVE_STOCK_OUT_ORDER.getFormId());
    Map<String, Object> dataMap = new HashMap<>(2);
    Map<String, Object> modelMap = new HashMap<>(16);

    // 单据编号
    modelMap.put("FBillNo", "CGTL" + RandomUtil.randomNumbers(8));
    // 退料组织
    Map<String, Object> stockMap = new HashMap<>(2);
    stockMap.put("FNumber", 103);
    modelMap.put("FStockOrgId", stockMap);
    // 采购组织
    Map<String, Object> purchaseMap = new HashMap<>(2);
    purchaseMap.put("FNumber", 103);
    modelMap.put("FPurchaseOrgId", purchaseMap);
    // 需求组织 = 采购组织
    Map<String, Object> requireOrgMap = new HashMap<>(2);
    requireOrgMap.put("FNumber", 103);
    modelMap.put("FRequireOrgId", requireOrgMap);

    // 退料日期
    modelMap.put("FDate", DateUtil.format(1730433600L, "yyyy-MM-dd"));

    // 采购组? todo
    // 采购员？todo
    // 退料方式.退料补料 1:A 。2.退料并扣款 2:B
    modelMap.put("FMRMODE", "B");
    // 供应商
    Map<String, Object> providerMap = new HashMap<>(2);
    providerMap.put("FNumber", providerNo);
    modelMap.put("FSupplierId", providerMap);
    // 退料类型 库存退料1:B
    modelMap.put("FMRTYPE", "B");

    List<Map<String, Object>> entryMapList = new LinkedList<>();

    for (StockExcelDto detail : stockOutDtoList) {

      ItemSku itemSku = itemSkuGateway.getBySkuCode(detail.getSkuCode());
      String unit = iBaseUnitService.getKingDeeNum(itemSku.getUnit());

      Map<String, Object> entryMap = new HashMap<>(8);
      // 物料编号
      Map<String, Object> fMaterialId = new HashMap<>(2);
      fMaterialId.put("FNumber", detail.getSkuCode());
      entryMap.put("FMaterialId", fMaterialId);
      // 名称，规则，计价单位、采购单位。这几个参数直接物料的数据，自动填充，无需填写
      // 库存单位，
      Map<String, Object> unitMap = new HashMap<>(2);
      unitMap.put("FNumber", unit);
      entryMap.put("FUnitID", unitMap);
      // 计价单位
      Map<String, Object> priceUnitMap = new HashMap<>(2);
      priceUnitMap.put("FNumber", unit);
      entryMap.put("FPriceUnitID", priceUnitMap);
      // 实退数量（主要，可编辑)，扣款数量 = 计价数量 = 采购数量 自动填充跟随实退数量。对应erp实际退料数量
      entryMap.put("FRMREALQTY", Math.abs(Integer.parseInt(detail.getNum())));
      // 补料数量（可编辑）。对应erp实际退料数量
      entryMap.put("FBaseReplayQty", Math.abs(Integer.parseInt(detail.getNum())));
      // 库存状态，默认可用
      Map<String, Object> stockStatusMap = new HashMap<>(2);
      stockStatusMap.put("FNumber", "KCZT01_SYS");
      entryMap.put("FStockStatusId", stockStatusMap);
      // 是否是赠品
      boolean giveAway = new BigDecimal(detail.getPriceWithTax()).compareTo(BigDecimal.ZERO) == 0;
      entryMap.put("FGiveAway", giveAway);
      // 仓库
      Map<String, Object> stockIdMap = new HashMap<>(2);

      stockIdMap.put("FNumber", detail.getWarehouseNo());
      entryMap.put("FSTOCKID", stockIdMap);
      // 备注
      entryMap.put("FNote", "");
      // 含税单价
      entryMap.put("FTAXPRICE", detail.getPriceWithTax());
      // 税率（单位%带小数点）
      entryMap.put("FENTRYTAXRATE", new BigDecimal(detail.getTaxRate()));
      entryMapList.add(entryMap);
    }

    modelMap.put("FPURMRBENTRY", entryMapList);
    dataMap.put("Model", modelMap);
    //        if (getKingDeeConfig().getAutoSubmit()) {
    dataMap.put("IsAutoSubmitAndAudit", "false");
    //        }
    // 补丁参数解决。更新库存时出现可以忽略的异常数据是否继续？
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    reqMap.put("data", dataMap);

    return JsonUtil.toJson(reqMap);
  }

  public void saveStockInOrder(InputStream inputStream) {
    List<StockExcelDto> sheetRowsRaw =
        EasyExcel.read(inputStream)
            .headRowNumber(1)
            .head(StockExcelDto.class)
            .sheet(0)
            .doReadSync();
    List<StockExcelDto> sheetRows = sheetRowsRaw.stream().distinct().collect(toList());

    final Map<String, List<StockExcelDto>> collect =
        sheetRows.stream().collect(Collectors.groupingBy(StockExcelDto::getProviderNo));

    log.info("----- saveStockInOrder ---- size:{}", collect.size());

    collect.forEach(
        (providerNo, list) -> {
          final String reqJson = saveStockInOrder(list, providerNo);
          try {
            final String res = reqTemplate.save2(reqJson);
            TimeUnit.SECONDS.sleep(1);
            log.info("saveStockInOrder providerNo:{},res:{}", providerNo, res);
          } catch (Exception e) {
            log.error("saveStockInOrder providerNo fail", e);
          }
        });
  }

  public String saveStockInOrder(List<StockExcelDto> stockExcelDtoList, String providerNo) {
    //        StockInOrder stockInOrder = iStockInOrderService.getById(id);
    //        Organization organization =
    // iOrganizationService.getById(stockInOrder.getOrganizationId());
    //        Provider provider = providerGateway.getById(stockInOrder.getProviderId());
    //        String buyerKingDeeId = buyerGateway.getKingDeeId(stockInOrder.getBuyerUserId());

    Map<String, Object> reqMap = new HashMap<>(2);
    reqMap.put("formId", "STK_InStock");
    Map<String, Object> modelMap = new HashMap<>(16);
    // 如果存在kingDeeId，则表示更新，否则为新增。
    //        if (StringUtil.isNotBlank(stockInOrder.getKingDeeId())) {
    //            modelMap.put("FID", stockInOrder.getKingDeeId());
    //        }
    // 单据编号
    modelMap.put("FBillNo", "CGRK" + RandomUtil.randomNumbers(8));
    // 入库日期
    //        if (isHedge) {
    //            LocalDateTime localDateTime =
    // DateUtil.parseTimeStamp(stockInOrder.getReceiptTime());
    //            LocalDateTime localDateTime1 = localDateTime.plusDays(1);
    //            modelMap.put("FDate", DateUtil.format(localDateTime1, "yyyy-MM-dd"));
    //        } else {
    modelMap.put("FDate", "2024-11-01");
    //        }
    // 收料组织id
    Map<String, Object> stockMap = new HashMap<>(2);
    stockMap.put("FNumber", "103");
    modelMap.put("FStockOrgId", stockMap);
    // 采购组织
    Map<String, Object> purchaseMap = new HashMap<>(2);
    purchaseMap.put("FNumber", "103");
    modelMap.put("FPurchaseOrgId", purchaseMap);
    // 采购员
    Map<String, Object> buyerMap = new HashMap<>(2);
    buyerMap.put("FNumber", "DSKJ0018");
    modelMap.put("FPurchaserId", buyerMap);
    // 供应商
    Map<String, Object> providerMap = new HashMap<>(2);
    providerMap.put("FNumber", providerNo);
    modelMap.put("FSupplierId", providerMap);
    // 需求组织
    Map<String, Object> demandOrgMap = new HashMap<>(2);
    demandOrgMap.put("FNumber", "103");
    modelMap.put("FDemandOrgId", demandOrgMap);

    // 物料信息列
    List<Map<String, Object>> stockEntryList = new LinkedList<>();
    for (StockExcelDto detail : stockExcelDtoList) {

      final ItemSku itemSku = itemSkuGateway.getBySkuCode(detail.getSkuCode());
      String unit = iBaseUnitService.getKingDeeNum(itemSku.getUnit());
      unit = StrUtil.isEmpty(unit) ? "ge" : unit;

      Map<String, Object> entryMap = new HashMap<>(16);
      // 物料编号
      Map<String, Object> fMaterialId = new HashMap<>(2);
      fMaterialId.put("FNumber", detail.getSkuCode());
      entryMap.put("FMaterialId", fMaterialId);
      // 规格
      //            entryMap.put("FUOM", attrStr);
      // 库存单位
      Map<String, Object> unitMap = new HashMap<>(2);
      unitMap.put("FNumber", unit);
      entryMap.put("FUnitID", unitMap);
      // 定价单位
      Map<String, Object> setPriceUnitMap = new HashMap<>(2);
      setPriceUnitMap.put("FNumber", unit);
      entryMap.put("FSetPriceUnitID", setPriceUnitMap);
      // 采购单位
      Map<String, Object> remainInStockUnitMap = new HashMap<>(2);
      remainInStockUnitMap.put("FNumber", unit);
      entryMap.put("FRemainInStockUnitId", remainInStockUnitMap);
      // 计价单位
      Map<String, Object> priceUnitMap = new HashMap<>(2);
      priceUnitMap.put("FNumber", unit);
      entryMap.put("FPriceUnitID", priceUnitMap);
      // 应收数量
      entryMap.put("FMustQty ", Math.abs(Integer.parseInt(detail.getNum())));
      // 实收数量
      entryMap.put("FRealQty ", Math.abs(Integer.parseInt(detail.getNum())));
      // 是否赠品
      String giveAway =
          new BigDecimal(detail.getPriceWithTax()).compareTo(BigDecimal.ZERO) == 0
              ? "true"
              : "false";
      entryMap.put("FGiveAway", giveAway);
      // 仓库
      Map<String, Object> stockIdMap = new HashMap<>(2);
      stockIdMap.put("FNumber", detail.getWarehouseNo());
      entryMap.put("FStockId", stockIdMap);
      // 备注
      entryMap.put("FNote", "");
      // 含税单价
      entryMap.put("FTaxPrice", detail.getPriceWithTax());
      // 税率，金蝶单位是%，所以要再乘以100
      entryMap.put("FEntryTaxRate", detail.getTaxRate());
      // 计价数量
      entryMap.put("FPriceUnitQty", Math.abs(Integer.parseInt(detail.getNum())));
      stockEntryList.add(entryMap);
    }

    modelMap.put("FInStockEntry", stockEntryList);

    // 单据类型
    Map<String, Object> billTypeIdMap = new HashMap<>(2);
    billTypeIdMap.put("FNumber", "RKD01_SYS");
    modelMap.put("FBillTypeID", billTypeIdMap);
    // 货主类型
    modelMap.put("FOwnerTypeIdHead", "BD_OwnerOrg");
    // 货主
    Map<String, Object> ownerTypeIdHeadMap = new HashMap<>(2);
    ownerTypeIdHeadMap.put("FNumber", "103");
    modelMap.put("FOwnerIdHead", ownerTypeIdHeadMap);

    Map<String, Object> dataMap = new HashMap<>(2);
    dataMap.put("Model", modelMap);
    dataMap.put("IsAutoSubmitAndAudit", "false");
    dataMap.put("InterationFlags", "STK_InvCheckResult");
    reqMap.put("data", dataMap);
    return JsonUtil.toJson(reqMap);
  }

  public void skuPriceRefresh() {
    List<ItemSkuPrice> itemSkuPrices = new LinkedList<>();

    final List<ItemSku> list = iItemSkuService.lambdaQuery().list();
    for (ItemSku itemSku : list) {
      ItemSkuPrice skuPrice = new ItemSkuPrice();
      skuPrice.setSkuId(itemSku.getId());
      skuPrice.setSkuCode(itemSku.getSkuCode());
      skuPrice.setPrice(itemSku.getContractSalePrice());
      skuPrice.setStartTime(1727712000L);
      skuPrice.setEndTime(0L);
      skuPrice.setType(2);
      itemSkuPrices.add(skuPrice);

      ItemSkuPrice skuPrice2 = new ItemSkuPrice();
      skuPrice2.setSkuId(itemSku.getId());
      skuPrice2.setSkuCode(itemSku.getSkuCode());
      skuPrice2.setPrice(itemSku.getPlatformCommission());
      skuPrice2.setStartTime(1727712000L);
      skuPrice2.setEndTime(0L);
      skuPrice2.setType(3);
      itemSkuPrices.add(skuPrice2);
    }

    if (CollUtil.isNotEmpty(itemSkuPrices)) {
      final IItemSkuPriceService itemSkuPriceService =
          SpringUtil.getBean(IItemSkuPriceService.class);
      itemSkuPriceService.saveBatch(itemSkuPrices);
    }
  }

  public void refreshPurchaseOrderBusinessLine() {
    final IPurchaseOrderService bean = SpringUtil.getBean(IPurchaseOrderService.class);
    final IPurchaseOrderDetailService detail =
        SpringUtil.getBean(IPurchaseOrderDetailService.class);
    final ItemSkuGateway itemSkuGateway1 = SpringUtil.getBean(ItemSkuGateway.class);

    final List<PurchaseOrder> list =
        bean.lambdaQuery()
            .eq(PurchaseOrder::getPurchaseDate, 1735574400L)
            .eq(PurchaseOrder::getBuyerUserId, -1)
            .list();
    for (PurchaseOrder purchaseOrder : list) {
      final List<PurchaseOrderDetail> detailList =
          detail
              .lambdaQuery()
              .eq(PurchaseOrderDetail::getPurchaseOrderId, purchaseOrder.getId())
              .list();
      final List<String> skuCodeList =
          detailList.stream().map(PurchaseOrderDetail::getItemSkuCode).collect(toList());

      Set<Integer> businessLineSet = new HashSet<>();
      for (String skuCode : skuCodeList) {
        final Integer skuBusinessLine = itemSkuGateway1.getSkuBusinessLine(skuCode);
        businessLineSet.add(skuBusinessLine);
      }
      if (businessLineSet.size() == 1) {
        List<Integer> businessLineList = new ArrayList<>(businessLineSet);
        if (businessLineList.get(0) == 3) {
          log.info("update BusinessLine 3 。purchaseOrderNo:{}", purchaseOrder.getNo());
          purchaseOrder.setBusinessLine(3);
          bean.updateById(purchaseOrder);
        }
      }
    }
  }

  @Autowired private IAllChannelBillDataService allChannelBillDataService;
  @Autowired private IAllChannelBillOrderDataService iAllChannelBillOrderDataService;

  static String subOrderIds =
      "20250325169343154810207870_13544603\n"
          + "20250325148708362339727426_13544502\n"
          + "20250326175905278401731907_13549099\n"
          + "20250326187561983382217197_13549151\n"
          + "20250325490197358064307094_13546729\n"
          + "20250326306131088006493608_13547860\n"
          + "20250326272262283436474129_13549860\n"
          + "20250326119405137951595104_13548640\n"
          + "20250325520149251216844839_13546958\n"
          + "20250325217876954315599414_13544874\n"
          + "20250325194462399173134496_13544744\n"
          + "20250326117645626581790287_13548626\n"
          + "20250326421922446862656344_13547931\n"
          + "20250325398395413670123751_13546086\n"
          + "20250325353192411244535614_13545828\n"
          + "20250325252717913869125007_13545177\n"
          + "20250325223260502969712265_13544926\n"
          + "20250326395219011500986821_13547909\n"
          + "20250325214671711200096545_13544845\n"
          + "20250325187082477883591168_13544694\n"
          + "20250325220827367810223372_13544901\n"
          + "20250326174875381106900310_13549091\n"
          + "20250326965645970377234974_13548395\n"
          + "20250326644815039935169319_13548107\n"
          + "20250325235715135650257841_13545041\n"
          + "20250325140306703047483787_13544427\n"
          + "20250325184386993689180460_13544677\n"
          + "20250325931675520055998000_13542646\n"
          + "20250326266304867501172909_13549816\n"
          + "20250325203458601174522624_13544790\n"
          + "20250326254159317121676611_13549680\n"
          + "20250326262425662226498629_13549778\n"
          + "20250326141946662010822107_13548826\n"
          + "20250325132900332772214859_13544306\n"
          + "20250325414808854553624197_13546201\n"
          + "20250325250834963846804140_13545162\n"
          + "20250326140248588759769867_13548813\n"
          + "20250326844777149565758186_13548297\n"
          + "20250325209596191265039286_13544820\n"
          + "20250326167086682899393633_13549028\n"
          + "20250325230679821280449815_13544989\n"
          + "20250325218257395589997410_13544877\n"
          + "20250325261223416753547571_13545239\n"
          + "20250325252791894649902474_13545181\n"
          + "20250325442154322829114827_13546404\n"
          + "20250325212387614726944233_13544827\n"
          + "20250326256976424529031669_13549711\n"
          + "20250325214383021617374793_13544842\n"
          + "20250325492229817469067064_13546747\n"
          + "20250326256500015618241040_13549706\n"
          + "20250325245706087327471728_13545117\n"
          + "20250326143110555730090953_13548835\n"
          + "20250325507838095348318583_13546872\n"
          + "20250326150307559983430502_13548908\n"
          + "20250325225527721925157397_13544951\n"
          + "20250326257265651831070619_13549726\n"
          + "20250326160790518396014323_13548981\n"
          + "20250325156658007789596283_13544542\n"
          + "20250326298075088006498301_13547858\n"
          + "20250326261692049983623665_13549766\n"
          + "20250325202964001174520068_13544784\n"
          + "20250325296259825207546966_13545486\n"
          + "20250326255975167964702086_13549699\n"
          + "20250325198778509441314103_13544761\n"
          + "20250325541489071216186319_13547118\n"
          + "20250325138814435181168389_13544411\n"
          + "20250326174213639983465991_13549077\n"
          + "20250326277524282847204236_13549909\n"
          + "20250325222620572821307566_13544920\n"
          + "20250325443917691607227640_13546416\n"
          + "20250325444832853676566431_13546426\n"
          + "20250325271549796913762866_13545312\n"
          + "20250326255615208273897828_13549694\n"
          + "20250326274136093886356974_13549875\n"
          + "20250325352559401335382351_13545825\n"
          + "20250326231545411346544163_13549494\n"
          + "20250325221636331919499595_13544912\n"
          + "20250326262509128902610658_13549779\n"
          + "20250325447930297702797570_13546447\n"
          + "20250326135910092994134538_13548782\n"
          + "20250326196748581749988063_13549227\n"
          + "20250325163863326055683686_13544575\n"
          + "20250326283357687355622502_13549961\n"
          + "20250326192311391620640089_13549184\n"
          + "20250326158262328235994029_13548973\n"
          + "20250325189111884595870827_13544708\n"
          + "20250326113884761895126338_13548595\n"
          + "20250326180924566068179445_13549116\n"
          + "20250325310401513594169492_13545607\n"
          + "20250325491195843011486557_13546731\n"
          + "20250326127968058378319951_13548722\n"
          + "20250325262898271258734900_13545248\n"
          + "20250325548058032708542882_13547154\n"
          + "20250325423663601604964838_13546264\n"
          + "20250325212972217883591189_13544834\n"
          + "20250325253614947810230402_13545188\n"
          + "20250325139808371749988253_13544424\n"
          + "20250325297085769385864619_13545495\n"
          + "20250325254125531312412841_13545193\n"
          + "20250326137175082994134232_13548789\n"
          + "20250326265281207507037509_13549810\n"
          + "20250326247425312822590050_13549631\n"
          + "20250325317413388614106928_13545644\n"
          + "20250326372979564613220279_13547890\n"
          + "20250326272145852823851566_13549859\n"
          + "20250325255685722271886418_13545206\n"
          + "20250326225995971760865656_13549446\n"
          + "20250325142663821207632489_13544444\n"
          + "20250325409814907620913603_13546159\n"
          + "20250325314205667224596653_13545626\n"
          + "20250326139242003295419131_13548796\n"
          + "20250326143876581639026370_13548842\n"
          + "20250326237475856191550352_13549550\n"
          + "20250326132664612023433037_13548760\n"
          + "20250326225729874907609379_13549442\n"
          + "20250325225548291604179953_13544952\n"
          + "20250325145880119541482626_13544480\n"
          + "20250326229390833397896535_13549479\n"
          + "20250325384565112617942363_13546005\n"
          + "20250325292646496164267455_13545460\n"
          + "20250325157988867864690617_13544544\n"
          + "20250325270271962216961780_13545303\n"
          + "20250325458939511309479198_13546524\n"
          + "20250325449887998110674312_13546462\n"
          + "20250325203218011174528892_13544788\n"
          + "20250325494260072819418921_13546759\n"
          + "20250326190821262256477021_13549174\n"
          + "20250325274581608127573675_13545334\n"
          + "20250325279789504641054626_13545369\n"
          + "20250325277363672850824244_13545354\n"
          + "20250325194573637608325854_13544745\n"
          + "20250325273100104452191886_13545325\n"
          + "20250325231125007494443132_13544996\n"
          + "20250325429224115756679528_13546295\n"
          + "20250326195641921047413845_13549223\n"
          + "20250325216763091714960707_13544857\n"
          + "20250325224969821179137123_13544939\n"
          + "20250326188367944228898250_13549159\n"
          + "20250325239732531050726004_13545071\n"
          + "20250326870455025003418009_13548313\n"
          + "20250325289943689417935313_13545441\n"
          + "20250326278271931240505981_13549918\n"
          + "20250325214196781179901914_13544840\n"
          + "20250325282591253029942001_13545390\n"
          + "20250326171940444815836913_13549060\n"
          + "20250325435100934001704329_13546332\n"
          + "20250325148627567200573461_13544501\n"
          + "20250325410441962066336292_13546160\n"
          + "20250325145138545803047486_13544469\n"
          + "20250325259296395425918480_13545228\n"
          + "20250326139700703224777814_13548806\n"
          + "20250325147359854103362260_13544496\n"
          + "20250325327925541608980910_13545687\n"
          + "20250325269188651277420598_13545294\n"
          + "20250326456485211965664121_13547963\n"
          + "20250326132761281201841622_13548761\n"
          + "20250325340128546379815810_13545766\n"
          + "20250325148394826461322911_13544499\n"
          + "20250325184974673689180267_13544683\n"
          + "20250325420930409634237645_13546243\n"
          + "20250326147970928036764593_13548881\n"
          + "20250326192571989627861269_13549188\n"
          + "20250325140333302987817780_13544428\n"
          + "20250325465663363815056194_13546567\n"
          + "20250325332993093364438132_13545718\n"
          + "20250325970616337882844026_13542693\n"
          + "20250325160125972215640308_13544557\n"
          + "20250325244731113908572918_13545114\n"
          + "20250326152976909349237646_13548927\n"
          + "20250326265586185862373718_13549813\n"
          + "20250326742050256137853323_13547551\n"
          + "20250326786994158851596957_13547566\n"
          + "20250325428549003563746287_13546289\n"
          + "20250326214299354304190928_13549366\n"
          + "20250326229136444907609680_13549475\n"
          + "20250326143768885730090810_13548841\n"
          + "20250326282179281831070633_13549958\n"
          + "20250326259106073804205011_13549739\n"
          + "20250326772098511842311737_13548217\n"
          + "20250326253742901760865153_13549664\n"
          + "20250326159806202262671507_13548978\n"
          + "20250325263135564921730212_13545250\n"
          + "20250326270690345873668071_13549851\n"
          + "20250325175929305017200979_13544628\n"
          + "20250325441379259458774323_13546395\n"
          + "20250326182460509901024478_13549126\n"
          + "20250326206255561196250400_13549288\n"
          + "20250326835261453528584622_13547662\n"
          + "20250325381911154601174682_13545994\n"
          + "20250325180587784553622968_13544656\n"
          + "20250326172759073665753476_13549064\n"
          + "20250325165763891794839517_13544591\n"
          + "20250325201617363354742674_13544775\n"
          + "20250326237444427706752968_13549549\n"
          + "20250325141446703563749102_13544437\n"
          + "20250325133239692772214665_13544313";

  public static void main(String[] args) {
    for (String s : subOrderIds.split("\n")) {
      System.out.println(s);
    }
  }

  public void deleteBillData() {

    for (String s : subOrderIds.split("\n")) {
      final boolean remove =
          allChannelBillDataService
              .lambdaUpdate()
              .set(AllChannelBillData::getIsDel, 1)
              .eq(AllChannelBillData::getSubOrderId, s.trim())
              .update();
      log.info("deleteBillData res:{}", remove);
    }

    String orderIds =
        "20250325132900332772214859\n"
            + "20250325133239692772214665\n"
            + "20250325138814435181168389\n"
            + "20250325139808371749988253\n"
            + "20250325140306703047483787\n"
            + "20250325140333302987817780\n"
            + "20250325141446703563749102\n"
            + "20250325142663821207632489\n"
            + "20250325145138545803047486\n"
            + "20250325145880119541482626\n"
            + "20250325147359854103362260\n"
            + "20250325148394826461322911\n"
            + "20250325148627567200573461\n"
            + "20250325148708362339727426\n"
            + "20250325156658007789596283\n"
            + "20250325157988867864690617\n"
            + "20250325160125972215640308\n"
            + "20250325163863326055683686\n"
            + "20250325165763891794839517\n"
            + "20250325169343154810207870\n"
            + "20250325175929305017200979\n"
            + "20250325180587784553622968\n"
            + "20250325184386993689180460\n"
            + "20250325184974673689180267\n"
            + "20250325187082477883591168\n"
            + "20250325189111884595870827\n"
            + "20250325194462399173134496\n"
            + "20250325198778509441314103\n"
            + "20250325202964001174520068\n"
            + "20250325203218011174528892\n"
            + "20250325203458601174522624\n"
            + "20250325209596191265039286\n"
            + "20250325212387614726944233\n"
            + "20250325212972217883591189\n"
            + "20250325214383021617374793\n"
            + "20250325214671711200096545\n"
            + "20250325216763091714960707\n"
            + "20250325217876954315599414\n"
            + "20250325218257395589997410\n"
            + "20250325220827367810223372\n"
            + "20250325221636331919499595\n"
            + "20250325222620572821307566\n"
            + "20250325223260502969712265\n"
            + "20250325224969821179137123\n"
            + "20250325225527721925157397\n"
            + "20250325225548291604179953\n"
            + "20250325230679821280449815\n"
            + "20250325231125007494443132\n"
            + "20250325235715135650257841\n"
            + "20250325239732531050726004\n"
            + "20250325244731113908572918\n"
            + "20250325245706087327471728\n"
            + "20250325250834963846804140\n"
            + "20250325252717913869125007\n"
            + "20250325252791894649902474\n"
            + "20250325253614947810230402\n"
            + "20250325254125531312412841\n"
            + "20250325255685722271886418\n"
            + "20250325259296395425918480\n"
            + "20250325261223416753547571\n"
            + "20250325262898271258734900\n"
            + "20250325263135564921730212\n"
            + "20250325269188651277420598\n"
            + "20250325270271962216961780\n"
            + "20250325271549796913762866\n"
            + "20250325273100104452191886\n"
            + "20250325274581608127573675\n"
            + "20250325277363672850824244\n"
            + "20250325279789504641054626\n"
            + "20250325282591253029942001\n"
            + "20250325289943689417935313\n"
            + "20250325292646496164267455\n"
            + "20250325296259825207546966\n"
            + "20250325297085769385864619\n"
            + "20250325310401513594169492\n"
            + "20250325314205667224596653\n"
            + "20250325317413388614106928\n"
            + "20250325327925541608980910\n"
            + "20250325332993093364438132\n"
            + "20250325340128546379815810\n"
            + "20250325352559401335382351\n"
            + "20250325381911154601174682\n"
            + "20250325384565112617942363\n"
            + "20250325409814907620913603\n"
            + "20250325410441962066336292\n"
            + "20250325414808854553624197\n"
            + "20250325420930409634237645\n"
            + "20250325423663601604964838\n"
            + "20250325428549003563746287\n"
            + "20250325429224115756679528\n"
            + "20250325435100934001704329\n"
            + "20250325441379259458774323\n"
            + "20250325442154322829114827\n"
            + "20250325443917691607227640\n"
            + "20250325447930297702797570\n"
            + "20250325449887998110674312\n"
            + "20250325458939511309479198\n"
            + "20250325465663363815056194\n"
            + "20250325490197358064307094\n"
            + "20250325491195843011486557\n"
            + "20250325492229817469067064\n"
            + "20250325494260072819418921\n"
            + "20250325507838095348318583\n"
            + "20250325520149251216844839\n"
            + "20250325541489071216186319\n"
            + "20250325548058032708542882\n"
            + "20250325931675520055998000\n"
            + "20250325970616337882844026\n"
            + "20250326113884761895126338\n"
            + "20250326117645626581790287\n"
            + "20250326119405137951595104\n"
            + "20250326127968058378319951\n"
            + "20250326132664612023433037\n"
            + "20250326132761281201841622\n"
            + "20250326135910092994134538\n"
            + "20250326137175082994134232\n"
            + "20250326139242003295419131\n"
            + "20250326139700703224777814\n"
            + "20250326140248588759769867\n"
            + "20250326141946662010822107\n"
            + "20250326143110555730090953\n"
            + "20250326143768885730090810\n"
            + "20250326143876581639026370\n"
            + "20250326147970928036764593\n"
            + "20250326150307559983430502\n"
            + "20250326152976909349237646\n"
            + "20250326158262328235994029\n"
            + "20250326159806202262671507\n"
            + "20250326160790518396014323\n"
            + "20250326167086682899393633\n"
            + "20250326171940444815836913\n"
            + "20250326172759073665753476\n"
            + "20250326174213639983465991\n"
            + "20250326175905278401731907\n"
            + "20250326180924566068179445\n"
            + "20250326182460509901024478\n"
            + "20250326187561983382217197\n"
            + "20250326188367944228898250\n"
            + "20250326190821262256477021\n"
            + "20250326192311391620640089\n"
            + "20250326192571989627861269\n"
            + "20250326195641921047413845\n"
            + "20250326196748581749988063\n"
            + "20250326206255561196250400\n"
            + "20250326214299354304190928\n"
            + "20250326225729874907609379\n"
            + "20250326225995971760865656\n"
            + "20250326229136444907609680\n"
            + "20250326229390833397896535\n"
            + "20250326231545411346544163\n"
            + "20250326237444427706752968\n"
            + "20250326237475856191550352\n"
            + "20250326247425312822590050\n"
            + "20250326253742901760865153\n"
            + "20250326254159317121676611\n"
            + "20250326255615208273897828\n"
            + "20250326255975167964702086\n"
            + "20250326256500015618241040\n"
            + "20250326256976424529031669\n"
            + "20250326257265651831070619\n"
            + "20250326259106073804205011\n"
            + "20250326261692049983623665\n"
            + "20250326262425662226498629\n"
            + "20250326262509128902610658\n"
            + "20250326265281207507037509\n"
            + "20250326265586185862373718\n"
            + "20250326266304867501172909\n"
            + "20250326270690345873668071\n"
            + "20250326272145852823851566\n"
            + "20250326272262283436474129\n"
            + "20250326277524282847204236\n"
            + "20250326278271931240505981\n"
            + "20250326282179281831070633\n"
            + "20250326283357687355622502\n"
            + "20250326298075088006498301\n"
            + "20250326306131088006493608\n"
            + "20250326372979564613220279\n"
            + "20250326395219011500986821\n"
            + "20250326421922446862656344\n"
            + "20250326456485211965664121\n"
            + "20250326644815039935169319\n"
            + "20250326742050256137853323\n"
            + "20250326772098511842311737\n"
            + "20250326786994158851596957\n"
            + "20250326835261453528584622\n"
            + "20250326844777149565758186\n"
            + "20250326965645970377234974";
    for (String s : orderIds.split("\n")) {
      iAllChannelBillOrderDataService
          .lambdaUpdate()
          .eq(AllChannelBillOrderData::getOrderId, s)
          .set(AllChannelBillOrderData::getIsDel, 1)
          .update();
    }
  }

  public void deleteOrderSettlement() {
    SettlementOrderPageQuery query = new SettlementOrderPageQuery();
    query.setTimes(ListUtil.of(1743436800L));
    query.setBusinessLine(ListUtil.of(0));
    query.setPageSize(999);
    final PageResponse<SettlementOrderPageVo> settlementOrderPageVoPageResponse =
        orderSettlementBizService.settlementPageQuery(query);
    final Set<String> nos =
        settlementOrderPageVoPageResponse.getData().stream()
            .map(SettlementOrderPageVo::getNo)
            .collect(Collectors.toSet());
    orderSettlementBizService.deleteOrderForm(new LinkedList<>(nos), "");
  }

  @Autowired IPaymentApplyOrderService paymentApplyOrderService;

  public void syncPaymentOrder() {
    final List<PaymentApplyOrder> list =
        paymentApplyOrderService
            .lambdaQuery()
            .between(PaymentApplyOrder::getFinancialAuditTime, 1746028800L, 1748707200L)
            .eq(PaymentApplyOrder::getStatus, PaymentOrderStatus.SYNC_KING_DEE_ERROR)
            .list();
    list.forEach(
        order -> {
          paymentOrderBizService.sync(order.getId());
          try {
            TimeUnit.SECONDS.sleep(5);
          } catch (InterruptedException e) {
            // ignore
          }
        });
  }

  public void fillContractTime() {
    iPurchaseOrderService
        .lambdaQuery()
        .ne(PurchaseOrder::getContractNo, "")
        .isNotNull(PurchaseOrder::getContractNo)
        .list()
        .forEach(
            val -> {
              final String contractNo = val.getContractNo();
              final List<ContractDropdownItem> contractDropdownItemList =
                  contractGateway.contractDropdownList(contractNo, null, null);
              if (CollUtil.isNotEmpty(contractDropdownItemList)) {
                val.setContractStartTime(contractDropdownItemList.get(0).getStartTime());
                val.setContractEndTime(contractDropdownItemList.get(0).getEndTime());
                iPurchaseOrderService.updateById(val);
              }
            });
    log.info("合同历史数据处理完毕");
  }
}
