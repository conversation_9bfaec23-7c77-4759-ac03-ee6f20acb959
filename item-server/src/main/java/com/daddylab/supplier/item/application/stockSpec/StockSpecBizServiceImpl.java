package com.daddylab.supplier.item.application.stockSpec;

import static com.alibaba.cola.dto.PageQuery.ASC;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.application.message.QyMsgSendService;
import com.daddylab.supplier.item.application.message.wechat.RemindType;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.common.domain.dto.ResponseFactory;
import com.daddylab.supplier.item.common.trans.StaffAssembler;
import com.daddylab.supplier.item.controller.common.dto.WarehousePageQuery;
import com.daddylab.supplier.item.controller.item.dto.CorpBizTypeDTO;
import com.daddylab.supplier.item.domain.exportTask.ExportManager;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.staff.vo.DepartmentHeadBriefVO;
import com.daddylab.supplier.item.domain.staff.vo.StaffBrief;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.types.BatchResultStat;
import com.daddylab.supplier.item.types.inventoryMonitor.IInventoryMonitorId;
import com.daddylab.supplier.item.types.inventoryMonitor.InventoryMonitorId;
import com.daddylab.supplier.item.types.inventoryMonitor.SetMonitorThresholdCmd;
import com.daddylab.supplier.item.types.stockSpec.*;
import com.daddylab.supplier.item.types.stockStatistic.WdtStockSpecStatisticQuery;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @since 2024/3/1
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class StockSpecBizServiceImpl implements StockSpecBizService {
    private final IWarehouseService warehouseService;
    private final IWdtStockSpecService wdtStockSpecService;
    private final IWdtStockSpecRtService wdtStockSpecRtService;
    private final IBrandService brandService;
    private final ICategoryService categoryService;
    private final IItemService itemService;
    private final IItemSkuService itemSkuService;
    private final IInventoryMonitorService inventoryMonitorService;
    private final ExportManager exportManager;
    private final WdtStockSpecFetchService wdtStockSpecFetchService;
    private final IItemSkuTradeStatisticService itemSkuTradeStatisticService;
    private final ItemProcurementGateway itemProcurementGateway;
    private final StockSpecConfig stockSpecConfig;
    private final RefreshConfig refreshConfig;
    private final QyMsgSendService qyMsgSendService;
    private final IBizLevelDivisionService iBizLevelDivisionService;
    private final StaffService staffService;
    private final IPurchaseOrderService purchaseOrderService;
    private final ItemGateway itemGateway;
    private final ICombinationItemService combinationItemService;
    private final IComposeSkuService composeSkuService;
    private final IInventoryAllocShopService inventoryAllocShopService;

    @Override
    public PageResponse<StockSpecVO> query(StockSpecQuery query) {
        final IPage<WdtStockSpec> wdtStockSpecIPage = pageQueryStockSpecPO(query);
        final List<WdtStockSpec> records = wdtStockSpecIPage.getRecords();
        if (records.isEmpty()) {
            return ResponseFactory.emptyPage();
        }
        final List<String> warehouseNos = records.stream().map(WdtStockSpec::getWarehouseNo).distinct()
                .collect(Collectors.toList());
        final List<Warehouse> warehouses = warehouseService.getBatchByNos(warehouseNos);
        final Map<String, Warehouse> warehousesMap = warehouses.stream()
                .collect(Collectors.toMap(Warehouse::getNo, Function.identity()));
        final List<String> spuCodes = records.stream().map(WdtStockSpec::getGoodsNo).distinct()
                .collect(Collectors.toList());
        final List<Item> items = itemService.getBatchByMixedCode(spuCodes);
        final Map<String, Item> itemsMap = items.stream()
                .collect(Collectors.toMap(Item::getSupplierCode, Function.identity(), (a, b) -> a));
        itemsMap.putAll(items.stream().collect(Collectors.toMap(Item::getCode, Function.identity(), (a, b) -> a)));

        final List<String> specNos = records.stream().map(WdtStockSpec::getSpecNo).distinct()
                .collect(Collectors.toList());
        final List<ItemSku> itemSkus = itemSkuService.selectByMixCodes(specNos);
        final Map<String, ItemSku> itemSkusMap = itemSkus.stream()
                .collect(Collectors.toMap(ItemSku::getSupplierCode, Function.identity(), (a, b) -> a));
        itemSkusMap.putAll(itemSkus.stream().collect(Collectors.toMap(ItemSku::getSkuCode, Function.identity())));

        // 层级参数
        final Map<String, List<CorpBizTypeDTO>> levelDivisionMap = iBizLevelDivisionService.queryBySkuCode(specNos);

        final List<Long> brandIds = items.stream().map(Item::getBrandId).distinct().collect(Collectors.toList());
        Map<Long, Brand> brandsMap = new HashMap<>(brandIds.size());
        if (CollUtil.isNotEmpty(brandIds)) {
            final List<Brand> brands = brandService.listByIds(brandIds);
            brandsMap = brands.stream().collect(Collectors.toMap(Entity::getId, Function.identity()));
        }

        final List<Long> categoryIds = items.stream().map(Item::getCategoryId).distinct().collect(Collectors.toList());
        Map<Long, Category> categoriesMap = new HashMap<>(categoryIds.size());
        if (CollUtil.isNotEmpty(categoryIds)) {
            categoriesMap = categoryService.listByIds(categoryIds).stream()
                    .collect(Collectors.toMap(Category::getId, Function.identity()));
        }

        final List<IInventoryMonitorId> warehouseSkus = records.stream()
                .map(v -> InventoryMonitorId.of(v.getWarehouseNo(), v.getSpecNo())).collect(Collectors.toList());
        final Map<IInventoryMonitorId, InventoryMonitor> skuMonitorsMap = inventoryMonitorService
                .selectByMonitorIds(warehouseSkus);
        final Map<String, InventoryMonitor> warehouseMonitorsMap = inventoryMonitorService
                .selectByWarehouseNos(warehouseNos).stream()
                .collect(Collectors.toMap(InventoryMonitor::getWarehouseNo, Function.identity()));

        Map<Long, Brand> finalBrandsMap = brandsMap;
        Map<Long, Category> finalCategoriesMap = categoriesMap;
        return ResponseFactory.ofPage(wdtStockSpecIPage, v -> {
            final StockSpecVO stockSpecVO = WdtStockSpecAssembler.INST.persistModelToStockSpecVO(v);
            final Item item = itemsMap.get(v.getGoodsNo());
            if (item != null) {
                final Long brandId = item.getBrandId();
                stockSpecVO.setBrandId(brandId);
                final Brand brand = finalBrandsMap.get(brandId);
                if (brand != null) {
                    stockSpecVO.setBrandNo(brand.getSn());
                    stockSpecVO.setBrandName(brand.getName());
                }

                final Long categoryId = item.getCategoryId();
                final Category category = finalCategoriesMap.get(categoryId);
                if (category != null) {
                    stockSpecVO.setCategoryId(categoryId);
                    stockSpecVO.setCategoryName(category.getPath());
                }
            }

            final ItemSku itemSku = itemSkusMap.get(v.getSpecNo());
            if (itemSku != null) {
                stockSpecVO.setUnit(itemSku.getUnit());
                try {
                    itemSkuTradeStatisticService.lambdaQuery().eq(ItemSkuTradeStatistic::getItemCode, v.getGoodsNo())
                            .eq(ItemSkuTradeStatistic::getSkuCode, v.getSpecNo()).oneOpt().ifPresent(x -> {
                                if (x.getFirstTradeTime() != null && x.getFirstTradeTime() > 0) {
                                    stockSpecVO.setFirstSalesTime(DateUtil.toLocalDateTime(x.getFirstTradeTime()));
                                }
                            });
                } catch (Exception e) {
                    log.error("[仓库库存][大数据]从大数据获取首销时间异常:{}", e.getMessage(), e);
                }
            }

            final Warehouse warehouse = warehousesMap.get(v.getWarehouseNo());
            if (warehouse != null) {
                stockSpecVO.setWarehouseId(warehouse.getId());
                stockSpecVO.setWarehouseNo(warehouse.getNo());
                stockSpecVO.setWarehouseName(warehouse.getName());
                // stockSpecVO.setBusinessLine(warehouse.getBusinessLineList());
            }

            final InventoryMonitor inventoryMonitor = Optional
                    .ofNullable(skuMonitorsMap.get(InventoryMonitorId.of(v.getWarehouseNo(), v.getSpecNo())))
                    .orElse(warehouseMonitorsMap.get(v.getWarehouseNo()));
            if (inventoryMonitor != null) {
                stockSpecVO.setAlertStock(BigDecimal.valueOf(inventoryMonitor.getAlertThreshold()));
                stockSpecVO.setAlertStatus(inventoryMonitor.getStatus());
            }

            // 层级参数填充
            final List<CorpBizTypeDTO> levelDivisionVos = levelDivisionMap.get(stockSpecVO.getSkuCode());
            if (CollUtil.isEmpty(levelDivisionVos)) {
                stockSpecVO.setLevelDivisionVos(new LinkedList<>());
                stockSpecVO.setCoryTypeStr("");
                stockSpecVO.setBizTypeStr("");
            } else {
                stockSpecVO.setLevelDivisionVos(levelDivisionVos);
                final String coryStr = levelDivisionVos.stream()
                        .map(val -> IEnum.getEnumByValue(DivisionLevelValueEnum.class, val.getCorpType()).getDesc())
                        .collect(Collectors.joining(","));
                stockSpecVO.setCoryTypeStr(coryStr);
                final String bizStr = levelDivisionVos.stream().map(vo -> vo.getBizType().stream()
                        .map(sVal -> IEnum.getEnumByValue(DivisionLevelValueEnum.class, vo.getCorpType()).getDesc()
                                + "-" + IEnum.getEnumByValue(DivisionLevelValueEnum.class, sVal).getDesc())
                        .collect(Collectors.joining(","))).collect(Collectors.joining("."));
                stockSpecVO.setBizTypeStr(bizStr);
            }

            return stockSpecVO;
        });
    }

    @Override
    public PageResponse<AvailableStockSpecVO> availableStockQuery(StockSpecQuery query) {
        final IPage<WdtStockSpec> wdtStockSpecIPage = pageQueryStockSpecPO(query);
        final List<WdtStockSpec> records = wdtStockSpecIPage.getRecords();
        if (records.isEmpty()) {
            return ResponseFactory.emptyPage();
        }
        List<String> allSkuCodes = records.stream().map(WdtStockSpec::getSpecNo).distinct()
                .collect(Collectors.toList());
        List<InventoryMonitor> allInventoryMonitors = inventoryMonitorService.lambdaQuery()
                .in(InventoryMonitor::getSkuNo, allSkuCodes).eq(InventoryMonitor::getThresholdType, 1)
                .eq(InventoryMonitor::getStatus, 1).list();
        // 查询仓库级别的警戒库存
        List<InventoryMonitor> warehouseInventoryMonitors = inventoryMonitorService.lambdaQuery()
                .eq(InventoryMonitor::getSkuNo, "").ne(InventoryMonitor::getWarehouseNo, "")
                .eq(InventoryMonitor::getThresholdType, 1).eq(InventoryMonitor::getStatus, 1).list();
        Map<String, InventoryMonitor> warehouseMonitorMap = warehouseInventoryMonitors.stream()
                .collect(Collectors.toMap(InventoryMonitor::getWarehouseNo, Function.identity(), (v1, v2) -> v1));
        return ResponseFactory.ofPage(wdtStockSpecIPage, stockSpec -> {
            AvailableStockSpecVO availableStockSpecVO = WdtStockSpecAssembler.INST
                    .persistModelToAvailableStockSpecVO(stockSpec);
            InventoryMonitor warehouseMonitor = warehouseMonitorMap.get(stockSpec.getWarehouseNo());
            if (warehouseMonitor != null) {
                availableStockSpecVO.setWarnStock(BigDecimal.valueOf(warehouseMonitor.getAlertThreshold()));
            }
            Optional<InventoryMonitor> inventoryMonitor = allInventoryMonitors.stream()
                    .filter(monitor -> monitor.getSkuNo().equals(stockSpec.getSpecNo())
                            && monitor.getWarehouseNo().equals(stockSpec.getWarehouseNo()))
                    .findFirst();
            inventoryMonitor.ifPresent(
                    monitor -> availableStockSpecVO.setWarnStock(BigDecimal.valueOf(monitor.getAlertThreshold())));
            availableStockSpecVO.setWarnStock(inventoryMonitor.map(InventoryMonitor::getAlertThreshold)
                    .map(BigDecimal::new).orElse(BigDecimal.ZERO));
            BigDecimal allocableStock = availableStockSpecVO.getAvailableStock()
                    .subtract(availableStockSpecVO.getWarnStock()).setScale(0, RoundingMode.HALF_UP);
            availableStockSpecVO.setAllocableStock(NumberUtil.max(allocableStock, BigDecimal.ZERO));
            return availableStockSpecVO;
        });
    }

    @Override
    public List<AvailableStockSpecVO> getAvailableStocks(String skuCode) {
        return getAvailableStocks(Collections.singletonList(skuCode));
    }

    @Override
    public List<AvailableStockSpecVO> getAvailableStocks(Collection<String> skuCodes) {
        return getAvailableStocks(skuCodes, null);
    }

    @Override
    public List<AvailableStockSpecVO> getAvailableStocks(Collection<String> skuCodes,
            Collection<String> excludedWarehouseNos) {
        StockSpecQuery stockSpecQuery = new StockSpecQuery();
        stockSpecQuery.setPageSize(99999);
        stockSpecQuery.setShowAll(true);
        stockSpecQuery.setSkuCodes(new ArrayList<>(skuCodes));
        stockSpecQuery.setWarehouseState(1);
        stockSpecQuery.setExcludedWarehouseNos(excludedWarehouseNos);
        return availableStockQuery(stockSpecQuery).getData();
    }

    private IPage<WdtStockSpec> pageQueryStockSpecPO(StockSpecQuery query) {
        final Page<WdtStockSpec> emptyPage = new Page<>(query.getPageIndex(), query.getPageSize(), 0);
        if (!query.isShowAll()) {
            if (CollUtil.isEmpty(query.getCorpType())) {
                return emptyPage;
            }
        }

        final LambdaQueryChainWrapper<WdtStockSpec> queryWrapper = wdtStockSpecService.lambdaQuery();
        if (CollUtil.isNotEmpty(query.getIds())) {
            queryWrapper.in(WdtStockSpec::getId, query.getIds());
        }
        final HashSet<String> searchWarehouseNos = new HashSet<>();
        if (CollUtil.isNotEmpty(query.getWarehouseNos())) {
            searchWarehouseNos.addAll(query.getWarehouseNos());
        }
        if (CollUtil.isNotEmpty(query.getWarehouseIds())) {
            searchWarehouseNos.addAll(warehouseService.warehouseIdsToNos(query.getWarehouseIds()));
        }
        final WarehousePageQuery warehousePageQuery = new WarehousePageQuery();
        if (StringUtil.isNotBlank(query.getWarehouseName())) {
            warehousePageQuery.setName(query.getWarehouseName());
        }
        if (!searchWarehouseNos.isEmpty()) {
            warehousePageQuery.setNos(new ArrayList<>(searchWarehouseNos));
        }
        // 设置需要排除的仓库
        if (CollUtil.isNotEmpty(query.getExcludedWarehouseNos())) {
            warehousePageQuery.setExcludedNos(query.getExcludedWarehouseNos());
        }
        // 根据参数设置仓库状态过滤条件
        if (query.getWarehouseState() != null) {
            warehousePageQuery.setState(query.getWarehouseState());
        }
        warehousePageQuery.setPageSize(9999);
        final Page<Warehouse> warehousePage = warehouseService.pageQuery(warehousePageQuery);
        if (warehousePage.getRecords().isEmpty()) {
            return new Page<>(query.getPageIndex(), query.getPageSize(), 0);
        }
        final List<String> warehouseNosIn = warehousePage.getRecords().stream().map(Warehouse::getNo)
                .collect(Collectors.toList());
        queryWrapper.in(WdtStockSpec::getWarehouseNo, warehouseNosIn);
        queryWrapper.in(CollUtil.isNotEmpty(query.getSkuCodes()), WdtStockSpec::getSpecNo, query.getSkuCodes());
        queryWrapper.in(CollUtil.isNotEmpty(query.getSpuCodes()), WdtStockSpec::getGoodsNo, query.getSpuCodes());
        final HashSet<String> searchBrandNos = new HashSet<>();
        if (CollUtil.isNotEmpty(query.getBrandNos())) {
            searchBrandNos.addAll(query.getBrandNos());
        }
        if (CollUtil.isNotEmpty(query.getBrandIds())) {
            searchBrandNos.addAll(brandService.brandIdsToNos(query.getBrandIds()));
        }
        queryWrapper.in(CollUtil.isNotEmpty(searchBrandNos), WdtStockSpec::getBrandNo, searchBrandNos);
        final HashSet<String> itemCodesMatched = new HashSet<>();
        if (CollUtil.isNotEmpty(query.getCategoryIds())) {
            final List<Item> itemsMatchByCategoryIds = itemService.lambdaQuery()
                    .in(Item::getCategoryId, query.getCategoryIds())
                    .select(Item::getCode, Item::getProviderSpecifiedCode).list();
            if (itemsMatchByCategoryIds.isEmpty()) {
                return emptyPage;
            }
            for (Item item : itemsMatchByCategoryIds) {
                itemCodesMatched.add(item.getCode());
                itemCodesMatched.add(item.getProviderSpecifiedCode());
            }
        }
        queryWrapper.in(CollUtil.isNotEmpty(itemCodesMatched), WdtStockSpec::getGoodsNo, itemCodesMatched);
        if (CollUtil.isNotEmpty(query.getItemNames())) {
            queryWrapper.and(q -> {
                for (String itemName : query.getItemNames()) {
                    q.or().like(WdtStockSpec::getGoodsName, itemName);
                }
            });
        }
        // 只查询出正品。次品不考虑
        if (!query.isIncludeDefect()) {
            queryWrapper.eq(WdtStockSpec::getDefect, 0);
        }
        // 不查询出可用库存为0的记录
        if (!query.isIncludeStockZero()) {
            queryWrapper.ne(WdtStockSpec::getAvailableStock, 0);
        }

        if (!query.isShowAll()) {
            final String corpTypeStr = query.getCorpType().stream().map(it -> String.format("(%s,%s)", 0, it))
                    .collect(Collectors.joining(","));

            if (CollUtil.isNotEmpty(query.getBizType())) {
                final String bizTypeStr = query.getBizType() != null ? query.getBizType().stream()
                        .map(it -> String.format("(%s,%s)", 1, it)).collect(Collectors.joining(",")) : null;
                queryWrapper.apply(String.format("`goods_no` IN (SELECT bld1.`biz_code`\n"
                        + "                     FROM `biz_level_division` AS `bld1`\n"
                        + "                              JOIN `biz_level_division` AS `bld2`\n"
                        + "                                   ON `bld1`.`type` = `bld2`.`type` AND `bld1`.`biz_id` = `bld2`.`biz_id` AND\n"
                        + "                                      (`bld2`.`level`, `bld2`.`level_val`) IN (%s)\n"
                        + "                     WHERE `bld1`.`type` = 0\n"
                        + "                       AND (`bld1`.`level`, `bld1`.`level_val`) IN (%s))", corpTypeStr,
                        bizTypeStr));
            } else {
                queryWrapper.apply(String.format(
                        "`goods_no` IN (SELECT bld1.`biz_code`\n"
                                + "                     FROM `biz_level_division` AS `bld1`\n"
                                + "                     WHERE `bld1`.`type` = 0\n"
                                + "                       AND (`bld1`.`level`, `bld1`.`level_val`) IN (%s))",
                        corpTypeStr));
            }
        }
        setPageQueryStockSpecOrderBy(query, queryWrapper);
        final IPage<WdtStockSpec> page = query.getPage();
        queryWrapper.page(page);
        return page;
    }

    @SuppressWarnings("unchecked")
    private static void setPageQueryStockSpecOrderBy(StockSpecQuery query,
            LambdaQueryChainWrapper<WdtStockSpec> queryWrapper) {
        if (StringUtil.isNotEmpty(query.getOrderBy())) {
            final boolean orderDirectionIsAsc = Objects.equals(query.getOrderDirection(), ASC);
            switch (query.getOrderBy()) {
            case "stockNum":
                queryWrapper.orderBy(true, orderDirectionIsAsc, WdtStockSpec::getStockNum);
                break;
            case "availableSendStock":
                queryWrapper.orderBy(true, orderDirectionIsAsc, WdtStockSpec::getAvailableSendStock);
                break;
            case "availableStock":
                queryWrapper.orderBy(true, orderDirectionIsAsc, WdtStockSpec::getAvailableStock);
                break;
            }
        } else if (CollUtil.isNotEmpty(query.getSkuCodes()) && query.getSkuCodes().size() == 1) {
            queryWrapper.orderByDesc(WdtStockSpec::getAvailableStock);
        } else {
            queryWrapper.orderByDesc(WdtStockSpec::getGoodsNo).orderByAsc(WdtStockSpec::getSpecNo);
        }
    }

    public static void main(String[] args) {
        final SetInventoryMonitorCmd parse = JsonUtil.parse(
                "{\"setThresholdCommands\":[{\"warehouseNo\":\"CK0001201\",\"alertThreshold\":10}]}",
                SetInventoryMonitorCmd.class);
        System.out.println(parse);
    }

    @Override
    @Transactional
    public SingleResponse<BatchResultStat> setInventoryMonitor(SetInventoryMonitorCmd cmd) {
        final BatchResultStat batchResultStat = new BatchResultStat();
        final List<IInventoryMonitorId> setThresholdCommands = new ArrayList<>(cmd.getSetThresholdCommands());
        final Map<IInventoryMonitorId, InventoryMonitor> monitorsMap = inventoryMonitorService
                .selectByMonitorIds(setThresholdCommands);

        for (SetMonitorThresholdCmd setMonitorThresholdCmd : cmd.getSetThresholdCommands()) {
            final InventoryMonitor inventoryMonitor = monitorsMap.get(setMonitorThresholdCmd.asId());
            if (inventoryMonitor != null) {
                inventoryMonitor.setAlertThreshold(setMonitorThresholdCmd.getAlertThreshold());
                inventoryMonitor.setStatus(setMonitorThresholdCmd.getStatus());
                inventoryMonitorService.updateById(inventoryMonitor);
                batchResultStat.addUpdateObject();
            } else {
                final InventoryMonitor newInventoryMonitor = new InventoryMonitor();
                newInventoryMonitor.setWarehouseNo(setMonitorThresholdCmd.getWarehouseNo());
                newInventoryMonitor.setSkuNo(setMonitorThresholdCmd.getSkuNo());
                newInventoryMonitor.setThresholdType(1);
                newInventoryMonitor.setAlertThreshold(setMonitorThresholdCmd.getAlertThreshold());
                newInventoryMonitor.setStatus(setMonitorThresholdCmd.getStatus());
                inventoryMonitorService.save(newInventoryMonitor);
                batchResultStat.addNewObject();
            }
            if (setMonitorThresholdCmd.isWarehouseDimension()) {
                removeMonitorsOfSkuDimension(setMonitorThresholdCmd.getWarehouseNo());
            }
        }
        return SingleResponse.of(batchResultStat);
    }

    /**
     * 移除指定仓库下全部SKU维度的库存警戒
     *
     * @param warehouseNo 仓库编号
     */
    private void removeMonitorsOfSkuDimension(String warehouseNo) {
        final List<Long> removeAllSkuMonitorIds = inventoryMonitorService.lambdaQuery()
                .eq(InventoryMonitor::getWarehouseNo, warehouseNo).ne(InventoryMonitor::getSkuNo, "")
                .select(InventoryMonitor::getId).list().stream().map(InventoryMonitor::getId)
                .collect(Collectors.toList());
        inventoryMonitorService.removeByIdsWithTime(removeAllSkuMonitorIds);
    }

    @Override
    public SingleResponse<WarehouseStockSpecStatistic> statistics(String warehouseNo) {
        final SingleResponse<Map<String, WarehouseStockSpecStatistic>> statistics = statistics(
                Collections.singletonList(warehouseNo));
        final Map<String, WarehouseStockSpecStatistic> data = statistics.getData();
        return SingleResponse.of(data.get(warehouseNo));
    }

    @Override
    public SingleResponse<Map<String, WarehouseStockSpecStatistic>> statistics(List<String> warehouseNos) {
        if (CollectionUtil.isEmpty(warehouseNos)) {
            return SingleResponse.of(Collections.emptyMap());
        }
        final WdtStockSpecStatisticQuery query = new WdtStockSpecStatisticQuery();
        query.setWarehouseNos(warehouseNos);
        return SingleResponse.of(wdtStockSpecService.statistics(query));
    }

    @Override
    public SingleResponse<Map<String, WarehouseStockSpecStatistic>> statistics(WdtStockSpecStatisticQuery query) {
        return SingleResponse.of(wdtStockSpecService.statistics(query));
    }

    @Override
    public SingleResponse<ExportTask> export(StockSpecQuery query) {
        final ExportTask exportTask = exportManager.export(ExportTaskType.STOCK_SPEC, StockSpecExportVO.class,
                pageIndex -> {
                    query.setPageIndex(pageIndex);
                    query.setPageSize(100);
                    return ResponseFactory.ofPage(query(query),
                            WdtStockSpecAssembler.INST::stockSpecVOToStockSpecExportVO);
                }, (task, excelWriterBuilder) -> task.setParams(JsonUtil.toJson(query)));
        return SingleResponse.of(exportTask);
    }

    @Override
    public Response fetchBySpecNos(Collection<String> specNos) {
        if (!CollUtil.isEmpty(specNos)) {
            wdtStockSpecFetchService.fetchBySpecNos(specNos);
        }
        return Response.buildSuccess();
    }

    @XxlJob(value = "inventoryMonitorJob")
    @XxlJobAutoRegister(cron = "0 0/1 * * * ?", jobDesc = "库存警戒监控", author = "徴乌")
    public void runInventoryMonitorJob() {
        try {
            runInventoryMonitor();
        } catch (Exception e) {
            log.error("库存警戒监控执行失败", e);
        }
    }

    @Override
    public Response runInventoryMonitor() {
        Long cursor = 0L;
        final Map<Long, Set<InventoryMonitorAlertVO>> notices = new LinkedHashMap<>();
        final List<Long> adminUids = Optional.ofNullable(stockSpecConfig.getAdminUids())
                .orElseGet(Collections::emptyList);
        for (;;) {
            final List<InventoryMonitorAlertVO> inventoryMonitorAlertVOS = inventoryMonitorService
                    .selectAbsoluteThresholdAlertList(100, cursor);
            log.info("[库存警戒]查询到库存小于监控阈值的SKU数量:{} cursor:{}", inventoryMonitorAlertVOS.size(), cursor);
            if (inventoryMonitorAlertVOS.isEmpty()) {
                break;
            }
            // noinspection OptionalGetWithoutIsPresent
            cursor = inventoryMonitorAlertVOS.stream()
                    .max(Comparator.comparing(InventoryMonitorAlertVO::getInventoryMonitorId))
                    .map(InventoryMonitorAlertVO::getInventoryMonitorId).get();
            final List<String> skuNos = inventoryMonitorAlertVOS.stream().map(InventoryMonitorAlertVO::getSkuNo)
                    .collect(Collectors.toList());
            final List<ItemSku> itemSkus = itemSkuService.selectByMixCodes(skuNos);
            if (CollUtil.isEmpty(itemSkus)) {
                continue;
            }
            final Set<Long> itemIds = itemSkus.stream().map(ItemSku::getItemId).collect(Collectors.toSet());
            final Map<Long, Long> buyerMapByItemId = itemProcurementGateway.getBuyerUidList(new ArrayList<>(itemIds));
            final HashMap<String, Long> buyerMap = new HashMap<>();
            for (ItemSku itemSku : itemSkus) {
                if (buyerMapByItemId.containsKey(itemSku.getItemId())) {
                    buyerMap.put(itemSku.getSkuCode(), buyerMapByItemId.get(itemSku.getItemId()));
                    buyerMap.put(itemSku.getProviderSpecifiedCode(), buyerMapByItemId.get(itemSku.getItemId()));
                }
            }

            for (InventoryMonitorAlertVO inventoryMonitorAlertVO : inventoryMonitorAlertVOS) {
                List<PurchaseOrder> purchaseOrders = purchaseOrderService
                        .listBySkuCode(inventoryMonitorAlertVO.getSkuNo());
                if (purchaseOrders.stream()
                        .anyMatch(v -> Arrays
                                .asList(PurchaseOrderState.FINISHED.getValue(), PurchaseOrderState.CLOSED.getValue())
                                .contains(v.getState()))) {
                    continue;
                }

                for (Long adminUid : adminUids) {
                    addNotice(inventoryMonitorAlertVO, notices, adminUid);
                }
                final Long buyerId = buyerMap.get(inventoryMonitorAlertVO.getSkuNo());
                if (buyerId != null) {
                    addNotice(inventoryMonitorAlertVO, notices, buyerId);
                    final DepartmentHeadBriefVO staffDepartmentHeadBriefVO = staffService
                            .getStaffDepartmentHeadBriefVO(buyerId);
                    if (staffDepartmentHeadBriefVO != null) {
                        if (staffDepartmentHeadBriefVO.getLeader() != null) {
                            addNotice(inventoryMonitorAlertVO, notices,
                                    staffDepartmentHeadBriefVO.getLeader().getUserId());
                        } else if (staffDepartmentHeadBriefVO.getManager() != null) {
                            addNotice(inventoryMonitorAlertVO, notices,
                                    staffDepartmentHeadBriefVO.getManager().getUserId());
                        }
                    }
                }
            }
        }
        if (notices.isEmpty()) {
            log.info("[库存警戒]没有需要报警的SKU");
        } else {
            log.info("[库存警戒]待发送通知的用户共{}名", notices.size());
            notices.forEach((buyerId, alertVOS) -> {
                final StaffBrief receiver = StaffAssembler.INST.toStaffBrief(buyerId);
                if (receiver == null) {
                    return;
                }
                final HashMap<String, Object> variables = new HashMap<>();
                final List<String> skuNoList = alertVOS.stream().map(InventoryMonitorAlertVO::getSkuNo)
                        .collect(Collectors.toList());
                final String skuNoStr = String.join("、", skuNoList);
                final List<Long> stockSpecIds = alertVOS.stream().map(InventoryMonitorAlertVO::getStockSpecId)
                        .filter(Objects::nonNull).collect(Collectors.toList());

                List<String> cc = new LinkedList<>();
                itemGateway.queryItemBatchBySkuCodes(skuNoList).forEach((skuCode, itemVal) -> cc
                        .add(StrUtil.format("{}{}", Objects.isNull(itemVal) ? "未知商品" : itemVal.getName(), skuCode)));
                String skuTitle = String.join("、", cc);
                variables.put("skuTitle", skuTitle);

                // if (alertVOS.size() > 2) {
                // variables.put(
                // "skuTitle",
                // alertVOS.stream()
                // .map(InventoryMonitorAlertVO::getSkuNo)
                // .filter(StringUtil::isNotBlank)
                // .limit(10)
                // .collect(Collectors.joining(","))
                // + "...");
                // } else {
                // variables.put("skuTitle", skuNoStr);
                // }

                variables.put("skuNos", skuNoStr);
                variables.put("receiver", receiver.getQwUserId());
                variables.put("ids", stockSpecIds.stream().map(Object::toString).collect(Collectors.joining(",")));
                qyMsgSendService.send(RemindType.TO_DO_REMINDER, MsgTemplateCode.STOCK_SPEC_ALERT, variables);
                log.info("[库存警戒]通知用户'{}'SKU库存紧张:{}", receiver.getNickname(), skuNoStr);
            });
            log.info("[库存警戒]成功发送{}条，库存警戒报警完成", notices.size());
        }
        return Response.buildSuccess();
    }

    private static void addNotice(InventoryMonitorAlertVO inventoryMonitorAlertVO,
            Map<Long, Set<InventoryMonitorAlertVO>> notices, Long buyerId) {
        notices.compute(buyerId, (key, value) -> {
            if (value == null) {
                value = new LinkedHashSet<>();
            }
            value.add(inventoryMonitorAlertVO);
            return value;
        });
    }
}
