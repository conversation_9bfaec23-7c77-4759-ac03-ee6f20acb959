package com.daddylab.supplier.item.application.platformItem.tasks;

import com.daddylab.job.core.context.XxlJobHelper;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.application.staff.StaffService;
import com.daddylab.supplier.item.domain.staff.vo.DadStaffVO;
import com.daddylab.supplier.item.infrastructure.email.EmailService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Buyer;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Item;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemProcurement;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItem;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Provider;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ContractStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBuyerService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemProcurementService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPlatformItemService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IProviderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerContract;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerContractQuery;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
@ConfigurationProperties(prefix = "contract-status-sync")
public class ContractStatusSyncTask {
  @Resource private PartnerFeignClient partnerFeignClient;

  @Resource private IPlatformItemService platformItemService;

  @Resource private IItemService itemService;

  @Resource private IProviderService providerService;

  @Resource private IItemProcurementService itemProcurementService;

  @Resource private IBuyerService buyerService;

  @Resource private ICategoryService categoryService;

  @Resource private StaffService staffService;

  @Resource private EmailService emailService;

  /** 强制指定一个邮件接收人，用于调试 */
  private @Getter @Setter String forceRecipient;

  // 邮件提醒相关数据结构
  private static class ExpiringContractInfo {
    Item item;
    Category category;
    List<PlatformItem> platformItems;
    PartnerContract contract;
    String buyerNickname;
    String buyerEmail;
    long daysDiff;

    ExpiringContractInfo(
        Item item,
        Category category,
        List<PlatformItem> platformItems,
        PartnerContract contract,
        String buyerNickname,
        String buyerEmail,
        long daysDiff) {
      this.item = item;
      this.category = category;
      this.platformItems = platformItems;
      this.contract = contract;
      this.buyerNickname = buyerNickname;
      this.buyerEmail = buyerEmail;
      this.daysDiff = daysDiff;
    }
  }

  @XxlJob("ContractStatusSyncTask:sync")
  @XxlJobAutoRegister(cron = "0 0 10 * * ? *", author = "徵乌", jobDesc = "合同状态同步")
  public void sync() {
    String jobParam = XxlJobHelper.getJobParam();
    log.info("开始同步合同状态:{}", jobParam);

    // 查询全部合同
    PartnerContractQuery query = new PartnerContractQuery();
    Rsp<List<PartnerContract>> contractResp = partnerFeignClient.contractQuery(query);
    if (contractResp == null || contractResp.getData() == null) {
      log.warn("获取合同数据失败");
      return;
    }
    List<PartnerContract> allContracts = contractResp.getData();
    Map<Long, List<PartnerContract>> allContractGroupedMap =
        allContracts.stream().collect(Collectors.groupingBy(PartnerContract::getOrganizationId));

    // 查询全部平台商品
    List<PlatformItem> allPlatformItems = platformItemService.list();
    Map<Long, List<PlatformItem>> platformItemGroupedMap =
        allPlatformItems.stream().collect(Collectors.groupingBy(PlatformItem::getItemId));

    // 查询全部后端商品
    List<Item> allItems = itemService.list();
    // 根据ERP供应商ID分组
    Map<Long, List<Item>> itemGroupedMap =
        allItems.stream().collect(Collectors.groupingBy(Item::getProviderId));

    // 查询全部供应商
    List<Provider> allProviders = providerService.list();
    // 根据P系统供应商ID分组
    Map<Long, List<Provider>> providerGroupedMap =
        allProviders.stream().collect(Collectors.groupingBy(Provider::getPartnerProviderId));

    // 查询全部商品采购信息
    List<ItemProcurement> allItemProcurements = itemProcurementService.list();
    Map<Long, ItemProcurement> itemProcurementMap =
        allItemProcurements.stream()
            .collect(
                Collectors.toMap(
                    ItemProcurement::getItemId,
                    procurement -> procurement,
                    (existing, replacement) -> existing));

    // 查询全部采购员
    List<Buyer> allBuyers = buyerService.list();
    Map<Long, Buyer> buyerMap =
        allBuyers.stream()
            .collect(
                Collectors.toMap(
                    Buyer::getId, buyer -> buyer, (existing, replacement) -> existing));

    // 查询全部品类
    List<Category> allCategories = categoryService.list();
    Map<Long, Category> categoryMap =
        allCategories.stream()
            .collect(
                Collectors.toMap(
                    Category::getId, category -> category, (existing, replacement) -> existing));

    Long currentTime = DateUtil.currentTime();

    // 存储需要发送邮件提醒的信息
    Map<String, List<ExpiringContractInfo>> emailNotificationMap = new HashMap<>();

    allContractGroupedMap.forEach(
        (organizationId, contracts) -> {
          if (StringUtil.isNotBlank(jobParam) && !jobParam.equals(organizationId.toString())) {
            return;
          }
          log.info("[同步合同状态]开始同步，P系统供应商ID:{}", organizationId);
          Map<ContractStatus, List<PartnerContract>> contractsByStatus =
              groupContractsByStatus(contracts, currentTime);
          ContractStatus finalContractStatus = determineContractStatus(contracts, currentTime);
          log.info("[同步合同状态]最终合同状态:{}，P系统供应商ID:{}", finalContractStatus, organizationId);

          // 获取合同对应的供应商
          List<Provider> providers = providerGroupedMap.get(organizationId);
          if (providers == null || providers.isEmpty()) {
            log.warn("[同步合同状态]未找到对应的供应商，P系统供应商ID:{}", organizationId);
            return;
          }

          // 获取合同对应的后端商品
          ArrayList<PlatformItem> toUpdateObjs = new ArrayList<>();
          for (Provider provider : providers) {
            List<Item> items = itemGroupedMap.get(provider.getId());
            if (items == null || items.isEmpty()) {
              log.warn("[同步合同状态]未找到对应的后端商品，供应商ID:{}", provider.getId());
              continue;
            }
            for (Item item : items) {
              List<PlatformItem> platformItems = platformItemGroupedMap.get(item.getId());
              if (platformItems == null || platformItems.isEmpty()) {
                log.debug("[同步合同状态]未找到对应的平台商品，后端商品ID:{}", item.getId());
                continue;
              }

              // 收集需要邮件提醒的信息
              if (finalContractStatus == ContractStatus.EXPIRING
                  || finalContractStatus == ContractStatus.INVALID) {
                collectExpiringContractInfo(
                    contractsByStatus,
                    item,
                    categoryMap,
                    platformItems,
                    itemProcurementMap,
                    buyerMap,
                    emailNotificationMap,
                    currentTime,
                    finalContractStatus);
              }

              for (PlatformItem platformItem : platformItems) {
                PlatformItem updateObj = new PlatformItem();
                updateObj.setId(platformItem.getId());
                updateObj.setContractStatus(finalContractStatus);
                toUpdateObjs.add(updateObj);
              }
            }
          }
          if (!toUpdateObjs.isEmpty()) {
            platformItemService.updateBatchById(toUpdateObjs);
            log.info("[同步合同状态]更新平台商品合同状态，数量:{}", toUpdateObjs.size());
          }
        });

    // 发送邮件提醒
    sendEmailNotifications(emailNotificationMap);

    log.info("合同状态同步完成");
  }

  /** 计算单个合同的状态 */
  private ContractStatus calculateSingleContractStatus(PartnerContract contract, Long currentTime) {
    // 先判断合同是否有效
    if ("cooperate".equals(contract.getContractType())) {
      // 采购合同使用 status 字段（状态 1 待提交，2 审核中，3 审核通过 4 审核不通过 5 已撤回 6 已过期 7 已失效 （类型为采购合同时有效））
      if (contract.getStatus() == null
          || contract.getStatus() == 1
          || contract.getStatus() == 2
          || contract.getStatus() == 4
          || contract.getStatus() == 5) {
        return ContractStatus.NOT_ASSOCIATED;
      }
      // 已过期或已失效
      if (contract.getStatus() == 6 || contract.getStatus() == 7) {
        return ContractStatus.INVALID;
      }
    } else if ("customized_item".equals(contract.getContractType())) {
      // 定制品合同使用 applyStatus 字段（审核状态 1待提交，2审核中(待审核)，3审核通过，4已拒绝，5已撤回 （类型为定制品合同时有效））
      if (contract.getApplyStatus() == null || contract.getApplyStatus() == 1
          || contract.getApplyStatus() == 2
          || contract.getApplyStatus() == 4
          || contract.getApplyStatus() == 5) {
        return ContractStatus.NOT_ASSOCIATED;
      }
    }

    // 检查时间范围
    if (contract.getStartTime() == null || contract.getEndTime() == null) {
      return ContractStatus.NOT_ASSOCIATED;
    }

    // 合同已过期
    if (currentTime > contract.getEndTime()) {
      return ContractStatus.INVALID;
    }

    // 合同未生效
    if (currentTime < contract.getStartTime()) {
      return ContractStatus.NOT_ASSOCIATED;
    }

    // 判断是否为临期合同（剩余时间小于30天）
    if (contract.getEndTime() - currentTime < 30L * 24 * 3600) {
      return ContractStatus.EXPIRING;
    }

    // 合同正常生效中
    return ContractStatus.EFFECTIVE;
  }

  /** 根据规则选择最合适的合同进行通知 */
  private PartnerContract selectMostRecentContractForNotification(
      Map<ContractStatus, List<PartnerContract>> contractsByStatus,
      ContractStatus finalContractStatus,
      Long currentTime) {

    if (finalContractStatus == ContractStatus.EXPIRING) {
      // 如果是临期状态，选择剩余时间最长的合同（结束时间最晚的）
      List<PartnerContract> expiringContracts =
          contractsByStatus.getOrDefault(ContractStatus.EXPIRING, new ArrayList<>());
      if (expiringContracts.isEmpty()) {
        return null;
      }

      // 按结束时间降序排序，选择结束时间最晚的（剩余时间最长）
      return expiringContracts.stream()
          .max(Comparator.comparingLong(PartnerContract::getEndTime))
          .orElse(null);

    } else if (finalContractStatus == ContractStatus.INVALID) {
      // 如果是已失效状态，选择失效时间最短的合同（结束时间最接近当前的）
      List<PartnerContract> invalidContracts =
          contractsByStatus.getOrDefault(ContractStatus.INVALID, new ArrayList<>());
      if (invalidContracts.isEmpty()) {
        return null;
      }

      // 按结束时间降序排序，选择结束时间最接近当前的（失效时间最短）
      return invalidContracts.stream()
          .max(Comparator.comparingLong(PartnerContract::getEndTime))
          .orElse(null);
    }

    return null;
  }

  /** 按状态分组合同 */
  private Map<ContractStatus, List<PartnerContract>> groupContractsByStatus(
      List<PartnerContract> contracts, Long currentTime) {
    Map<ContractStatus, List<PartnerContract>> statusMap = new HashMap<>();

    for (PartnerContract contract : contracts) {
      ContractStatus status = calculateSingleContractStatus(contract, currentTime);
      statusMap.computeIfAbsent(status, k -> new ArrayList<>()).add(contract);
    }

    return statusMap;
  }

  /** 确定合同状态 */
  protected ContractStatus determineContractStatus(
      List<PartnerContract> contracts, Long currentTime) {
    Map<ContractStatus, List<PartnerContract>> contractsByStatus =
        groupContractsByStatus(contracts, currentTime);
    if (log.isDebugEnabled()) {
      contractsByStatus.forEach(
          (status, group) -> {
            log.info(
                "[同步合同状态]合同状态:{}，数量:{}，合同编号:{}",
                status,
                group.size(),
                group.stream().map(PartnerContract::getContractNo).collect(Collectors.toList()));
          });
    }

    // 根据各个状态的合同数量确定最终状态
    boolean hasEffective =
        !contractsByStatus.getOrDefault(ContractStatus.EFFECTIVE, new ArrayList<>()).isEmpty();
    boolean hasExpiring =
        !contractsByStatus.getOrDefault(ContractStatus.EXPIRING, new ArrayList<>()).isEmpty();
    boolean hasInvalid =
        !contractsByStatus.getOrDefault(ContractStatus.INVALID, new ArrayList<>()).isEmpty();

    // 确定最终合同状态
    if (hasEffective) {
      return ContractStatus.EFFECTIVE; // 存在生效中且非临期的合同
    } else if (hasExpiring) {
      return ContractStatus.EXPIRING; // 关联了临期合同且未关联其他生效非临期合同
    } else if (hasInvalid) {
      return ContractStatus.INVALID; // 关联了合同，但是所有合同都已过期或已失效
    } else {
      return ContractStatus.NOT_ASSOCIATED; // 没有关联任何合同（默认状态）
    }
  }

  /** 收集即将过期的合同信息用于邮件提醒 */
  private void collectExpiringContractInfo(
      Map<ContractStatus, List<PartnerContract>> contractsByStatus,
      Item item,
      Map<Long, Category> categoryMap,
      List<PlatformItem> platformItems,
      Map<Long, ItemProcurement> itemProcurementMap,
      Map<Long, Buyer> buyerMap,
      Map<String, List<ExpiringContractInfo>> emailNotificationMap,
      Long currentTime,
      ContractStatus finalContractStatus) {

    // 根据finalContractStatus选择最合适的合同进行通知
    PartnerContract selectedContract =
        selectMostRecentContractForNotification(
            contractsByStatus, finalContractStatus, currentTime);

    if (selectedContract == null) {
      log.warn("[同步合同状态]未找到合适的合同进行通知，商品ID:{}", item.getId());
      return;
    }

    long daysDiff = (selectedContract.getEndTime() - currentTime) / (24 * 3600);
    log.info("[同步合同状态]商品ID:{}，合同ID:{}，剩余天数:{}", item.getId(), selectedContract.getId(), daysDiff);

    // 获取商品采购信息
    ItemProcurement itemProcurement = itemProcurementMap.get(item.getId());
    if (itemProcurement == null) {
      log.error("[同步合同状态]未找到商品采购信息，商品ID:{}", item.getId());
      return;
    }

    // 获取采购员
    Buyer buyer = buyerMap.get(itemProcurement.getBuyerId());
    if (buyer == null || buyer.getUserId() == null) {
      log.error("[同步合同状态]未找到采购员信息，商品ID:{}", item.getId());
      return;
    }

    // 获取采购员花名和邮箱
    try {
      DadStaffVO staff = staffService.getStaff(buyer.getUserId());
      if (staff == null) {
        log.error("[同步合同状态]未找到采购员信息，商品ID:{}", item.getId());
        return;
      }
      String buyerNickname = staff.getNickname();
      String buyerEmail = staff.getEmail();
      if (buyerNickname == null || buyerNickname.isEmpty()) {
        log.error("[同步合同状态]采购员花名为空，商品ID:{}", item.getId());
        return;
      }
      if (buyerEmail == null || buyerEmail.isEmpty()) {
        log.error("[同步合同状态]采购员邮箱为空，商品ID:{}", item.getId());
        return;
      }

      // 获取品类信息
      Category category = categoryMap.get(item.getCategoryId());

      // 按采购员花名分组存储
      emailNotificationMap
          .computeIfAbsent(buyerNickname, k -> new ArrayList<>())
          .add(
              new ExpiringContractInfo(
                  item,
                  category,
                  platformItems,
                  selectedContract,
                  buyerNickname,
                  buyerEmail,
                  daysDiff));
      log.info(
          "[同步合同状态]收集邮件提醒信息成功，商品ID:{}，合同ID:{}，剩余天数:{}，采购员花名:{}，采购员邮箱:{}",
          item.getId(),
          selectedContract.getId(),
          daysDiff,
          buyerNickname,
          buyerEmail);
    } catch (Exception e) {
      log.warn("获取采购员信息失败，buyerUserId: {}", buyer.getUserId(), e);
    }
  }

  /** 发送邮件提醒 */
  private void sendEmailNotifications(
      Map<String, List<ExpiringContractInfo>> emailNotificationMap) {
    LocalDate currentDate = LocalDate.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    String dateString = currentDate.format(formatter);

    for (Map.Entry<String, List<ExpiringContractInfo>> entry : emailNotificationMap.entrySet()) {
      String buyerNickname = entry.getKey();
      List<ExpiringContractInfo> contractInfos = entry.getValue();

      // 获取收件人邮箱（使用第一个合同信息中的邮箱）
      String recipientEmail = contractInfos.get(0).buyerEmail;

      // 构建邮件标题
      String subject = String.format("商品合同临期提醒【%s-%s】", buyerNickname, dateString);

      // 构建邮件正文（HTML格式）
      StringBuilder content = new StringBuilder();
      content.append("<!DOCTYPE html>\n<html><body>");
      content.append("<p>").append(buyerNickname).append("你好，</p>");
      content.append("<p>截至").append(dateString).append("，你所负责的以下商品关联合同已临期或到期，请及时处理！</p>");
      content.append("<table border='1' cellpadding='5' cellspacing='0'>");
      content.append(
          "<tr><th>商品名称</th><th>P系统款号</th><th>合同状态</th><th>商品品类</th><th>关联平台商品数</th><th>关联平台在售商品数</th></tr>");

      // 表格内容
      for (ExpiringContractInfo info : contractInfos) {
        String categoryName = info.category != null ? info.category.getName() : "";
        long platformItemCount = info.platformItems.size();
        long onSaleCount =
            info.platformItems.stream()
                .mapToLong(
                    platformItem ->
                        platformItem.getStatus() != null
                                && platformItem.getStatus() == PlatformItemStatus.ON_SALE
                            ? 1
                            : 0)
                .sum();

        String statusStr;
        if (info.daysDiff < 0) {
          statusStr = String.format("已失效%d天", Math.abs(info.daysDiff));
        } else {
          statusStr = String.format("剩余%d天", info.daysDiff);
        }

        content
            .append("<tr>")
            .append("<td>")
            .append(info.item.getName() != null ? info.item.getName() : "")
            .append("</td>")
            .append("<td>")
            .append(
                info.item.getPartnerProviderItemSn() != null
                    ? info.item.getPartnerProviderItemSn()
                    : "")
            .append("</td>")
            .append("<td>")
            .append(statusStr)
            .append("</td>")
            .append("<td>")
            .append(categoryName)
            .append("</td>")
            .append("<td>")
            .append(platformItemCount)
            .append("</td>")
            .append("<td>")
            .append(onSaleCount)
            .append("</td>")
            .append("</tr>");
      }

      content.append("</table>");
      content.append("</body></html>");

      try {
        // 发送邮件
        if (StringUtil.isNotBlank(this.forceRecipient)) {
          emailService.sendHtmlMail(
              this.forceRecipient,
              String.format("%s（to: %s）", subject, recipientEmail),
              content.toString());
        } else {
          emailService.sendSimpleMail(recipientEmail, subject, content.toString());
        }
        log.info("发送邮件提醒成功：收件人={}, 标题={}", recipientEmail, subject);
      } catch (Exception e) {
        log.error("发送邮件提醒失败：收件人={}, 标题={}", recipientEmail, subject, e);
      }
    }
  }
}
