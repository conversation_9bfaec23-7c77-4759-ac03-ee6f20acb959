package com.daddylab.supplier.item.application.stockWarn;

import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 库存告警定时任务
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Slf4j
@Component
public class StockWarnTask {

    @Autowired
    private StockWarnApplicationService stockWarnApplicationService;

    /**
     * 库存告警检查任务
     * 每天14:30执行
     */
    @XxlJob("StockWarnTask:checkStockWarn")
    @XxlJobAutoRegister(cron = "0 30 14 * * ? *", author = "Claude", jobDesc = "库存告警检查任务")
    public void checkStockWarn() {
        log.info("[库存告警检查任务] 开始执行");
        try {
            stockWarnApplicationService.executeStockWarnCheck();
            log.info("[库存告警检查任务] 执行完成");
        } catch (Exception e) {
            log.error("[库存告警检查任务] 执行失败", e);
            throw e;
        }
    }

    /**
     * 库存告警邮件发送任务
     * 每天15:00执行
     */
    @XxlJob("StockWarnTask:sendStockWarnEmail")
    @XxlJobAutoRegister(cron = "0 0 15 * * ? *", author = "Claude", jobDesc = "库存告警邮件发送任务")
    public void sendStockWarnEmail() {
        log.info("[库存告警邮件发送任务] 开始执行");
        try {
            stockWarnApplicationService.executeStockWarnEmailSend();
            log.info("[库存告警邮件发送任务] 执行完成");
        } catch (Exception e) {
            log.error("[库存告警邮件发送任务] 执行失败", e);
            throw e;
        }
    }
}
