package com.daddylab.supplier.item.application.ItemAi.job;

import cn.hutool.core.util.StrUtil;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.application.ItemAi.dto.MarketAnalysisStatus;
import com.daddylab.supplier.item.application.ItemAi.service.ItemAiAnalysisBizService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemMarketAnalysisRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemMarketAnalysisRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2025年09月23日 10:21
 */
@Component
@Slf4j
public class ItemAiScheduleTask {

  @Autowired private IItemMarketAnalysisRecordService itemMarketAnalysisRecordService;
  @Autowired private ItemAiAnalysisBizService itemAiAnalysisBizService;

  @XxlJob("ItemAiScheduleTaskReportTech")
  @XxlJobAutoRegister(cron = "0 0 */1 * * ?", author = "七喜", jobDesc = "[商品信息AI]同步上报 TECH")
  public void resport() {

    Map<Long, ItemMarketAnalysisRecord> resultMap =
        itemMarketAnalysisRecordService
            .lambdaQuery()
            .eq(ItemMarketAnalysisRecord::getStatus, MarketAnalysisStatus.END.getCode())
            .and(
                wq ->
                    wq.isNotNull(ItemMarketAnalysisRecord::getTechFileId)
                        .ne(ItemMarketAnalysisRecord::getTechFileId, 0))
            .isNotNull(ItemMarketAnalysisRecord::getTechFileId)
            .list()
            .stream()
            .collect(
                Collectors.toMap(
                    ItemMarketAnalysisRecord::getItemId,
                    Function.identity(),
                    (r1, r2) -> r1.getId() > r2.getId() ? r1 : r2));

    resultMap
        .values()
        .forEach(
            val -> {
              try {
                itemAiAnalysisBizService.reportTechDocument(
                    val.getItemId(), Objects.nonNull(val.getTechFileId()) && val.getTechFileId() > 0);
              } catch (Exception e) {
                log.error("ItemAiScheduleTask:reportTech error", e);
              }
            });
  }
}
