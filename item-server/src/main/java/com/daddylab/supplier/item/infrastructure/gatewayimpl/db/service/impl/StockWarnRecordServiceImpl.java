package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.StockWarnRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.StockWarnRecordMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockWarnRecordService;
import org.springframework.stereotype.Service;

/**
 * 库存告警记录表 服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Service
public class StockWarnRecordServiceImpl extends ServiceImpl<StockWarnRecordMapper, StockWarnRecord> implements IStockWarnRecordService {

}
