package com.daddylab.supplier.item.domain.daddylabWorkbench;

import com.alibaba.cola.dto.SingleResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.OtherOutboundOrderProcessForm;
import com.kuaishou.merchant.open.api.domain.item.PropValueDtoLIst;
import liquibase.pro.packaged.S;

public interface DaddylabWorkbenchGateway {

    SingleResponse<String> syncPurchaseOrder(Long purchaseOrderId, Long currentUserId);

    SingleResponse<String> syncStockOutOrder(Long stockOutOrderId, Long currentUserId);
    
    SingleResponse<String> syncOtherOutboundOrder(Long currentUserId, OtherOutboundOrderProcessForm form);
    
    void callback(WorkbenchCallbackRequest request);

    SingleResponse<String> syncPurchaseCostChange(Long bizId);
}
