package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 类目警戒库存配置表
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("category_warn_stock")
public class CategoryWarnStock extends Entity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 警戒库存数量
     */
    private BigDecimal warnStock;
}
