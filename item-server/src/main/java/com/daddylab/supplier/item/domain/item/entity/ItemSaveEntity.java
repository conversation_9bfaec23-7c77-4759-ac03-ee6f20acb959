package com.daddylab.supplier.item.domain.item.entity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.cmd.ResetProportionBO;
import com.daddylab.supplier.item.application.message.wechat.MsgEvent;
import com.daddylab.supplier.item.application.message.wechat.MsgEventType;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.trans.ItemTransMapper;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemCmd;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.item.event.ItemQcChangeEvent;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.operateLog.entity.ItemChangeBuffers;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CascadedDivisionLevel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IBizLevelDivisionService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuPriceService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.INewGoodsService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.PartnerSyncChargePersonReq;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.PartnerSyncChargePersonReqWrapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.google.common.base.Joiner;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/20 1:40 下午
 * @description
 */
@Slf4j
public class ItemSaveEntity {

    private SaveItemCmd cmd;

    private Boolean haveAssignCodeRight;

    private ItemGateway itemGateway;
    private UserGateway userGateway;

    private CategoryGateway categoryGateway;

    private void setCmd(SaveItemCmd cmd) {
        this.cmd = cmd;
    }

    private void setHaveAssignCodeRight(Boolean haveAssignCodeRight) {
        this.haveAssignCodeRight = haveAssignCodeRight;
    }

    private void setItemGateway(ItemGateway itemGateway) {
        this.itemGateway = itemGateway;
    }

    public void setUserGateway(UserGateway userGateway) {
        this.userGateway = userGateway;
    }

    public void setCategoryGateway(CategoryGateway categoryGateway) {
        this.categoryGateway = categoryGateway;
    }

    public UserGateway getUserGateway() {
        return userGateway;
    }

    private ItemBizService itemBizService;

    public void setItemBizService(ItemBizService itemBizService) {
        this.itemBizService = itemBizService;
    }

    private ItemSaveEntity() {
    }


    public static ItemSaveEntity register(
            SaveItemCmd cmd,
            Boolean haveAssignCodeRight,
            ItemGateway itemGateway,
            UserGateway userGateway,
            CategoryGateway categoryGateway) {
        ItemSaveEntity entity = new ItemSaveEntity();
        entity.setCmd(cmd);
        entity.setItemGateway(itemGateway);
        entity.setHaveAssignCodeRight(haveAssignCodeRight);
        entity.setUserGateway(userGateway);
        entity.setCategoryGateway(categoryGateway);
        entity.setItemBizService(SpringUtil.getBean(ItemBizService.class));

        Assert.hasText(cmd.getItemName(), "商品名称不得为空");
        Assert.notNull(cmd.getCategoryId(), "商品品类id不得为空");
        Assert.notNull(cmd.getProviderId(), "供应商id不得为空");
        Assert.notNull(cmd.getBuyerUserId(), "采购员用户id不得为空");
        Assert.isTrue(CollUtil.isNotEmpty(cmd.getDelivery()), "物流渠道不得为空");
        if (CollUtil.isNotEmpty(cmd.getImageList())) {
//            Assert.isTrue(cmd.getImageList().size() <= 5, "图片数量不得超过5张");
        }

        return entity;
    }

    public Item saveBase() {
        boolean add = Objects.isNull(cmd.getItemId());

        if (add) {
            boolean repeat = itemGateway.isRepeat(cmd.getItemName());
            if (repeat) {
                throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "商品名字冲突");
            }
        }

        Item oldItem = new Item();
        if (!add) {
            oldItem = itemGateway.getItem(cmd.getItemId());
            Assert.notNull(oldItem, "未查找到指定商品");
            if (!oldItem.getName().trim().equals(cmd.getItemName().trim())) {
                boolean repeat = itemGateway.isRepeat(cmd.getItemName());
                if (repeat) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "商品名字冲突");
                }
            }
        }

        Item item = new Item();
        //修改商品
        if (!add) {
            item.setId(oldItem.getId());
            item.setCode(oldItem.getCode());
            item.setProviderSpecifiedCode(oldItem.getProviderSpecifiedCode());
            item.setName(StringUtil.isNotBlank(cmd.getItemName()) ? cmd.getItemName() : oldItem.getName());
            item.setShortName(StringUtil.isNotBlank(cmd.getShortName()) ? cmd.getShortName() : oldItem.getShortName());
            item.setPartnerProviderItemSn(StringUtil.isNotBlank(cmd.getPartnerProviderItemSn()) ? cmd.getPartnerProviderItemSn() : oldItem.getPartnerProviderItemSn());
            item.setPartnerSysType(Objects.nonNull(cmd.getPartnerSysType()) ? cmd.getPartnerSysType() : oldItem.getPartnerSysType());
            item.setBusinessLine(Objects.nonNull(cmd.getBusinessLine()) ? cmd.getBusinessLine().get(0) : oldItem.getBusinessLine());
            item.setBusinessLines(Objects.nonNull(cmd.getBusinessLine())
                    ? cmd.getBusinessLine()
                    .stream()
                    .filter(Objects::nonNull)
                    .map(Object::toString)
                    .collect(Collectors.joining(","))
                    : oldItem.getBusinessLines());
            final List<CorpBizTypeDTO> corpBizType = cmd.getCorpBizType();
            if (corpBizType != null) {
                final List<Integer> businessLines =
                    corpBizType.stream()
                        .map(CorpBizTypeDTO::getCorpType)
                        .distinct()
                        .collect(Collectors.toList());
                item.setBusinessLine(businessLines.get(0));
                item.setBusinessLines(
                    businessLines.stream().map(Object::toString).collect(Collectors.joining(",")));
            }
            item.setBrandId(Objects.nonNull(cmd.getBrandId()) ? cmd.getBrandId() : Objects.requireNonNull(oldItem).getBrandId());
            item.setProviderId(Objects.nonNull(cmd.getProviderId()) ? cmd.getProviderId() : Objects.requireNonNull(oldItem).getProviderId());
            item.setCategoryId(cmd.getCategoryId());
            final ItemStatus itemStatus = mapItemStatus(cmd.getStatus());
            final ItemStatus oldItemStatus = mapItemStatus(oldItem.getStatus());
            //如果商品状态发生修改
            item.setLaunchStatus(oldItem.getLaunchStatus());
            if (!itemStatus.equals(oldItemStatus)) {
                if (Arrays.asList(ItemStatus.DISCARD, ItemStatus.OUT).contains(itemStatus)) {
                    item.setDownFrameTime(DateUtil.currentTime());
                    ItemChangeBuffers.addChange(item.getId(),
                            String.format("商品手动下架，状态从【%s】修改为【%s】", oldItemStatus.getDesc(), itemStatus.getDesc()),
                            OperateLogTarget.ITEM_DOWN);
                    if (!Arrays.asList(
                                    ItemLaunchStatus.DOWN.getValue(),
                                    ItemLaunchStatus.NON_SELECTIVITY.getValue(),
                                    ItemLaunchStatus.TO_BE_SELECTED.getValue())
                            .contains(item.getLaunchStatus())) {
                        item.setLaunchStatus(ItemLaunchStatus.DOWN.getValue());
                        SpringUtil.getBean(OperateLogDomainService.class)
                                .addOperatorLog(
                                        UserContext.getUserId(),
                                        OperateLogTarget.NEW_GOODS_SPU,
                                        item.getId(),
                                        "后端商品【下架】，同步新品商品库改为【已下架】状态，下架原因：" + cmd.getDownFrameReason());
                    }
                    item.setStatus(itemStatus.getValue());
                }
            }
            item.setStatusRemark(StringUtil.isNotBlank(cmd.getStatusReason()) ? cmd.getStatusReason() : oldItem.getStatusRemark());
            item.setIsGift(Objects.nonNull(cmd.getIsGift()) ? cmd.getIsGift() : Objects.requireNonNull(oldItem).getIsGift());
            item.setWarehouseNo(Objects.nonNull(cmd.getWarehouseNo()) ? cmd.getWarehouseNo() : Objects.requireNonNull(oldItem).getWarehouseNo());
            if (0 == cmd.getIsGift()) {
                item.setParentItemId(0L);
                item.setParentCode("0");
            } else {
                item.setParentItemId(Objects.nonNull(cmd.getParentItemId()) ? cmd.getParentItemId() : Objects.requireNonNull(oldItem).getParentItemId());
                item.setParentCode(StringUtil.isNotBlank(cmd.getParentCode()) ? cmd.getParentCode() : oldItem.getParentCode());
            }
            item.setDownFrameTime(cmd.getDownFrameTime());
            item.setDownFrameReason(cmd.getDownFrameReason());
        } else {
            //新增商品
            item.setLaunchStatus(ItemLaunchStatus.TO_BE_SELECTED.getValue());
            item.setSource(DataSource.SELF);
            item.setName(cmd.getItemName());
            item.setShortName(cmd.getShortName());
            item.setPartnerProviderItemSn(cmd.getPartnerProviderItemSn());
            item.setPartnerSysType(cmd.getPartnerSysType());
            if (cmd.getBusinessLine() != null) {
                item.setBusinessLine(cmd.getBusinessLine().get(0));
                item.setBusinessLines(cmd.getBusinessLine()
                        .stream()
                        .filter(Objects::nonNull)
                        .map(Object::toString)
                        .collect(Collectors.joining(",")));
            }
            final List<CorpBizTypeDTO> corpBizType = cmd.getCorpBizType();
            if (corpBizType != null) {
                final List<Integer> businessLines =
                        corpBizType.stream()
                                   .map(CorpBizTypeDTO::getCorpType)
                                   .distinct()
                                   .collect(Collectors.toList());
                item.setBusinessLine(businessLines.get(0));
                item.setBusinessLines(
                        businessLines.stream().map(Object::toString).collect(Collectors.joining(",")));
            }
            item.setCategoryId(cmd.getCategoryId());
            item.setBrandId(cmd.getBrandId());
            item.setProviderId(cmd.getProviderId());
            //ERP1.6 商品上新流程改动：默认刚创建的后端商品状态为【待上架】
            item.setStatus(ItemStatus.WAIT.getValue());
            item.setStatusRemark(cmd.getStatusReason());
            item.setIsGift(cmd.getIsGift());
            item.setWarehouseNo(cmd.getWarehouseNo());
            if (0 == cmd.getIsGift()) {
                item.setParentItemId(0L);
                item.setParentCode("0");
            } else {
                item.setParentItemId(cmd.getParentItemId());
                item.setParentCode(cmd.getParentCode());
            }
            item.setDownFrameTime(cmd.getDownFrameTime());
            item.setDownFrameReason(cmd.getDownFrameReason());
        }

        if (haveAssignCodeRight) {
            final String specifiedCode = cmd.getSpecialCode();
            if (StringUtil.isNotBlank(specifiedCode)) {
                if (add) {
                    Assert.isTrue(!itemGateway.isSpecialItemCodeRepeat(specifiedCode), "供货指定编码重复");
                }
                if (!add) {
                    final String oldSpecifiedCode = oldItem.getProviderSpecifiedCode();
                    if (!specifiedCode.equals(oldSpecifiedCode)) {
                        Assert.isTrue(!itemGateway.isSpecialItemCodeRepeat(specifiedCode), "供货指定编码重复");
                    }
                }
                item.setProviderSpecifiedCode(specifiedCode);
            }
        }

        if (add) {
            item.setCode("1000001");
            Optional<String> optional = itemGateway.getLatestItemCode();
            optional.ifPresent(code -> {
                if (code.startsWith("PR") || code.startsWith("GR") || code.startsWith("TE")
                        || code.startsWith("LO") || code.startsWith("DE")) {
                    code = code.substring(2);
                }
                if (code.startsWith("W")) {
                    code = code.replace("W", "").trim();
                }
                long l = Long.parseLong(code) + 1;
                item.setCode(String.valueOf(l));
            });
            if (Objects.equals(item.getBusinessLine(), 3)) {
                item.setCode(item.getCode());
            }
        }
        itemGateway.saveItem(item);
        //如果前端没传合作伙伴系统商品款号，就从尝试从后端商品中取，这是为了后面组装商品采购信息时可以根据供应商商品款号查询到P系统QC ID
        if (StringUtil.isBlank(cmd.getPartnerProviderItemSn())) {
            cmd.setPartnerProviderItemSn(item.getPartnerProviderItemSn());
        }
        //保存合作业务范围层级信息
        final IBizLevelDivisionService bizLevelDivisionService = SpringUtil.getBean(IBizLevelDivisionService.class);
        if (cmd.getCorpBizType() != null) {
            final ArrayList<CascadedDivisionLevel> divisionLevels = getCascadedDivisionLevels(cmd.getCorpBizType());
            bizLevelDivisionService
                    .saveCascadedLevels(BizUnionTypeEnum.SPU, item.getId(), item.getSupplierCode(), divisionLevels, Arrays.asList(
                            DivisionLevelEnum.BUSINESS_TYPE, DivisionLevelEnum.COOPERATION));
        }
        return item;
    }

    @NotNull
    private ArrayList<CascadedDivisionLevel> getCascadedDivisionLevels(List<CorpBizTypeDTO> corpBizType) {
        final ArrayList<CascadedDivisionLevel> divisionLevels = new ArrayList<>();
        for (CorpBizTypeDTO corpBizTypeDTO : corpBizType) {
            final CascadedDivisionLevel cascadedDivisionLevel = new CascadedDivisionLevel(corpBizTypeDTO.getCorpType());
            if (corpBizTypeDTO.getBizType() != null) {
                for (Integer bizType : corpBizTypeDTO.getBizType()) {
                    cascadedDivisionLevel.addSubValue(bizType);
                }
            }
            divisionLevels.add(cascadedDivisionLevel);
        }
        return divisionLevels;
    }

    private ItemStatus mapItemStatus(Integer status) {
        return IEnum.getEnumOptByValue(ItemStatus.class, status)
                .orElseThrow(() -> ExceptionPlusFactory
                        .bizException(ErrorCode.VERIFY_PARAM, "商品状态选择无效值！"));
    }

    /**
     * sku 无法删除。
     *
     * @param itemId     商品id
     * @param itemCode   商品code
     * @param categoryId 品类id
     * @param providerId 供应商id
     */
    public void saveSku(Long itemId, String itemCode, Long categoryId, Long providerId) {
        //如果商品规格中存在规格编码与商品编码相同的，说明是存量商品，处理新增SKU时需要特殊处理
        final boolean isHistoryItem = cmd.getSkuList().stream().anyMatch(v -> itemCode.equals(
                v.getSkuCode()));

        // 检查 sku入参当中条码或者指定特殊编码是否重复
        List<ItemSkuListDto> collect = cmd.getSkuList().stream().filter(val -> StrUtil.isNotBlank(val.getBarCode())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            int size = collect.size();
            long count = collect.stream().map(ItemSkuListDto::getBarCode).distinct().count();
            Assert.isTrue(size == count, "sku条码不得重复");
        }
        List<ItemSkuListDto> collect2 = cmd.getSkuList().stream().filter(val -> StrUtil.isNotBlank(val.getSpecifiedSkuCode())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect2)) {
            int size = collect2.size();
            long count = collect2.stream().map(ItemSkuListDto::getSpecifiedSkuCode).distinct().count();
            Assert.isTrue(size == count, "sku供货指定编码不得重复");
        }

        List<ItemSkuListDto> updateList = cmd.getSkuList().stream().filter(index -> Objects.nonNull(index.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(updateList)) {
            setParam(updateList);
            updateSku(updateList, categoryId);
        }

        List<ItemSkuListDto> addList = cmd.getSkuList().stream().filter(index -> Objects.isNull(index.getId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(addList)) {
            addSku(itemId, itemCode, categoryId, providerId, addList, cmd, isHistoryItem);
        }

        final List<Long> removeList =
                cmd.getSkuList()
                        .stream()
                        .filter(itemSkuListDto -> Boolean.TRUE.equals(itemSkuListDto.getIsDel()) && itemSkuListDto.getId() != null)
                        .map(ItemSkuListDto::getId)
                        .collect(Collectors.toList());
        if (!removeList.isEmpty()) {
            itemBizService.deleteSkuByIds(removeList);
        }
    }

    private void setParam(List<ItemSkuListDto> updateList) {
        updateList.forEach(itemSkuListDto -> {
            itemSkuListDto.setProviderId(cmd.getProviderId());
            itemSkuListDto.setUnit(cmd.getBaseUnitName());
            itemSkuListDto.setTaxRate(cmd.getRate());
            itemSkuListDto.setPurchaseTaxRate(cmd.getPurchaseRate());
            itemSkuListDto.setWarehouseNo(cmd.getWarehouseNo());
        });

    }

    public void saveProcurement(Long itemId) {
        final ArrayList<Object> events = new ArrayList<>();
        final ItemProcurement saved = itemGateway.getProcurementByItemId(itemId);
        final ItemProcurement buildProcurement = buildProcurement(itemId, events);

        if (Objects.isNull(saved)) {
            itemGateway.saveOrUpdateProcurement(buildProcurement);
            if (CollUtil.isNotEmpty(events)) {
                for (Object event : events) {
                    EventBusUtil.post(event);
                }
            }
            return;
        }

        buildProcurement.setId(saved.getId());
        itemGateway.saveOrUpdateProcurement(buildProcurement);

        if (CollUtil.isNotEmpty(events)) {
            for (Object event : events) {
                EventBusUtil.post(event);
            }
        }

        // 如果采购员发生了修改。1.将修改信息同步到新品商品。2.发送变更消息
        boolean buyerChange = !saved.getBuyerId().equals(buildProcurement.getBuyerId());
        if (buyerChange) {
            INewGoodsService iNewGoodsService = SpringUtil.getBean(INewGoodsService.class);
            Integer count = iNewGoodsService.lambdaQuery().eq(NewGoods::getItemId, itemId).select().count();
            if (count > 0) {
                MsgEvent msgEvent = MsgEvent.of(ListUtil.of(itemId), MsgEventType.ONE_FIELD_CHANGE);
                EventBusUtil.post(msgEvent, true);
            }

            // 采购员改动通知P系统，记录商品日志
            if (StrUtil.isNotBlank(cmd.getPartnerProviderItemSn())) {
                final PartnerFeignClient partnerFeignClient = SpringUtil.getBean(PartnerFeignClient.class);
                PartnerSyncChargePersonReq req = new PartnerSyncChargePersonReq();
                req.setItemNo(cmd.getPartnerProviderItemSn());
                req.setPurchaseId(cmd.getBuyerUserId());
                req.setQcIds(StrUtil.isNotBlank(buildProcurement.getQcIds()) ?
                        Arrays.stream(buildProcurement.getQcIds().split(StrUtil.COMMA))
                                .map(Long::valueOf).collect(Collectors.toList())
                        : new LinkedList<>());
                final PartnerSyncChargePersonReqWrapper reqWrapper = PartnerSyncChargePersonReqWrapper.of(ListUtil.of(req));
                final Rsp<Object> objectRsp = partnerFeignClient.notifyBuryOrQcChange(reqWrapper);
                if (!objectRsp.isSuccess()) {
                    log.error("notifyBuryOrQcChange fail. req:{},res:{}", JsonUtil.toJson(reqWrapper), JsonUtil.toJson(objectRsp));
                    throw ExceptionPlusFactory.bizException(ErrorCode.SYS_ERROR, "采购员改变，同步数据到P系统失败。" + objectRsp.getMsg());
                }
            }
        }

        // 如果单位发生改变，判定此商品编号是否大于单位推送分割id,如果小于等于分割id,并且此sku已经生成了入库单或者出库单，则不允许修改单位
//        boolean unitChange = !saved.getBaseUnitId().equals(buildProcurement.getBaseUnitId());
//        if (unitChange) {
//            boolean canChange = true;
//            KingDeeConfig kingDeeConfig = SpringUtil.getBean(KingDeeConfig.class);
//            if (itemId <= Long.parseLong(kingDeeConfig.getLineItemId())) {
//                ItemSkuGateway itemSkuGateway = SpringUtil.getBean(ItemSkuGateway.class);
//                List<ItemSku> skuList = itemGateway.getSkuList(itemId);
//                for (ItemSku itemSku : skuList) {
//                    Boolean aBoolean1 = itemSkuGateway.beRelatedStockInOrder(itemSku.getSkuCode());
//                    Boolean aBoolean2 = itemSkuGateway.beRelatedStockOutOrder(itemSku.getSkuCode());
//                    if (aBoolean1 || aBoolean2) {
//                        canChange = false;
//                        break;
//                    }
//                }
//            }
//            if (!canChange) {
//                throw ExceptionPlusFactory.bizException(ErrorCode.BUSINESS_OPERATE_ERROR, "此商品sku已经生成了下游单据，单位不允许修改");
//            }
//        }

    }

    public void saveExpressTemplate(Long itemId) {
        final List<ItemExpressTemplate> saveList = itemGateway.getExpressByItemId(itemId);

        // 新增快递模板
        if (CollUtil.isEmpty(saveList) && Objects.nonNull(cmd.getExpresses())) {
            addOrUpdateExpressTemplate(itemId);
            return;
        }
        // 删除快递模板
        if (CollUtil.isNotEmpty(saveList) && Objects.isNull(cmd.getExpresses())) {
            final List<Long> idList = saveList.stream().map(ItemExpressTemplate::getId).collect(Collectors.toList());
            itemGateway.removeExpress(idList);
            return;
        }
        if (Objects.isNull(cmd.getExpresses())) {
            return;
        }
        // 更新
        addOrUpdateExpressTemplate(itemId);
    }

//    -------------------------------------  public end -----------------------------------


    /**
     * 快递模板没有能锚定唯一的参数，只能依据请求中知否存在id来进行处理。
     *
     * @param itemId 商品id
     */
    private void addOrUpdateExpressTemplate(Long itemId) {
        List<ItemExpressTemplate> itemExpressTemplateList = new LinkedList<>();
        ExpressDto expresses = cmd.getExpresses();
        verifyExpressTemplate(expresses);
        final ItemExpressTemplate expressTemplate = ItemTransMapper.INSTANCE.expressDtoToDb(expresses);
        expressTemplate.setItemId(itemId);
        itemExpressTemplateList.add(expressTemplate);
        itemGateway.saveOrUpdateBatchExpress(itemExpressTemplateList);
    }


    private void verifyExpressTemplate(ExpressDto dto) {
        //@徵乌 2022-06-24:工厂发货，发货地、物流、物流模版改成非必填
        // if (delivery.contains(ItemDelivery.FACTORY.getValue())) {
        //    Assert.hasText(dto.getFromArea(), "发货地不得为空");
        //    Assert.hasText(dto.getExpressCompany(), "物流不得为空");
        //    Assert.hasText(dto.getArea(), "快递模板不得为空");
        //}
        Assert.isTrue(dto.getFromArea().length() <= 20, "发货地小于20字符");
        Assert.isTrue(dto.getExpressCompany().length() <= 20, "物流小于20字符");
        Assert.isTrue(dto.getArea().length() <= 150, "包邮区小于150字符");
    }

    private ItemProcurement buildProcurement(Long itemId, List<Object> events) {
        ItemProcurement itemProcurement = new ItemProcurement();
        itemProcurement.setItemId(itemId);
        itemProcurement.setProviderId(cmd.getProviderId());

        final StaffInfo staffInfo = userGateway.queryStaffInfoById(cmd.getBuyerUserId());
        if (staffInfo.getStatus() == 0) {
            throw ExceptionPlusFactory.bizException(
                    ErrorCode.OPERATION_REJECT,
                    String.format("采购员【%s】已离职，请更换采购员后重新保存", staffInfo.getNickname()));
        }

        Long buyerId = itemGateway.saveOrUpdateBuyer(cmd.getBuyerUserId(), cmd.getBuyerNickName());
        itemProcurement.setBuyerId(buyerId);
        itemProcurement.setIsGift(cmd.getIsGift());
        itemProcurement.setDelivery(StringUtils.join(new TreeSet<>(cmd.getDelivery()), ","));
        itemProcurement.setWarehouseNo(cmd.getWarehouseNo());
        itemProcurement.setBaseUnitId(cmd.getBaseUnitId());
        itemProcurement.setRate(cmd.getRate());
        itemProcurement.setPurchaseRate(cmd.getPurchaseRate());
        itemProcurement.setTaxRateCode(cmd.getTaxRateCode());

        if (StringUtil.isNotBlank(cmd.getPartnerProviderItemSn())) {
            final PartnerItemCmd partnerItemQuery = new PartnerItemCmd();
            partnerItemQuery.setSearchType(1);
            partnerItemQuery.setContext(cmd.getPartnerProviderItemSn());
            partnerItemQuery.setPageIndex(1);
            partnerItemQuery.setPageSize(10);
            final List<PartnerItemResp> partnerItemResps = itemGateway.partnerQuery(partnerItemQuery);
            if (partnerItemResps.isEmpty()) {
                throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND, "供应商商品款号无效");
            }
            final PartnerItemResp partnerItemResp = partnerItemResps.get(0);
            final List<Long> qcIds = partnerItemResp.getQcIds();
            final String qcIdsStrR = qcIds.isEmpty() ? "" : Joiner.on(',').join(qcIds) + ",";
            final String qcIdsStrL = StringUtil.isBlank(itemProcurement.getQcIds()) ? "" : itemProcurement.getQcIds();
            if (!qcIdsStrL.equals(qcIdsStrR)) {
                itemProcurement.setQcIds(qcIdsStrR);
                events.add(new ItemQcChangeEvent(itemId, qcIdsStrL, qcIdsStrR));
            }
        }
        return itemProcurement;
    }

    /**
     * 更新sku,sku更新只更新三个值。销售价，采购价，指定编码,条形码
     */
    @SneakyThrows
    private void updateSku(List<ItemSkuListDto> skuListDtoList, Long categoryId) {
        List<ItemSku> updateList = new LinkedList<>();

        Set<ItemAttrDto> updateAttrList = new HashSet<>();

        IItemSkuPriceService iItemSkuPriceService = SpringUtil.getBean(IItemSkuPriceService.class);
        List<ItemSkuPrice> itemSkuPriceList = new LinkedList<>();
        CombinationItemBizService combinationItemBizService = SpringUtil.getBean(CombinationItemBizService.class);
        List<ResetProportionBO> priceChangeSkuCodeList = new LinkedList<>();

        for (ItemSkuListDto skuDto : skuListDtoList) {
            if (Boolean.TRUE.equals(skuDto.getIsDel())) {
                continue;
            }
            final Optional<ItemSku> oldSkuOptional = itemGateway.getByItemSkuId(skuDto.getId());
            final ItemSku itemSkuOld = oldSkuOptional.orElseThrow(
                    (Supplier<Throwable>) () -> ExceptionPlusFactory
                            .bizException(ErrorCode.ITEM_BIZ_ERROR,
                                    "sku更新异常,skuId非法,skuId:" + skuDto.getId()));
            verifySkuDto(skuDto, itemSkuOld);

            final ItemSku itemSku = ItemTransMapper.INSTANCE.skuDtoToDb(skuDto);
            itemSku.setCategoryId(categoryId);
            itemSku.setPlatformCommission(skuDto.getPlatformCommission().getVal());
            itemSku.setContractSalePrice(skuDto.getContractSalePrice().getVal());

            if (StringUtil.isNotBlank(skuDto.getSales())) {
                BigDecimal decimal = new BigDecimal(skuDto.getSales().replaceAll(" ", ""))
                        .setScale(6, RoundingMode.HALF_DOWN);
                itemSku.setSalePrice(decimal);
            }
            if (StringUtil.isNotBlank(skuDto.getProcurement())) {
                BigDecimal decimal = new BigDecimal(skuDto.getProcurement().replaceAll(" ", ""))
                        .setScale(6, RoundingMode.HALF_DOWN);
                itemSku.setCostPrice(decimal);
            }

            // 更新规格
            itemSku.setSpecifications(skuDto.buildSpecifications());

            if (haveAssignCodeRight) {
                final String newCode = skuDto.getSpecifiedSkuCode();
                final String oldCode = oldSkuOptional.get().getProviderSpecifiedCode();
                if (!newCode.equals(oldCode)) {
                    if (StringUtil.isNotBlank(newCode)) {
                        Assert.isTrue(!itemGateway.isSpecialSkuCodeRepeat(newCode), "供货指定SKU重复," + newCode);
                    }
                }
                itemSku.setProviderSpecifiedCode(newCode);
            }
            updateList.add(itemSku);
            updateAttrList.addAll(skuDto.getAttrList());

            boolean costPriceChange = false;
            boolean salesPriceChange = false;
            // 合同销售价是否改变
            boolean contractSalePriceChange = skuExtraPriceChange(
                    2, skuDto.getContractSalePrice().getStartTime(), skuDto.getContractSalePrice().getEndTime(),
                    skuDto.getContractSalePrice().getVal(), skuDto.getSkuCode());
            // 平台佣金是否发生变化
            boolean platformCommissionChange = skuExtraPriceChange(
                    3, skuDto.getPlatformCommission().getStartTime(), skuDto.getPlatformCommission().getEndTime(),
                    skuDto.getPlatformCommission().getVal(), skuDto.getSkuCode());

            //记录SKU变更日志
            final List<String> changeLog = new ArrayList<>();
            final List<String> priceChangeLog = new ArrayList<>();
            if (ObjectUtil.isNotNull(itemSku.getBarCode()) && !ObjectUtil
                    .equals(itemSku.getBarCode(), itemSkuOld.getBarCode())) {
                changeLog.add(StringUtil
                        .format("条码（{} -> {}）", formatLogVar(itemSkuOld.getBarCode()),
                                formatLogVar(itemSkuOld.getBarCode())));
            }
            if (ObjectUtil.isNotNull(itemSku.getSalePrice()) && !ObjectUtil
                    .equals(itemSku.getSalePrice(), itemSkuOld.getSalePrice())) {
                priceChangeLog.add(StringUtil
                        .format("销售价（{} -> {}）", formatLogVar(itemSkuOld.getSalePrice()),
                                formatLogVar(itemSku.getSalePrice())));
                salesPriceChange = true;
            }
            if (ObjectUtil.isNotNull(itemSku.getCostPrice()) && !ObjectUtil
                    .equals(itemSku.getCostPrice(), itemSkuOld.getCostPrice())) {
                priceChangeLog.add(StringUtil
                        .format("采购成本（{} -> {}）", formatLogVar(itemSkuOld.getCostPrice()),
                                formatLogVar(itemSku.getCostPrice())));
                costPriceChange = true;
            }
            if (Objects.nonNull(itemSku.getGoodsType()) && !ObjectUtil.equals(itemSku.getGoodsType(), itemSkuOld.getGoodsType())) {
                changeLog.add(StringUtil.format("货品类型（{} -> {}）",
                        formatLogVar(itemSkuOld.getGoodsType().getDesc()),
                        formatLogVar(itemSku.getGoodsType().getDesc())));
            }
            if (Objects.nonNull(itemSku.getPlatformCommission()) && !ObjectUtil.equals(itemSku.getPlatformCommission(), itemSkuOld.getPlatformCommission())) {
                changeLog.add(StringUtil.format("平台佣金（{} -> {}）",
                        formatLogVar(itemSkuOld.getPlatformCommission()),
                        formatLogVar(itemSku.getPlatformCommission())));
            }
            if (Objects.nonNull(itemSku.getContractSalePrice()) && !ObjectUtil.equals(itemSku.getContractSalePrice(), itemSkuOld.getContractSalePrice())) {
                changeLog.add(StringUtil.format("合同销售价（{} -> {}）",
                        formatLogVar(itemSkuOld.getContractSalePrice()),
                        formatLogVar(itemSku.getContractSalePrice())));
            }


            if (ObjectUtil.isNotNull(itemSku.getProviderSpecifiedCode()) && !ObjectUtil
                    .equals(itemSku.getProviderSpecifiedCode(),
                            itemSkuOld.getProviderSpecifiedCode())) {
                changeLog.add(StringUtil.format("供货指定SKU（{} -> {}）",
                        formatLogVar(itemSkuOld.getProviderSpecifiedCode()),
                        formatLogVar(itemSku.getProviderSpecifiedCode())));
            }

            if (!changeLog.isEmpty()) {
                ItemChangeBuffers.addChange(cmd.getItemId(),
                        StringUtil.format("规格（{}）：{}", itemSku.getSkuCode(),
                                String.join("、", changeLog)));
            }
            if (!priceChangeLog.isEmpty()) {
                ItemChangeBuffers.addChange(cmd.getItemId(),
                        StringUtil.format("价格（{}）：{}", itemSku.getSkuCode(),
                                String.join("、", priceChangeLog)),
                        OperateLogTarget.ITEM_PRICE);
            }

            if (!costPriceChange) {
                // 如果更新sku时，成本价格没有发生变化，那么检查成本价的开始时间是否发生变化
                List<ItemSkuPrice> list = iItemSkuPriceService.lambdaQuery()
                        .eq(ItemSkuPrice::getSkuCode, skuDto.getSkuCode())
                        .eq(ItemSkuPrice::getPrice, new BigDecimal(skuDto.getProcurement()))
                        .eq(ItemSkuPrice::getType, 0)
                        .orderByDesc(ItemSkuPrice::getId)
                        .select().list();
                if (CollUtil.isNotEmpty(list)) {
                    ItemSkuPrice itemSkuPrice = list.get(0);
                    if (!itemSkuPrice.getStartTime().equals(skuDto.getProcurementStartDt())) {
                        costPriceChange = true;
                    }
                }
            }

            // 如果合同销售价或者平台佣金发生了变动
            if(contractSalePriceChange){
                final ItemSkuPrice itemSkuPrice = addSkuExtraPriceDto(skuDto.getContractSalePrice(), skuDto.getId(), itemSku.getSkuCode(), 2);
                itemSkuPriceList.add(itemSkuPrice);
            }
            if(platformCommissionChange){
                final ItemSkuPrice itemSkuPrice = addSkuExtraPriceDto(skuDto.getPlatformCommission(), skuDto.getId(), itemSku.getSkuCode(), 3);
                itemSkuPriceList.add(itemSkuPrice);
            }

            // 采购成本发生了变动,记录下价格操作记录
            if (costPriceChange) {
                Integer count = iItemSkuPriceService.lambdaQuery().eq(ItemSkuPrice::getSkuCode, skuDto.getSkuCode()).count();
                // 上sku成本价格快照功能后，兼容之间的旧数据。如果改sku的成本价格，将之前旧的价格数据也存到快照表里
                if (count == 0) {
                    ItemSkuPrice itemSkuPrice = new ItemSkuPrice();
                    itemSkuPrice.setSkuId(skuDto.getId());
                    itemSkuPrice.setSkuCode(skuDto.getSkuCode());
                    itemSkuPrice.setPrice(itemSkuOld.getCostPrice());
                    itemSkuPrice.setStartTime(1656604800L);
                    itemSkuPrice.setEndTime(System.currentTimeMillis() / 1000);
                    itemSkuPriceList.add(itemSkuPrice);
                }

                ItemSkuPrice itemSkuPrice = new ItemSkuPrice();
                itemSkuPrice.setSkuId(skuDto.getId());
                itemSkuPrice.setSkuCode(skuDto.getSkuCode());
                itemSkuPrice.setPrice(new BigDecimal(skuDto.getProcurement()));
                itemSkuPrice.setStartTime(DateUtil.getTimeStampOfZero(skuDto.getProcurementStartDt()));
                itemSkuPrice.setEndTime(skuDto.getProcurementEndDt());
                itemSkuPriceList.add(itemSkuPrice);
            }

            if (costPriceChange || salesPriceChange) {
                ResetProportionBO bo = new ResetProportionBO();
                bo.setSkuCode(skuDto.getSkuCode());
                bo.setOldCostPrice(itemSkuOld.getCostPrice());
                bo.setNewCostPrice(new BigDecimal(skuDto.getProcurement()));
                bo.setOldSalesPrice(itemSkuOld.getSalePrice());
                bo.setNewSalesPrice(new BigDecimal(skuDto.getSales()));
                priceChangeSkuCodeList.add(bo);
            }
        }

        if (CollUtil.isNotEmpty(skuListDtoList)) {
            updateSkuAttr(updateAttrList);
        }
        itemGateway.saveOrUpdateBatchItemSku(updateList);
        if (CollUtil.isNotEmpty(itemSkuPriceList)) {
            iItemSkuPriceService.saveBatch(itemSkuPriceList);
        }
        // 如果采购价格或者销售价格发生了变动，则进行组合装金额占比的预检查
        if (CollUtil.isNotEmpty(priceChangeSkuCodeList)) {
            combinationItemBizService.resetProportion(priceChangeSkuCodeList);
        }
    }

    /**
     * 检查价格是否发生变动
     *
     * @param type
     * @param newPriceVal
     * @param skuCode
     * @return
     */
    private Boolean skuExtraPriceChange(Integer type, Long newStartDate, Long newEndDate,
                                        BigDecimal newPriceVal, String skuCode) {
        final IItemSkuPriceService itemSkuPriceService = SpringUtil.getBean(IItemSkuPriceService.class);
        final List<ItemSkuPrice> list = itemSkuPriceService.lambdaQuery()
                .eq(ItemSkuPrice::getSkuCode, skuCode)
                .eq(ItemSkuPrice::getType, type)
                .orderByDesc(ItemSkuPrice::getId).list();
        if (CollUtil.isEmpty(list)) {
            return true;
        }

        final ItemSkuPrice itemSkuPrice = list.get(0);
        if (itemSkuPrice.getPrice().compareTo(newPriceVal) != 0) {
            return true;
        }

        boolean change1 = !itemSkuPrice.getStartTime().equals(newStartDate);
        if (change1) {
            return true;
        }

        return !itemSkuPrice.getEndTime().equals(newEndDate);
    }

    private ItemSkuPrice addSkuExtraPriceDto(SkuExtraPriceDto skuExtraPriceDto, Long skuId, String skuCode, Integer type) {
        ItemSkuPrice itemSkuPrice0 = new ItemSkuPrice();
        itemSkuPrice0.setSkuId(skuId);
        itemSkuPrice0.setSkuCode(skuCode);
        itemSkuPrice0.setPrice(skuExtraPriceDto.getVal());
        itemSkuPrice0.setStartTime(skuExtraPriceDto.getStartTime());
        itemSkuPrice0.setEndTime(skuExtraPriceDto.getEndTime());
        itemSkuPrice0.setType(type);
        return itemSkuPrice0;
    }


    private String formatLogVar(Object var) {
        String result = null;
        if (var != null) {
            if (var instanceof BigDecimal) {
                result = NumberUtil.format((BigDecimal) var);
            } else {
                result = var.toString();
            }
        }
        return StringUtil.isBlank(result) ? "无" : result;
    }

    /**
     * 更新sku和属性的依赖关系
     *
     * @param itemAttrDtoList
     */
    private void updateSkuAttr(Set<ItemAttrDto> itemAttrDtoList) {
        for (ItemAttrDto itemAttrDto : itemAttrDtoList) {
            if (Objects.nonNull(itemAttrDto.getItemAttrDbId())) {
                itemGateway.updateSkuAttr(itemAttrDto);
            }
        }
    }


    /**
     * SaveItemCmd.ItemSkuListDto.Id 为空，均将视为sku新增。
     *
     * @param itemId        商品id
     * @param itemCode      商品code。skuCode生成规则，在itemCode后加两位数字，从01开始递增。
     * @param categoryId    品类id。sku拥有的属性id即为品类id的属性。
     * @param isHistoryItem
     */
    private void addSku(Long itemId, String itemCode, Long categoryId, Long providerId, List<ItemSkuListDto> skuListDtoList, SaveItemCmd cmd, boolean isHistoryItem) {

        // 如果此商品存在新品商品库中，在商品新增sku的时候，也将新sku的信息同步到新品商品中
        INewGoodsService newGoodsService = SpringUtil.getBean(INewGoodsService.class);
        NewGoods one = newGoodsService.lambdaQuery().eq(NewGoods::getItemId, itemId).orderByDesc(NewGoods::getUpdatedAt).select().last("limit 1").one();
        boolean addNewGood = Objects.nonNull(one);
        List<NewGoods> addList = new LinkedList<>();
        List<ItemSkuPrice> addSkuPriceList = new LinkedList<>();

        StringJoiner stringJoiner = new StringJoiner("");
        for (ItemSkuListDto skuDto : skuListDtoList) {
            if (Boolean.TRUE.equals(skuDto.getIsDel())) {
                continue;
            }

            verifySkuDto(skuDto, null);
            final ItemSku itemSku = buildItemSkuWithoutId(itemId, itemCode, skuDto, isHistoryItem);
            itemSku.setCategoryId(categoryId);
            itemSku.setProviderId(providerId);
            // 保存规格
            itemSku.setSpecifications(skuDto.buildSpecifications());
            itemSku.setWarehouseNo(cmd.getWarehouseNo());
            itemSku.setTaxRate(cmd.getRate());
            itemSku.setPurchaseTaxRate(cmd.getPurchaseRate());
            itemSku.setUnit(cmd.getBaseUnitName());
            itemSku.setSplitType(skuDto.getSplitType());
            itemSku.setProps(skuDto.getProps());
            itemSku.setPlatformCommission(skuDto.getPlatformCommission().getVal());
            itemSku.setContractSalePrice(skuDto.getContractSalePrice().getVal());
            itemSku.setGoodsType(skuDto.getGoodsType());
            final Long itemSkuId = itemGateway.saveItemSkuReturnId(itemSku);

            // 处理SKU 合同销售价和平台佣金
            final ItemSkuPrice itemSkuPrice1 = addSkuExtraPriceDto(skuDto.getContractSalePrice(), itemSkuId, itemSku.getSkuCode(), 2);
            addSkuPriceList.add(itemSkuPrice1);
            final ItemSkuPrice itemSkuPrice2 = addSkuExtraPriceDto(skuDto.getPlatformCommission(), itemSkuId, itemSku.getSkuCode(), 3);
            addSkuPriceList.add(itemSkuPrice2);

            // 处理sku成本价格
            ItemSkuPrice itemSkuPrice = new ItemSkuPrice();
            itemSkuPrice.setSkuId(itemSkuId);
            itemSkuPrice.setSkuCode(itemSku.getSkuCode());
            itemSkuPrice.setPrice(new BigDecimal(skuDto.getProcurement()));
            itemSkuPrice.setStartTime(DateUtil.getTimeStampOfZero(skuDto.getProcurementStartDt()));
            itemSkuPrice.setEndTime(skuDto.getProcurementEndDt());
            itemSkuPrice.setType(0);
            addSkuPriceList.add(itemSkuPrice);

            StringBuilder skuSkrStr = new StringBuilder();
            List<ItemSkuAttrRef> saveItemSkuAttrRefList = new LinkedList<>();
            skuDto.getAttrList().forEach(itemAttrDto -> {
                Long itemAttrId;
                if (Objects.nonNull(itemAttrDto.getItemAttrDbId()) && 0 != itemAttrDto.getItemAttrDbId()) {
                    itemAttrId = itemAttrDto.getItemAttrDbId();
                    itemGateway.updateSkuAttr(itemAttrDto);
                } else {
                    ItemAttr itemAttr = buildItemAttr(itemAttrDto, categoryId, itemId);
                    itemAttrId = this.itemGateway.saveItemAttrReturnId(itemAttr);
                }
                // 新建 itemSku-itemAttr的映射关系
                final ItemSkuAttrRef itemSkuAttrRef = buildItemSkuAttrRef(itemId, itemSkuId, itemAttrId);
                saveItemSkuAttrRefList.add(itemSkuAttrRef);
                skuSkrStr.append("属性=").append(itemAttrDto.getName()).append("，值=").append(itemAttrDto.getValue()).append("；");
            });
            itemGateway.saveBatchItemSkuAttrRef(saveItemSkuAttrRefList);

            // 根据新增的sku,增加新品商品信息
            if (addNewGood) {
                NewGoods newGoods = new NewGoods();
                newGoods.setItemId(itemId);
                newGoods.setSkuCode(itemSku.getSkuCode());
                newGoods.setName(cmd.getItemName());
                newGoods.setBrandId(cmd.getBrandId());
                newGoods.setPrincipalId(one.getPrincipalId());
                newGoods.setLegalId(one.getLegalId());
                newGoods.setShipmentType(one.getShipmentType());
                newGoods.setShipmentArea(one.getShipmentArea());
                newGoods.setShipmentAging(one.getShipmentAging());
                newGoods.setLogistics(one.getLogistics());
                newGoods.setExpressTemplate(one.getExpressTemplate());
                newGoods.setNoReason(one.getNoReason());
                newGoods.setRemark(one.getRemark());
                addList.add(newGoods);
            }

            StringJoiner addLog = new StringJoiner(" ",
                    StringUtil.format("新增规格（{}）：", itemSku.getSkuCode()), "");
            String substring = skuSkrStr.substring(0, skuSkrStr.toString().length() - 1);
            addLog.add(StringUtil.format("【{}】", substring)).add("。");
            stringJoiner.add(addLog.toString());
        }
        SpringUtil.getBean(OperateLogDomainService.class).addOperatorLog(UserContext.getUserId(), OperateLogTarget.ITEM, itemId, stringJoiner.toString());

        if (CollectionUtil.isNotEmpty(addList)) {
            newGoodsService.saveBatch(addList);
        }

        if (CollUtil.isNotEmpty(addSkuPriceList)) {
            IItemSkuPriceService iItemSkuPriceService = SpringUtil.getBean(IItemSkuPriceService.class);
            iItemSkuPriceService.saveBatch(addSkuPriceList);
        }

    }


    private void verifySkuDto(ItemSkuListDto dto, ItemSku oldSku) {
        boolean isAdd = Objects.isNull(oldSku);

        String errorFlag = "skuCode:" + dto.getSkuCode() + ",itemSkuId:" + dto.getId();

        if (isAdd) {
            Assert.isTrue(CollUtil.isNotEmpty(dto.getAttrList()), "属性列表不得为空," + errorFlag);
            final List<ItemAttrDto> attrList = dto.getAttrList();
            for (ItemAttrDto itemAttrDto : attrList) {
                final boolean haveAttrId = Objects.nonNull(itemAttrDto.getAttrId());
                final boolean haveAttrVal = StringUtil.isNotBlank(itemAttrDto.getValue());
                Assert.isTrue(haveAttrId && haveAttrVal, "sku属性值和属性id必须非空," + errorFlag);
            }
        }

        if (StringUtil.isNotBlank(dto.getBarCode())) {
//            if (dto.getBarCode().length() > 20) {
//                throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "sku条形码长度最大20");
//            }
            if (isAdd) {
                if (itemGateway.isSkuBarCodeRepeat(dto.getBarCode())) {
                    throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "sku条形码不得重复");
                }
            } else {
                if (!dto.getBarCode().equals(oldSku.getBarCode())) {
                    if (itemGateway.isSkuBarCodeRepeat(dto.getBarCode())) {
                        throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "sku条形码不得重复");
                    }
                }
            }
        }
    }


    private ItemSku buildItemSkuWithoutId(Long itemId, String itemCode, ItemSkuListDto skuDto,
                                          boolean isHistoryItem) {
        skuDto.setItemId(itemId);
        final ItemSku itemSku = ItemTransMapper.INSTANCE.skuDtoToDb(skuDto);

        if (StringUtil.isNotBlank(skuDto.getSales())) {
            itemSku.setSalePrice(new BigDecimal(skuDto.getSales().replaceAll(" ", ""))
                    .setScale(6, RoundingMode.HALF_DOWN));
        }
        if (StringUtil.isNotBlank(skuDto.getProcurement())) {
            itemSku.setCostPrice(new BigDecimal(skuDto.getProcurement().replaceAll(" ", ""))
                    .setScale(6, RoundingMode.HALF_DOWN));
        }

        final String specifiedSkuCode = skuDto.getSpecifiedSkuCode();
        if (isHistoryItem) {
            if (StringUtil.isBlank(specifiedSkuCode)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "历史存量商品新增规格需要提供\"供货指定SKU\"编码");
            }
            if (itemGateway.isSkuCodeRepeat(specifiedSkuCode)) {
                throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "供货指定SKU与已存在的规格编码重复，" + specifiedSkuCode);
            }
            itemSku.setSkuCode(skuDto.getSpecifiedSkuCode());
        } else {
            itemSku.setSkuCode(getSkuCode(itemId, itemCode));
        }
        if (haveAssignCodeRight) {
            if (StringUtil.isNotBlank(specifiedSkuCode)) {
                Assert.isTrue(!itemGateway.isSpecialSkuCodeRepeat(specifiedSkuCode), "供货指定SKU重复," + specifiedSkuCode);
            }
            itemSku.setProviderSpecifiedCode(specifiedSkuCode);
        }
        return itemSku;
    }

    private String getSkuCode(Long itemId, String itemCode) {

        AtomicReference<String> skuCodeRef = new AtomicReference<>(itemCode + "01");
        Optional<String> latestSkuCode = itemGateway.getLatestSkuCode(itemId);
        latestSkuCode.ifPresent(skuCode -> {
            if (skuCode.startsWith("PR") || skuCode.startsWith("GR") || skuCode.startsWith("TE")
                    || skuCode.startsWith("LO") || skuCode.startsWith("DE")) {
                skuCode = skuCode.substring(2);
            }
            long l = Long.parseLong(skuCode.replaceAll(itemCode, "")) + 1;
            String s = l <= 99 ? String.format("%02d", l) : String.valueOf(l);
            skuCodeRef.set(itemCode + s);
        });
        return skuCodeRef.get();
    }


    private ItemAttr buildItemAttr(ItemAttrDto itemAttrDto, Long categoryId, Long itemId) {
        ItemAttr itemAttr = new ItemAttr();
        itemAttr.setItemId(itemId);
        itemAttr.setCategoryId(categoryId);
        itemAttr.setAttrId(itemAttrDto.getAttrId());
        itemAttr.setAttrValue(itemAttrDto.getValue());
        return itemAttr;
    }

    private ItemSkuAttrRef buildItemSkuAttrRef(Long itemId, Long itemSkuId, Long itemAttrId) {
        ItemSkuAttrRef itemSkuAttrRef = new ItemSkuAttrRef();
        itemSkuAttrRef.setItemAttrId(itemAttrId);
        itemSkuAttrRef.setItemId(itemId);
        itemSkuAttrRef.setItemSkuId(itemSkuId);
        return itemSkuAttrRef;
    }

}
