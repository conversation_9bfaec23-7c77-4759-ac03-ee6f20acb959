package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 库存告警商品明细表
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stock_warn_item_detail")
public class StockWarnItemDetail extends Entity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 告警记录ID
     */
    private Long warnRecordId;

    /**
     * 商品编号
     */
    private String itemCode;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 实时库存
     */
    private BigDecimal currentStock;

    /**
     * 警戒库存
     */
    private BigDecimal warnStock;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;
}
