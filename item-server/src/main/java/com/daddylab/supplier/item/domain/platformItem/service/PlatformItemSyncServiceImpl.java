package com.daddylab.supplier.item.domain.platformItem.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.daddylab.supplier.item.application.item.ItemBizService;
import com.daddylab.supplier.item.application.platformItem.PlatformItemDao;
import com.daddylab.supplier.item.application.platformItem.PlatformItemInventorySettingBizService;
import com.daddylab.supplier.item.application.platformItem.model.PlatformItemSkuStat;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.platformItem.service.event.PlatformItemUpdateEvent;
import com.daddylab.supplier.item.domain.shop.gateway.ShopGateway;
import com.daddylab.supplier.item.domain.wdt.gateway.WdtGateway;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnStatus;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.PlatformItemWarnType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.NumberUtil;
import com.daddylab.supplier.item.infrastructure.utils.StringUtil;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2022/1/6
 */
@Service
@Slf4j
public class PlatformItemSyncServiceImpl implements PlatformItemSyncService {
    
    @Autowired
    IPlatformItemService platformItemService;
    @Autowired
    IPlatformItemSkuService platformItemSkuService;
    @Autowired
    ItemGateway itemGateway;
    @Autowired
    ItemBizService itemBizService;
    @Autowired
    WdtGateway wdtGateway;
    @Autowired
    ShopGateway shopGateway;
    @Autowired
    IItemService itemService;
    @Autowired
    IItemSkuService itemSkuService;
    @Autowired
    ICombinationItemService combinationItemService;
    @Autowired
    IPlatformItemWarnService platformItemWarnService;
    @Autowired
    IWdtPlatformGoodsService wdtPlatformGoodsService;
    @Autowired
    PlatformItemDao platformItemDao;
    
    @Autowired
    OperateLogDomainService operateLogDomainService;
    
    @Autowired
    PlatformItemInventorySettingBizService platformItemInventorySettingBizService;
    
    @Override
    public Map<String, SyncResult> syncWdtPlatformGoods(String outerItemId) {
        final List<WdtPlatformGoods> wdtPlatformGoods = wdtPlatformGoodsService.lambdaQuery()
                .eq(WdtPlatformGoods::getGoodsId,
                        outerItemId).list();
        if (wdtPlatformGoods.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, SyncResult> results = Maps.newHashMap();
        for (WdtPlatformGoods wdtPlatformGood : wdtPlatformGoods) {
            final SyncResult syncResult = syncWdtPlatformGoods(wdtPlatformGood);
            results.put(wdtPlatformGood.getSpecId(), syncResult);
        }
        return results;
    }
    
    @Override
    public Map<String, SyncResult> syncWdtPlatformGoodsByItemId(Long itemId) {
        final Item item = itemService.getById(itemId);
        if (item == null) {
            return Collections.emptyMap();
        }
        List<String> itemCodeList = new ArrayList<>(2);
        itemCodeList.add(item.getCode());
        if (StringUtil.isNotBlank(item.getPartnerProviderItemSn())) {
            itemCodeList.add(item.getPartnerProviderItemSn());
        }
        final List<WdtPlatformGoods> wdtPlatformGoods = wdtPlatformGoodsService.lambdaQuery()
                .in(WdtPlatformGoods::getOuterId,
                        itemCodeList).list();
        if (wdtPlatformGoods.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<String, SyncResult> results = Maps.newHashMap();
        for (WdtPlatformGoods wdtPlatformGood : wdtPlatformGoods) {
            final SyncResult syncResult = syncWdtPlatformGoods(wdtPlatformGood);
            results.put(wdtPlatformGood.getSpecId(), syncResult);
        }
        return results;
    }
    
    @Override
    public SyncResult syncWdtPlatformGoods(WdtPlatformGoods wdtPlatformGood) {
        final String goodsId = wdtPlatformGood.getGoodsId();
        final String specId = wdtPlatformGood.getSpecId();
        
        final PlatformItemStatus platformItemStatus = IEnum
                .getEnumByValue(PlatformItemStatus.class, wdtPlatformGood.getStatus());
        
        //根据店铺编号匹配店铺
        Optional<Shop> shopOpt = Optional
                .ofNullable(shopGateway.batchQueryShopBySn(ImmutableList.of(wdtPlatformGood.getShopNo()))
                        .get(wdtPlatformGood.getShopNo()));
        if (!shopOpt.isPresent()) {
            return new SyncResult(goodsId, SyncStatus.ERROR_SHOP_NO_MATCH, null, null);
        }
        //取店铺对应的平台
        final Platform platform = shopOpt.map(Shop::getPlatform).orElse(null);
        
        //自库存管理3.0版本后，除[淘宝、有赞]外的其它平台数据都改为直接从平台接口获取
        if (platform != Platform.TAOBAO && platform != Platform.YOUZAN) {
            return new SyncResult(goodsId, SyncStatus.IGNORE, null, null);
        }
        
        if (StringUtil.isBlank(goodsId)) {
            return new SyncResult(goodsId, SyncStatus.ERROR_NO_GOODS_ID, null, null);
        }
        if (StringUtil.isBlank(specId)) {
            return new SyncResult(goodsId, SyncStatus.ERROR_NO_SPEC_ID, null, null);
        }
        
        final PlatformItem platformItem = new PlatformItem();
        platformItem.setGoodsName(wdtPlatformGood.getGoodsName());
        platformItem.setOuterItemId(wdtPlatformGood.getGoodsId());
        platformItem.setOuterItemCode(wdtPlatformGood.getOuterId());
        platformItem.setShopId(shopOpt.map(Shop::getId).orElse(0L));
        platformItem.setShopName(wdtPlatformGood.getShopName());
        platformItem.setShopNo(wdtPlatformGood.getShopNo());
        platformItem.setPlatform(shopOpt.map(Shop::getPlatform).orElse(null));
        
        final PlatformItemSku platformItemSku = new PlatformItemSku();
        platformItemSku.setPlatformItemId(platformItem.getId());
        platformItemSku.setOuterItemId(platformItem.getOuterItemId());
        platformItemSku.setOuterItemCode(platformItem.getOuterItemCode());
        platformItemSku.setShopNo(platformItem.getShopNo());
        platformItemSku.setOuterSkuId(wdtPlatformGood.getSpecId());
        platformItemSku.setOuterSkuCode(
                StringUtil.isNotEmpty(wdtPlatformGood.getMerchantNo()) ? wdtPlatformGood.getMerchantNo() :
                        wdtPlatformGood.getSpecOuterId());
        platformItemSku.setStatus(platformItemStatus);
        platformItemSku.setPrice(wdtPlatformGood.getPrice());
        platformItemSku.setStock(wdtPlatformGood.getStockNum().intValue());
        platformItemSku.setSpecName(wdtPlatformGood.getSpecName());
        platformItemSku.setModified(wdtPlatformGood.getModified());
        platformItemSku.setPlatform(platformItem.getPlatform());
        
        //匹配已存在的平台商品，不存在则新建
        Optional<PlatformItem> platformItemOpt = getPlatformItem(wdtPlatformGood);
        platformItemOpt.ifPresent(v -> platformItem.setId(v.getId()));
        
        //检查是否有匹配的平台商品SKU，不存在则新建
        Optional<PlatformItemSku> platformItemSkuOpt = getPlatformItemSku(specId, platformItem);
        platformItemSkuOpt.ifPresent(v -> platformItemSku.setId(v.getId()));
        
        matchAndSave(platformItem, platformItemSku);
        
        //后端商品未匹配
        if (platformItemSku.getSkuId() == null) {
            return new SyncResult(goodsId, SyncStatus.ERROR_ITEM_NO_MATCH, platformItem, platformItemSku);
        }
        return new SyncResult(goodsId, SyncStatus.SUCCESS, platformItem, platformItemSku);
    }
    
    @Override
    public void matchAndSave(PlatformItem platformItem, PlatformItemSku platformItemSku) {
        final boolean isNewItem = platformItem.getId() == null;
        final boolean isNewSku = platformItemSku.getId() == null;
        
        boolean outerSkuCodeModified = platformItemSku.isOuterSkuCodeModified();
        boolean stockChanged = platformItemSku.isStockChanged();
        
        if (platformItemSku.isHidden()) {
            if (isNewSku) {
                return;
            } else {
                platformItemSkuService.removeByIdWithTime(platformItemSku.getId());
                if (!isNewItem) {
                    operateLogDomainService.addOperatorLog(0L, OperateLogTarget.PLATFORM_ITEM, platformItem.getId(),
                            "删除平台商品SKU: " + platformItemSku.getOuterSkuId());
                }
            }
        } else {
            //匹配店铺
            matchShop(platformItem, platformItemSku);
            
            //平台商品匹配后端商品（OR 组合装）
            matchItem(platformItem, platformItemSku);
            
            platformItemService.saveOrUpdate(platformItem);
            if (isNewItem) {
                operateLogDomainService.addOperatorLog(0L, OperateLogTarget.PLATFORM_ITEM, platformItem.getId(),
                        "新增平台商品: " + platformItem.getOuterItemId());
            }
            
            platformItemSku.setPlatformItemId(platformItem.getId());
            platformItemSkuService.saveOrUpdate(platformItemSku);
            if (isNewSku) {
                operateLogDomainService.addOperatorLog(0L, OperateLogTarget.PLATFORM_ITEM, platformItem.getId(),
                        "新增平台商品SKU: " + platformItemSku.getOuterSkuId());
            }
        }
        
        //更新平台商品主记录统计数据
        updatePlatformItemStat(platformItem);
        
        final PlatformItemUpdateEvent event = new PlatformItemUpdateEvent();
        event.setPlatformItem(platformItem);
        event.setPlatformItemSku(platformItemSku);
        event.setNewItem(isNewItem);
        event.setNewSku(isNewSku);
        event.setOuterSkuCodeChanged(outerSkuCodeModified);
        event.setStockChanged(stockChanged);
        
        EventBusUtil.post(event, true);
    }
    
    private void matchShop(PlatformItem platformItem, PlatformItemSku platformItemSku) {
        if (platformItem.getShopId() == null && StringUtil.isNotEmpty(platformItem.getShopNo())) {
            shopGateway.getByShopNo(platformItem.getShopNo()).ifPresent(shop -> {
                platformItem.setShopId(shop.getId());
                platformItem.setShopName(shop.getName());
                platformItem.setShopNo(shop.getSn());
            });
        }
        platformItemSku.setShopNo(platformItem.getShopNo());
    }
    
    @Override
    public void matchItem(PlatformItem platformItem, PlatformItemSku platformItemSku) {
        //检查是否后端商品是否有匹配的SKU
        final Optional<ItemSku> itemSkuOpt = matchItemSku(platformItemSku.getOuterSkuCode());
        
        //商品取SKU对应的商品
        final Optional<Item> itemOpt = itemSkuOpt.map(ItemSku::getItemId).map(this::getItem);
        
        if (itemOpt.isPresent()) {
            platformItemSku.setItemMatch(1);
        }
        
        //根据SPU编码匹配
        final String outerItemCode = platformItemSku.getOuterItemCode();
        final Optional<Item> itemMatchByOuterItemCodeOpt =
                Optional.ofNullable(itemService.getByMixedCode(outerItemCode));
        
        if (NumberUtil.isZeroOrNull(platformItem.getItemId())) {
            platformItem.setItemId(itemMatchByOuterItemCodeOpt.map(Item::getId).orElse(0L));
            platformItem.setItemCode(itemMatchByOuterItemCodeOpt.map(Item::getCode).orElse(""));
            platformItem.setCategoryId(itemMatchByOuterItemCodeOpt.map(Item::getCategoryId).orElse(0L));
        }
        
        if (NumberUtil.isZeroOrNull(platformItem.getItemId())) {
            platformItem.setItemId(itemOpt.map(Item::getId).orElse(0L));
            platformItem.setItemCode(itemOpt.map(Item::getCode).orElse(""));
            platformItem.setCategoryId(itemOpt.map(Item::getCategoryId).orElse(0L));
        }
        
        platformItemSku.setItemId(itemOpt.map(Item::getId).orElse(0L));
        platformItemSku.setSkuId(itemSkuOpt.map(ItemSku::getId).orElse(0L));
        platformItemSku.setSkuCode(itemSkuOpt.map(ItemSku::getSkuCode).orElse(""));
        
        //匹配组合装
        CombinationItem combinationItem = combinationItemService.getByItemCode(platformItemSku.getOuterSkuCode());
        if (combinationItem != null) {
            platformItemSku.setItemMatch(2);
            platformItemSku.setCombinationItemId(combinationItem.getId());
        } else {
            platformItemSku.setCombinationItemId(0L);
        }
    }
    
    @Override
    public void updatePlatformItemStat(Long platformItemId) {
        final PlatformItem platformItem = platformItemService.getById(platformItemId);
        if (platformItem != null) {
            updatePlatformItemStat(platformItem);
        }
    }
    
    @Override
    public int deleteInvalidPlatformItem(String shopNo, Long invalidTime) {
        final PlatformItemSkuMapper platformItemSkuMapper = platformItemSkuService.getDaddyBaseMapper();
        int page = 1;
        int limit = 1000;
        long timeOffset = 0;
        long idOffset = 0;
        int num = 0;
        while (true) {
            final List<PlatformItemSku> platformItemSkus = platformItemSkuMapper.selectInvalidSkuList(shopNo,
                    invalidTime, timeOffset, idOffset, limit);
            if (platformItemSkus.isEmpty()) {
                break;
            }
            
            final List<Long> platformItemSkuIds = platformItemSkus.stream()
                    .map(PlatformItemSku::getId)
                    .collect(Collectors.toList());
            platformItemSkuService.removeByIdsWithTime(platformItemSkuIds);
            final List<Long> platformItemIds = platformItemSkus.stream()
                    .map(PlatformItemSku::getPlatformItemId)
                    .distinct()
                    .collect(Collectors.toList());
            platformItemSkuMapper.updatePlatformItemStats(platformItemIds);
            
            PlatformItemSku lastSku = platformItemSkus.get(platformItemSkus.size() - 1);
            timeOffset = lastSku.getUpdatedAt();
            idOffset = lastSku.getId();
            log.info("[删除失效平台商品][店铺 {}]第{}页数据已处理，更新时间偏移=[{}/{}]", shopNo, page, timeOffset,
                    idOffset);
            page++;
            num += platformItemSkus.size();
        }
        return num;
    }
    
    private void updatePlatformItemStat(PlatformItem platformItem) {
        final PlatformItemSkuStat platformItemSkuStat = platformItemDao.statSku(platformItem.getId());
        platformItem.setSkuNum(platformItemSkuStat.getSkuNum());
        platformItem.setPrice(platformItemSkuStat.getAvgPrice());
        platformItem.setStock(platformItemSkuStat.getTotalStockNum());
        platformItem.setModified(platformItemSkuStat.getLastModified());
        platformItem.setStatus(
                platformItemSkuStat.getOnSaleCount() == 0 ? PlatformItemStatus.SALE_OUT
                        : PlatformItemStatus.ON_SALE);
        if (platformItem.getFirstSalesTime() == null || platformItem.getFirstSalesTime() == 0) {
            platformItem.setFirstSalesTime(platformItem.getCreatedAt());
        }
        if (platformItem.getStatus() == PlatformItemStatus.SALE_OUT) {
            platformItem.setUnListingTime(DateUtil.toTime(platformItemSkuStat.getLastModified()));
        } else {
            platformItem.setUnListingTime(0L);
        }
        platformItemService.saveOrUpdate(platformItem);
    }
    
    private void handleItemNotMatch(PlatformItem platformItem,
                                    PlatformItemSku platformItemSku) {
        final LambdaQueryWrapper<PlatformItemWarn> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(PlatformItemWarn::getPlatformItemId, platformItem.getId());
        queryWrapper.eq(PlatformItemWarn::getPlatformItemSkuId, platformItemSku.getId());
        queryWrapper.eq(PlatformItemWarn::getWarnType, PlatformItemWarnType.SKU_NOT_MATCH);
        queryWrapper.last("LIMIT 1");
        final PlatformItemWarn existWarn = platformItemWarnService.getOne(queryWrapper);
        if (Objects.nonNull(existWarn)) {
            
            //如果警告存在且平台商品已经匹配到后端商品，则将警告移除
            if (NumberUtil.isPositive(platformItemSku.getSkuId()) && PlatformItemWarnStatus.UNHANDLED
                    .equals(existWarn.getStatus())) {
                
                platformItemWarnService.removeById(existWarn.getId());
            } else {
                existWarn.setUpdatedAt(DateUtil.currentTime());
                existWarn.setUpdatedUid(0L);
                platformItemWarnService.updateById(existWarn);
            }
        } else {
            final PlatformItemWarn platformItemWarn = new PlatformItemWarn();
            platformItemWarn.setPlatformItemId(platformItem.getId());
            platformItemWarn.setPlatformItemSkuId(platformItemSku.getId());
            platformItemWarn.setWarnType(PlatformItemWarnType.SKU_NOT_MATCH);
            platformItemWarn.setStatus(PlatformItemWarnStatus.UNHANDLED);
            platformItemWarnService.save(platformItemWarn);
        }
    }
    
    @NonNull
    private Item getItem(Long itemId) {
        return itemService.lambdaQuery().eq(Item::getId, itemId).one();
    }
    
    private Optional<PlatformItem> getPlatformItem(WdtPlatformGoods wdtPlatformGood) {
        final LambdaQueryWrapper<PlatformItem> platformItemQuery = Wrappers.lambdaQuery();
        platformItemQuery.eq(PlatformItem::getOuterItemId, wdtPlatformGood.getGoodsId());
        platformItemQuery.eq(PlatformItem::getShopNo, wdtPlatformGood.getShopNo());
        return Optional.ofNullable(platformItemService.getOne(platformItemQuery));
    }
    
    private Optional<ItemSku> matchItemSku(String skuCode) {
        if (StringUtil.isBlank(skuCode)) {
            return Optional.empty();
        }
        return itemSkuService.lambdaQuery().eq(ItemSku::getSkuCode, skuCode).or()
                .eq(ItemSku::getProviderSpecifiedCode, skuCode).last("limit 1").oneOpt();
    }
    
    private Optional<PlatformItemSku> getPlatformItemSku(String specId, PlatformItem platformItem) {
        final LambdaQueryWrapper<PlatformItemSku> platformItemSkuQuery = Wrappers.lambdaQuery();
        platformItemSkuQuery.eq(PlatformItemSku::getOuterSkuId, specId)
                .eq(PlatformItemSku::getPlatformItemId, platformItem.getId());
        return Optional.ofNullable(platformItemSkuService.getOne(platformItemSkuQuery));
    }
    
    
}
