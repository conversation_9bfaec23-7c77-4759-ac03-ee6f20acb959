package com.daddylab.supplier.item.domain.stockWarn.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 库存告警信息实体
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Data
public class StockWarnInfo {

    /**
     * 商品编号
     */
    private String itemCode;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 实时库存
     */
    private BigDecimal currentStock;

    /**
     * 警戒库存
     */
    private BigDecimal warnStock;

    /**
     * 仓库ID
     */
    private Long warehouseId;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 订单员ID列表
     */
    private List<Long> orderPersonnelIds;

    /**
     * 订单员花名列表
     */
    private List<String> orderPersonnelNames;

    /**
     * 订单员邮箱列表
     */
    private List<String> orderPersonnelEmails;
}
