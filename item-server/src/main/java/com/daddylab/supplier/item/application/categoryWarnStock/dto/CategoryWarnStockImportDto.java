package com.daddylab.supplier.item.application.categoryWarnStock.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 类目警戒库存导入DTO
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Data
public class CategoryWarnStockImportDto {

    @ExcelProperty("第一级分类")
    private String firstLevelCategory;

    @ExcelProperty("第二级分类")
    private String secondLevelCategory;

    @ExcelProperty("第三级分类")
    private String thirdLevelCategory;

    @ExcelProperty("数量")
    private BigDecimal warnStock;
}