package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryWarnStock;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CategoryWarnStockMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryWarnStockService;
import org.springframework.stereotype.Service;

/**
 * 类目警戒库存配置表 服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Service
public class CategoryWarnStockServiceImpl extends ServiceImpl<CategoryWarnStockMapper, CategoryWarnStock> implements ICategoryWarnStockService {

}
