package com.daddylab.supplier.item.application.purchase.cost.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.daddylab.supplier.item.controller.item.dto.ItemBuyerDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> up
 * @date 2025年09月12日 17:27
 */
@Data
public class CostPageVo {

  @ExcelIgnore private Long id;

  @ExcelProperty("审批单号")
  private String no;

  @ExcelIgnore private Long providerId;

  @ExcelProperty("供应商名称")
  private String providerName;

  @ApiModelProperty("变更类型。1.日常成本，2.活动成本，3.阶梯供价")
  @ExcelIgnore
  private Integer type;

  @ExcelProperty("变更类型")
  private String typeStr;

  @ExcelIgnore private List<ItemBuyerDto> buyerDtoList;

  @ExcelProperty(value = "采购员信息")
  private String buyerName;

  @ExcelProperty("审核状态")
  private String statusStr;

  @ApiModelProperty("CostAuditStatus")
  @ExcelIgnore
  private Integer status;

  @ExcelIgnore private Long startTime;

  @ExcelProperty("开始时间")
  private String startTimeStr;

  @ExcelIgnore private Long endTime;

  @ExcelProperty("关联 SKU 数量")
  private Integer relateSkuCount;

  @ExcelProperty("合作方")
  private String corpTypeStr;

  @ExcelIgnore private List<Integer> corpType;
}
