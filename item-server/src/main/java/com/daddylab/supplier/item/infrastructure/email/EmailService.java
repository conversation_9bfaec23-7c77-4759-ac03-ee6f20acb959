package com.daddylab.supplier.item.infrastructure.email;

import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.EmailSendRecord;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IEmailSendRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;

import static com.daddylab.supplier.item.common.enums.ErrorCode.EMAIL_ERROR;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/28 4:45 下午
 * @description
 */
@Slf4j
@Component
public class EmailService {

  @Value("${spring.mail.username}")
  private String mailSender;

  @SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
  @Autowired
  private JavaMailSender javaMailSender;

  @Autowired private IEmailSendRecordService emailSendRecordService;

  /**
   * 发送文本邮件
   *
   * @param recipient 接收人
   * @param subject 邮件主题
   * @param content 邮件内容
   * @return void
   */
  public void sendSimpleMail(String recipient, String subject, String content) {
    EmailSendRecord emailSendRecord =
        emailSendRecordService.recordEmailSend(recipient, subject, content, mailSender);
    try {
      SimpleMailMessage simpleMailMessage = new SimpleMailMessage();
      simpleMailMessage.setFrom(mailSender);
      // 邮件接收人
      simpleMailMessage.setTo(recipient);
      // 邮件主题
      simpleMailMessage.setSubject(subject);
      // 邮件内容
      simpleMailMessage.setText(content);
      javaMailSender.send(simpleMailMessage);
      log.info("[邮件发送] 邮件发送成功 from: {} to: {} sub: {}", mailSender, recipient, subject);
      emailSendRecordService.updateRecordSuccess(emailSendRecord.getId());
    } catch (Exception e) {
      log.error("[邮件发送] 邮件发送失败 from: {} to: {} sub: {}", mailSender, recipient, subject, e);
      emailSendRecordService.updateRecordFailed(emailSendRecord.getId(), e.getMessage());
      throw ExceptionPlusFactory.bizException(EMAIL_ERROR, "邮件发送失败");
    }
  }

  public void sendHtmlMail(String recipient, String subject, String content) {
    EmailSendRecord emailSendRecord =
        emailSendRecordService.recordEmailSend(recipient, subject, content, mailSender);
    MimeMessage mimeMailMessage;
    try {
      mimeMailMessage = javaMailSender.createMimeMessage();
      MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMailMessage, true);
      mimeMessageHelper.setFrom(new InternetAddress("<EMAIL>"));
      mimeMessageHelper.setTo(recipient);
      mimeMessageHelper.setSubject(subject);
      mimeMessageHelper.setText(content, true);
      javaMailSender.send(mimeMailMessage);
      log.info("[邮件发送] 邮件发送成功 from: {} to: {} sub: {}", mailSender, recipient, subject);
      emailSendRecordService.updateRecordSuccess(emailSendRecord.getId());
    } catch (Exception e) {
      log.error("[邮件发送] 邮件发送失败 from: {} to: {} sub: {}", mailSender, recipient, subject, e);
      emailSendRecordService.updateRecordFailed(emailSendRecord.getId(), e.getMessage());
      throw ExceptionPlusFactory.bizException(EMAIL_ERROR, "邮件发送失败");
    }
  }

  /**
   * 发送带跳转链接的邮件
   *
   * @param recipient 接收人
   * @param subject 邮件主题
   * @param content 邮件内容
   * @param url 跳转链接
   */
  public void sendUrlMail(String recipient, String subject, String content, String url) {
    EmailSendRecord emailSendRecord =
        emailSendRecordService.recordEmailSend(recipient, subject, content, mailSender);
    MimeMessage mimeMailMessage;
    try {
      mimeMailMessage = javaMailSender.createMimeMessage();
      MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMailMessage, true);
      mimeMessageHelper.setFrom(new InternetAddress("<EMAIL>"));
      mimeMessageHelper.setTo(recipient);
      mimeMessageHelper.setSubject(subject);
      mimeMessageHelper.setText(
          "<!DOCTYPE html>\n"
              + "<html>\n"
              + "\t<head>\n"
              + "\t\t<meta charset='utf-8'/>\n"
              + "\t\t<title>邮件</title>\n"
              + "\t</head>\n"
              + "\t<body>\n"
              + "\t\t<div id=\"content\">\n"
              + "\t\t\t<div>"
              + content
              + "</div>\n"
              + "\t\t\t<a href=\""
              + url
              + "\">点击链接</a>\n"
              + "\t\t</div>\n"
              + "\t</body>\n"
              + "</html>\n",
          true);

      javaMailSender.send(mimeMailMessage);
      log.info("[邮件发送] 邮件发送成功 from: {} to: {} sub: {}", mailSender, recipient, subject);
      emailSendRecordService.updateRecordSuccess(emailSendRecord.getId());
    } catch (Exception e) {
      log.error("[邮件发送] 邮件发送失败 from: {} to: {} sub: {}", mailSender, recipient, subject, e);
      emailSendRecordService.updateRecordFailed(emailSendRecord.getId(), e.getMessage());
      throw ExceptionPlusFactory.bizException(EMAIL_ERROR, "邮件发送失败");
    }
  }

  //    /**
  //     * 发送带附件格式的邮件
  //     *
  //     * @param recipient 接收人
  //     * @param subject   邮件主题
  //     * @param content   邮件内容
  //     * @return void
  //     */
  //    public void sendAttachmentMail(String recipient, String subject, String content) {
  //        MimeMessage mimeMailMessage = null;
  //        try {
  //            mimeMailMessage = javaMailSender.createMimeMessage();
  //            MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMailMessage, true);
  //            mimeMessageHelper.setTo(recipient);
  //            mimeMessageHelper.setSubject(subject);
  //            mimeMessageHelper.setText(content);
  //            //文件路径
  //            FileSystemResource file = new FileSystemResource(new
  // File("src/main/resources/static/image/mail.png"));
  //            mimeMessageHelper.addAttachment("mail.png", file);
  //
  //            javaMailSender.send(mimeMailMessage);
  //        } catch (Exception e) {
  //            log.error("邮件发送失败: {}", e.getMessage());
  //            throw ExceptionPlusFactory.bizException(EMAIL_ERROR, "邮件发送失败");
  //        }
  //    }
  //
  //    /**
  //     * 发送带静态资源的邮件
  //     *
  //     * @param recipient 接收人
  //     * @param subject   邮件主题
  //     * @param content   邮件内容
  //     * @return void
  //     */
  //    public void sendInlineMail(String recipient, String subject, String content) {
  //        MimeMessage mimeMailMessage = null;
  //        try {
  //            mimeMailMessage = javaMailSender.createMimeMessage();
  //            MimeMessageHelper mimeMessageHelper = new MimeMessageHelper(mimeMailMessage, true);
  //            mimeMessageHelper.setTo(recipient);
  //            mimeMessageHelper.setSubject(subject);
  //            mimeMessageHelper.setText("<!DOCTYPE html>\n" +
  //                    "<html>\n" +
  //                    "\t<head>\n" +
  //                    "\t\t<meta charset='utf-8'/>\n" +
  //                    "\t\t<title>邮件</title>\n" +
  //                    "\t</head>\n" +
  //                    "\t<body>\n" +
  //                    "\t\t<div id=\"content\">\n" +
  //                    "\t\t\t<div>" + content + "</div>\n" +
  //                    "\t\t\t<img src='cid:picture'/>\n" +
  //                    "\t\t</div>\n" +
  //                    "\t</body>\n" +
  //                    "</html>\n", true);
  //
  //            // 文件路径
  //            FileSystemResource file = new FileSystemResource(new
  // File("src/main/resources/static/image/mail.png"));
  //            mimeMessageHelper.addInline("picture", file);
  //
  //            javaMailSender.send(mimeMailMessage);
  //        } catch (Exception e) {
  //            log.error("邮件发送失败: {}", e.getMessage());
  //            throw ExceptionPlusFactory.bizException(EMAIL_ERROR, "邮件发送失败");
  //        }
  //    }

}
