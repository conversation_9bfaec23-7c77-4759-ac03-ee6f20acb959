package com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 库存告警记录表
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("stock_warn_record")
public class StockWarnRecord extends Entity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 发送目标用户ID
     */
    private Long targetUserId;

    /**
     * 发送目标用户花名
     */
    private String targetUserName;

    /**
     * 邮件地址
     */
    private String emailAddress;

    /**
     * 发送状态 0:待发送 1:发送成功 2:发送失败
     */
    private Integer sendStatus;

    /**
     * 附件OSS链接
     */
    private String attachmentOssUrl;

    /**
     * 发送时间
     */
    private Long sendTime;

    /**
     * 发送失败时的错误信息
     */
    private String errorMessage;
}
