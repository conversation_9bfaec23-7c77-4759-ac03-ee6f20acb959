package com.daddylab.supplier.item.controller.purchase;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.nacos.shaded.com.google.common.graph.ImmutableNetwork;
import com.daddylab.supplier.item.application.purchase.cost.PurchaseCostChangeBizService;
import com.daddylab.supplier.item.application.purchase.cost.dto.CostFormDto;
import com.daddylab.supplier.item.application.purchase.cost.dto.CostPageQuery;
import com.daddylab.supplier.item.application.purchase.cost.dto.CostPageVo;
import com.daddylab.supplier.item.application.purchase.cost.dto.MultiSkuPriceFullDTO;
import com.daddylab.supplier.item.domain.daddylabWorkbench.DaddylabWorkbenchGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.infrastructure.authorize.Auth;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OperateLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PurchaseCostChangeProcessForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.PurchaseOrderProcessForm;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import liquibase.pro.packaged.M;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.analysis.function.Sin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import rx.Single;

import javax.validation.constraints.NotNull;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR> up
 * @date 2025年09月15日 10:03
 */
@Slf4j
@Api(value = "采购成本价格变更相关API", tags = "采购成本价格变更相关API")
@RestController
@RequestMapping("/purchaseCost")
public class PurchaseCostChangeController {

  @Autowired PurchaseCostChangeBizService purchaseCostBizService;
  @Autowired OperateLogGateway operateLogGateway;

  @ResponseBody
  @ApiOperation(value = "采购成本价格变更审核单分页列表")
  @PostMapping("/page")
  public PageResponse<CostPageVo> page(@RequestBody CostPageQuery pageQuery) {
    return purchaseCostBizService.page(pageQuery);
  }

  @ResponseBody
  @ApiOperation(value = "采购成本价格变更审核单导出")
  @PostMapping("/export")
  public SingleResponse<Boolean> export(@RequestBody CostPageQuery pageQuery) {
    purchaseCostBizService.export(pageQuery);
    return SingleResponse.of(true);
  }

  @ResponseBody
  @ApiOperation(value = "查看审核单详情")
  @GetMapping("/viewForm")
  public SingleResponse<CostFormDto> viewForm(Long id) {
    return purchaseCostBizService.viewForm(id);
  }

  @ResponseBody
  @ApiOperation(value = "保存审核单")
  @PostMapping("/save")
  public SingleResponse<Long> save(@RequestBody CostFormDto id) {
    return purchaseCostBizService.save(id);
  }

  @ResponseBody
  @ApiOperation(value = "导入")
  @PostMapping("/import")
  @Auth(noAuth = true)
  public MultiResponse<MultiSkuPriceFullDTO> importExcel(
      @ApiParam(value = "价格明细Excel", required = true) @RequestParam MultipartFile file,
      @ApiParam(value = "0:活动价 1:sku日常阶梯价。2:sku活动阶梯价。3:spu日常阶梯价。4:spu活动阶梯价") Integer type) {
    return MultiResponse.of(purchaseCostBizService.importExcel(file, type));
  }

  @ResponseBody
  @ApiOperation(value = "删除审核单")
  @GetMapping("/delete")
  public SingleResponse<Boolean> delete(Long id) {
    return purchaseCostBizService.delete(id);
  }

  @ResponseBody
  @ApiOperation(value = "提交审核单")
  @GetMapping("/submit")
  public SingleResponse<Boolean> submit(Long id) {
    return purchaseCostBizService.submit(id);
  }

  @ResponseBody
  @ApiOperation(value = "撤销审核")
  @GetMapping("/cancel")
  public SingleResponse<Boolean> cancel(Long id) {
    return purchaseCostBizService.cancel(id);
  }

  @ResponseBody
  @ApiOperation(value = "操作记录")
  @GetMapping("/operateLog")
  public MultiResponse<OperateLog> operateLog(Long id) {
    final List<OperateLog> operateLogs =
        operateLogGateway.getOperateLogs(OperateLogTarget.PURCHASE_COST_CHANGE, id);
    return MultiResponse.of(operateLogs);
  }

  @ResponseBody
  @ApiOperation(value = "更新状态")
  @GetMapping("/updateStatus")
  public SingleResponse<Boolean> updateStatus(Long id, Integer status) {
    return purchaseCostBizService.updateStatus(id, status);
  }

  @ResponseBody
  @GetMapping("/form")
  public SingleResponse<PurchaseCostChangeProcessForm> form(Long id) {
    final PurchaseCostChangeProcessForm purchaseCostChangeProcessForm =
        purchaseCostBizService.buildWorkbenchForm(id);
    return SingleResponse.of(purchaseCostChangeProcessForm);
  }

  @ResponseBody
  @GetMapping("/startProcess")
  public SingleResponse<Boolean> startProcess(Long id) {
    SpringUtil.getBean(DaddylabWorkbenchGateway.class).syncPurchaseCostChange(id);
    return SingleResponse.of(true);
  }
}
