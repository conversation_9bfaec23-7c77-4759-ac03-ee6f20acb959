package com.daddylab.supplier.item.domain.stockWarn.entity;

import lombok.Data;

import java.util.List;

/**
 * 库存告警汇总信息
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Data
public class StockWarnSummary {

    /**
     * 订单员ID
     */
    private Long orderPersonnelId;

    /**
     * 订单员花名
     */
    private String orderPersonnelName;

    /**
     * 订单员邮箱
     */
    private String orderPersonnelEmail;

    /**
     * 告警商品列表
     */
    private List<StockWarnInfo> warnItems;

    /**
     * 告警商品数量
     */
    private Integer warnItemCount;
}
