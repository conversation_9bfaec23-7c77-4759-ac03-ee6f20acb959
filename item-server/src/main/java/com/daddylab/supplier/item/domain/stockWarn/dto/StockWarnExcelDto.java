package com.daddylab.supplier.item.domain.stockWarn.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 库存告警Excel导出DTO
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Data
public class StockWarnExcelDto {

    @ExcelProperty("商品编号")
    private String itemCode;

    @ExcelProperty("商品名称")
    private String itemName;

    @ExcelProperty("类目名称")
    private String categoryName;

    @ExcelProperty("实时库存")
    private BigDecimal currentStock;

    @ExcelProperty("警戒库存")
    private BigDecimal warnStock;

    @ExcelProperty("仓库名称")
    private String warehouseName;

    @ExcelProperty("库存差异")
    private BigDecimal stockDiff;

    @ExcelProperty("告警级别")
    private String warnLevel;
}
