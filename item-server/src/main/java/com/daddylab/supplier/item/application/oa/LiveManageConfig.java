package com.daddylab.supplier.item.application.oa;

import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.ImmutableMap;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * OA合同类型配置
 *
 * <AUTHOR>
 * @since 2025-09-22
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "live-manage")
@RefreshScope
public class LiveManageConfig {
  
  private Long douyinOpenAppId = 3L;
  private Long douyinOauthTokenId = 1L;
  private Long qianchuanOpenAppId = 4L;
  private Long qianchuanOauthTokenId = 2L;
  private Long qianchuanAccountId = 1671524909731851L;

  /** 常量字典配置 */
  private String constDictConfig =
      "{\"contractType1\":{\"-3247599047144420482\":\"收入合同\",\"-3951052332005439382\":\"支出合同\"},\"contractType2\":{\"6343187491232434609\":\"推广服务合同\",\"-4953342512067753076\":\"销售合同\",\"-2768530298000028544\":\"收入补充协议\",\"-4519881019499393577\":\"支出补充协议\",\"-3249331131387130689\":\"抽检服务合同\",\"-8269357454344040566\":\"电商采购合同\",\"-7144556016924735554\":\"定制合同\",\"-6063727517193029087\":\"其他采购合同\",\"-8199960393469608712\":\"技术服务合同\",\"-827417074596989375\":\"服务类合同\"},\"contractType3\":{\"-*****************\":\"纯佣\",\"-6882978803156695876\":\"保ROI\",\"-927181679874060637\":\"资源包\",\"-5823115754085792438\":\"加工承揽框架合同\",\"-6019339138934161263\":\"采购订单\"}}";

  public Map<String, Map<String, String>> getConstDict() {
    return JsonUtil.parse(constDictConfig, new TypeReference<Map<String, Map<String, String>>>() {});
  }
  
}
