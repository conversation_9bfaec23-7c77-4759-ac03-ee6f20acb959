package com.daddylab.supplier.item.domain.stockWarn.service;

import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnInfo;
import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnSummary;

import java.util.List;

/**
 * 库存告警领域服务
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
public interface StockWarnDomainService {

    /**
     * 检查库存告警
     * 获取旺店通库存数据，判断是否低于警戒库存
     *
     * @return 告警商品信息列表
     */
    List<StockWarnInfo> checkStockWarn();

    /**
     * 按订单员汇总告警信息
     *
     * @param warnInfos 告警商品信息列表
     * @return 按订单员汇总的告警信息
     */
    List<StockWarnSummary> summarizeWarnByOrderPersonnel(List<StockWarnInfo> warnInfos);

    /**
     * 生成Excel文件并上传到OSS
     *
     * @param summary 告警汇总信息
     * @return OSS文件链接
     */
    String generateAndUploadExcel(StockWarnSummary summary);

    /**
     * 发送告警邮件
     *
     * @param summary 告警汇总信息
     * @param ossUrl OSS文件链接
     */
    void sendWarnEmail(StockWarnSummary summary, String ossUrl);
}
