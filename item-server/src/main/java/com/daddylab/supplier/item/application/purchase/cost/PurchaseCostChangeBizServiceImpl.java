package com.daddylab.supplier.item.application.purchase.cost;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.daddylab.supplier.item.application.purchase.cost.dto.*;
import com.daddylab.supplier.item.application.purchase.order.factory.dto.QuantityCombinedPriceBO;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.ExportSingleSkuRow;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.excel.PurchasePriceReadExcelController;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.ProcessTypeEnum;
import com.daddylab.supplier.item.controller.item.dto.ItemBuyerDto;
import com.daddylab.supplier.item.domain.daddylabWorkbench.DaddylabWorkbenchGateway;
import com.daddylab.supplier.item.domain.daddylabWorkbench.WorkbenchCallbackEvent;
import com.daddylab.supplier.item.domain.daddylabWorkbench.WorkbenchCallbackState;
import com.daddylab.supplier.item.domain.exportTask.ExportManager;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.gateway.OperateLogGateway;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.purchase.enums.FavourableType;
import com.daddylab.supplier.item.domain.purchase.enums.PurchaseIsActive;
import com.daddylab.supplier.item.domain.purchase.enums.PurchasePlatform;
import com.daddylab.supplier.item.domain.purchase.enums.PurchaseType;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusListener;
import com.daddylab.supplier.item.infrastructure.enums.IEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.ExportTaskType;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.DaddylabWorkbenchFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PurchaseCostChangeProcessForm;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.DaddylabWorkbenchResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.WorkbenchCommonDto;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.eventbus.Subscribe;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.stream.Collectors.toList;

/**
 * <AUTHOR> up 1
 * @date 2025年09月15日 10:04
 */
@Service
@Slf4j
@EventBusListener
public class PurchaseCostChangeBizServiceImpl implements PurchaseCostChangeBizService {

  @Autowired IPurchaseCostChangeRecordService iPurchaseCostChangeRecordService;
  @Autowired IProviderService iProviderService;
  @Autowired UserGateway userGateway;
  @Autowired ExportManager exportManager;
  @Autowired IItemService itemService;
  @Autowired IItemSkuService itemSkuService;
  @Autowired ProviderGateway providerGateway;
  @Autowired DaddylabWorkbenchGateway daddylabWorkbenchGateway;
  @Autowired OperateLogGateway operateLogGateway;
  @Autowired DaddylabWorkbenchFeignClient workbenchFeignClient;
  @Autowired ItemSkuGateway itemSkuGateway;
  @Autowired INewGoodsService iNewGoodsService;
  @Autowired IItemSkuPriceService iItemSkuPriceService;
  @Autowired IPurchaseService iPurchaseService;
  @Autowired IPurchaseCostChangeRecordRefService iPurchaseCostChangeRecordRefService;
  @Autowired IPurchaseSingleSkuCombinationPriceService iPurchaseSingleSkuCombinationPriceService;
  @Autowired IPurchaseRandomSkuCombinationPriceService ipurchaseRandomSkuCombinationPriceService;
  @Autowired PurchasePriceReadExcelController readExcelController;

  private static final DecimalFormat df = new DecimalFormat("0000");

  @Override
  public PageResponse<CostPageVo> page(CostPageQuery pageQuery) {

    Set<Long> recordSet = new HashSet<>();
    if (Objects.nonNull(pageQuery.getItemId())) {
      recordSet =
          iPurchaseCostChangeRecordRefService
              .lambdaQuery()
              .eq(PurchaseCostChangeRecordRef::getItemId, pageQuery.getItemId())
              .list()
              .stream()
              .map(PurchaseCostChangeRecordRef::getRecordId)
              .collect(Collectors.toSet());
      if (CollUtil.isEmpty(recordSet)) {
        return PageResponse.of(
            new LinkedList<>(), 0, pageQuery.getPageSize(), pageQuery.getPageIndex());
      }
    }

    Set<Long> finalRecordSet = recordSet;
    final PageInfo<PurchaseCostChangeRecord> pageInfo =
        PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageSize())
            .doSelectPageInfo(
                () ->
                    iPurchaseCostChangeRecordService
                        .lambdaQuery()
                        .in(
                            CollUtil.isNotEmpty(finalRecordSet),
                            PurchaseCostChangeRecord::getId,
                            finalRecordSet)
                        .eq(
                            Objects.nonNull(pageQuery.getStatus()),
                            PurchaseCostChangeRecord::getStatus,
                            pageQuery.getStatus())
                        .eq(
                            StrUtil.isNotBlank(pageQuery.getNo()),
                            PurchaseCostChangeRecord::getNo,
                            pageQuery.getNo())
                        .eq(
                            Objects.nonNull(pageQuery.getProviderId()),
                            PurchaseCostChangeRecord::getProviderId,
                            pageQuery.getProviderId())
                        .like(
                            Objects.nonNull(pageQuery.getBuyerId()),
                            PurchaseCostChangeRecord::getBuyerId,
                            pageQuery.getBuyerId())
                        .ge(
                            Objects.nonNull(pageQuery.getStartTime()),
                            PurchaseCostChangeRecord::getStartTime,
                            pageQuery.getStartTime())
                        .le(
                            Objects.nonNull(pageQuery.getEndTime()),
                            PurchaseCostChangeRecord::getStartTime,
                            pageQuery.getEndTime())
                        .like(
                            StrUtil.isNotBlank(pageQuery.getSkuCode()),
                            PurchaseCostChangeRecord::getSkuCode,
                            pageQuery.getSkuCode())
                        .eq(
                            pageQuery.getCorpType().contains(0),
                            PurchaseCostChangeRecord::getIsMall,
                            1)
                        .eq(
                            pageQuery.getCorpType().contains(2),
                            PurchaseCostChangeRecord::getIsDecoration,
                            1)
                        .eq(
                            Objects.nonNull(pageQuery.getId()),
                            PurchaseCostChangeRecord::getId,
                            pageQuery.getId())
                        .orderByDesc(PurchaseCostChangeRecord::getId)
                        .list());

    final List<CostPageVo> voList =
        pageInfo.getList().stream()
            .map(
                val -> {
                  CostPageVo vo = new CostPageVo();
                  vo.setId(val.getId());
                  vo.setNo(val.getNo());
                  vo.setType(val.getChangeType());
                  vo.setTypeStr(
                      val.getChangeType() == 1
                          ? "日常成本"
                          : (val.getChangeType() == 2 ? "活动成本" : "阶梯供价"));
                  vo.setProviderId(val.getProviderId());
                  final Provider provider = iProviderService.getById(val.getProviderId());
                  vo.setProviderName(Objects.nonNull(provider) ? provider.getName() : "");
                  final String buyerId = val.getBuyerId();
                  if (StrUtil.isNotBlank(buyerId)) {
                    final List<Long> userIdList =
                        Stream.of(buyerId.split(","))
                            .map(Long::valueOf)
                            .collect(Collectors.toList());
                    final List<ItemBuyerDto> collect =
                        userGateway.batchQueryStaffInfoByIds(userIdList).values().stream()
                            .map(
                                v -> {
                                  ItemBuyerDto buyerDto = new ItemBuyerDto();
                                  buyerDto.setBuyerUserId(v.getUserId());
                                  buyerDto.setBuyerUserName(v.getNickname());
                                  return buyerDto;
                                })
                            .collect(Collectors.toList());
                    vo.setBuyerDtoList(collect);
                    final String collect1 =
                        collect.stream()
                            .map(ItemBuyerDto::getBuyerUserName)
                            .collect(Collectors.joining(","));
                    vo.setBuyerName(collect1);
                  } else {
                    vo.setBuyerDtoList(new LinkedList<>());
                    vo.setBuyerName("");
                  }
                  vo.setStatus(val.getStatus());
                  vo.setStatusStr(getStatusStr(val.getStatus()));
                  vo.setStartTime(val.getStartTime());
                  vo.setStartTimeStr(DateUtil.format(val.getStartTime()));
                  vo.setRelateSkuCount(val.getSkuCount());
                  List<Integer> corpTypeList = new LinkedList<>();
                  StringBuilder corpTypeStr = new StringBuilder();
                  if (val.getIsMall() == 1) {
                    corpTypeList.add(0);
                    corpTypeStr.append("电商");
                  }
                  if (val.getIsDecoration() == 1) {
                    corpTypeList.add(2);
                    corpTypeStr.append("绿色家装");
                  }
                  vo.setCorpType(corpTypeList);
                  vo.setCorpTypeStr(corpTypeStr.toString());
                  return vo;
                })
            .collect(Collectors.toList());
    return PageResponse.of(
        voList, (int) pageInfo.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
  }

  private String getStatusStr(Integer status) {
    return IEnum.getEnumOptByValue(CostAuditStatus.class, status)
        .map(CostAuditStatus::getDesc)
        .orElse("");
  }

  @Override
  public SingleResponse<CostFormDto> viewForm(Long id) {
    final PurchaseCostChangeRecord val = iPurchaseCostChangeRecordService.getById(id);
    Assert.notNull(val, "ID 非法");

    CostFormDto dto = new CostFormDto();
    dto.setId(val.getId());
    dto.setType(val.getChangeType());
    dto.setStatus(val.getStatus());

    List<Integer> corpTypeList = new LinkedList<>();
    if (val.getIsMall() == 1) {
      corpTypeList.add(0);
    }
    if (val.getIsDecoration() == 1) {
      corpTypeList.add(2);
    }
    dto.setCorpType(corpTypeList);
    dto.setProviderId(val.getProviderId());
    final Provider provider = providerGateway.getById(val.getProviderId());
    dto.setProviderName(Objects.nonNull(provider) ? provider.getName() : "");

    final List<Long> userIdList =
        Stream.of(val.getBuyerId().split(",")).map(Long::valueOf).collect(Collectors.toList());
    final List<ItemBuyerDto> collect =
        userGateway.batchQueryStaffInfoByIds(userIdList).values().stream()
            .map(
                v -> {
                  ItemBuyerDto buyerDto = new ItemBuyerDto();
                  buyerDto.setBuyerUserId(v.getUserId());
                  buyerDto.setBuyerUserName(v.getNickname());
                  return buyerDto;
                })
            .collect(Collectors.toList());
    dto.setBuyerDtoList(collect);
    final List<Long> collect1 =
        collect.stream().map(ItemBuyerDto::getBuyerUserId).collect(Collectors.toList());
    dto.setBuyerId(collect1);

    dto.setChangeReason(val.getChangeReason());
    dto.setStartTime(val.getStartTime());
    dto.setProofUrl(JsonUtil.parseList(val.getProofUrl(), ProofFileDto.class));
    dto.setCostPriceList(JsonUtil.parseList(val.getCostPriceList(), ItemCostDto.class));
    dto.setActivityPriceList(
        JsonUtil.parseList(val.getActivityPriceList(), MultiSkuPriceFullDTO.class));
    dto.setMultiPriceList(JsonUtil.parseList(val.getMultiPriceList(), MultiSkuPriceFullDTO.class));

    return SingleResponse.of(dto);
  }

  @Override
  public void export(CostPageQuery query) {
    exportManager.export(
        ExportTaskType.PURCHASE_COST_CHANGE,
        CostPageVo.class,
        pageIndex -> {
          CostPageQuery query1 = new CostPageQuery();
          query1.setPageSize(999);
          query1.setPageIndex(pageIndex);
          return page(query1);
        });
  }

  @Override
  public SingleResponse<Long> save(CostFormDto dto) {
    // ID 存在，编辑逻辑，检查参数中的字段是否存在有效值，存在有效值就覆盖。
    if (Objects.nonNull(dto.getId())) {
      final PurchaseCostChangeRecord record = buildRecord(dto);
      iPurchaseCostChangeRecordService.updateById(record);
      updateRef(record);
      return SingleResponse.of(dto.getId());
    }

    final PurchaseCostChangeRecord add = add(dto);
    operateLogGateway.addOperatorLog(
        UserContext.getUserId(),
        OperateLogTarget.PURCHASE_COST_CHANGE,
        add.getId(),
        "新增采购成本变更审核单",
        null);

    return SingleResponse.of(add.getId());
  }

  private PurchaseCostChangeRecord buildRecord(CostFormDto dto) {
    final PurchaseCostChangeRecord record = iPurchaseCostChangeRecordService.getById(dto.getId());
    if (CollUtil.isNotEmpty(dto.getCorpType())) {
      record.setIsMall(dto.getCorpType().contains(0) ? 1 : 0);
      record.setIsDecoration(dto.getCorpType().contains(2) ? 1 : 0);
    }
    if (Objects.nonNull(dto.getProviderId())) {
      record.setProviderId(dto.getProviderId());
    }
    if (Objects.nonNull(dto.getType())) {
      record.setChangeType(dto.getType());
    }
    if (CollUtil.isNotEmpty(dto.getBuyerId())) {
      record.setBuyerId(
          dto.getBuyerId().stream()
              .map(String::valueOf)
              .collect(Collectors.joining(StrUtil.COMMA)));
    }
    if (Objects.nonNull(dto.getStartTime())) {
      record.setStartTime(dto.getStartTime());
    }
    if (CollUtil.isNotEmpty(dto.getProofUrl())) {
      record.setProofUrl(JsonUtil.toJson(dto.getProofUrl()));
    }
    if (StrUtil.isNotBlank(dto.getChangeReason())) {
      record.setChangeReason(dto.getChangeReason());
    }
    if (CollUtil.isNotEmpty(dto.getCostPriceList())) {
      record.setCostPriceList(JsonUtil.toJson(dto.getCostPriceList()));
    }
    if (CollUtil.isNotEmpty(dto.getActivityPriceList())) {
      record.setActivityPriceList(JsonUtil.toJson(dto.getActivityPriceList()));
    }
    if (CollUtil.isNotEmpty(dto.getMultiPriceList())) {
      record.setMultiPriceList(JsonUtil.toJson(dto.getMultiPriceList()));
    }
    return record;
  }

  private PurchaseCostChangeRecord add(CostFormDto dto) {
    PurchaseCostChangeRecord record = new PurchaseCostChangeRecord();
    record.setNo(getNo());
    record.setProviderId(dto.getProviderId());
    record.setChangeType(dto.getType());
    record.setBuyerId(
        dto.getBuyerId().stream().map(String::valueOf).collect(Collectors.joining(StrUtil.COMMA)));
    record.setChangeReason(dto.getChangeReason());
    record.setStartTime(dto.getStartTime());
    record.setProofUrl(JsonUtil.toJson(dto.getProofUrl()));
    record.setIsMall(dto.getCorpType().contains(0) ? 1 : 0);
    record.setIsDecoration(dto.getCorpType().contains(2) ? 1 : 0);
    record.setCostPriceList(JsonUtil.toJson(dto.getCostPriceList()));
    record.setActivityPriceList(JsonUtil.toJson(dto.getActivityPriceList()));
    record.setMultiPriceList(JsonUtil.toJson(dto.getMultiPriceList()));

    final List<String> stringStream1 =
        dto.getCostPriceList().stream().map(ItemCostDto::getSkuCode).collect(Collectors.toList());
    final List<String> stringStream2 =
        dto.getActivityPriceList().stream()
            .map(MultiSkuPriceFullDTO::getSkuCode)
            .collect(Collectors.toList());
    final List<String> stringStream3 =
        dto.getMultiPriceList().stream()
            .map(MultiSkuPriceFullDTO::getSkuCode)
            .collect(Collectors.toList());
    stringStream1.addAll(stringStream2);
    stringStream1.addAll(stringStream3);
    final long count = stringStream1.stream().distinct().count();
    record.setSkuCount((int) count);
    record.setSkuCode(String.join(",", stringStream1));
    iPurchaseCostChangeRecordService.save(record);

    saveRef(stringStream1, record.getId(), record.getNo());
    saveRef(stringStream2, record.getId(), record.getNo());
    saveRef(stringStream3, record.getId(), record.getNo());

    return record;
  }

  private void updateRef(PurchaseCostChangeRecord record) {
    iPurchaseCostChangeRecordRefService
        .lambdaUpdate()
        .eq(PurchaseCostChangeRecordRef::getRecordId, record.getId())
        .remove();
    saveRef(
        JsonUtil.parseList(record.getCostPriceList(), ItemCostDto.class).stream()
            .map(ItemCostDto::getSkuCode)
            .collect(Collectors.toList()),
        record.getId(),
        record.getNo());
    saveRef(
        JsonUtil.parseList(record.getActivityPriceList(), MultiSkuPriceFullDTO.class).stream()
            .map(MultiSkuPriceFullDTO::getSkuCode)
            .collect(Collectors.toList()),
        record.getId(),
        record.getNo());
    saveRef(
        JsonUtil.parseList(record.getMultiPriceList(), MultiSkuPriceFullDTO.class).stream()
            .map(MultiSkuPriceFullDTO::getSkuCode)
            .collect(Collectors.toList()),
        record.getId(),
        record.getNo());
  }

  private void saveRef(List<String> skuCode, Long id, String no) {
    if (CollUtil.isEmpty(skuCode)) {
      return;
    }
    final Set<String> skuCodeSet =
        skuCode.stream().filter(StrUtil::isNotBlank).collect(Collectors.toSet());
    if (CollUtil.isEmpty(skuCodeSet)) {
      return;
    }
    final List<ItemSku> list =
        itemSkuService
            .lambdaQuery()
            .in(ItemSku::getSkuCode, skuCode)
            .or()
            .in(ItemSku::getProviderSpecifiedCode, skuCode)
            .list();
    final Map<String, ItemSku> map1 =
        list.stream()
            .collect(Collectors.toMap(ItemSku::getSkuCode, Function.identity(), (a, b) -> a));
    final Map<String, ItemSku> map2 =
        list.stream()
            .collect(
                Collectors.toMap(
                    ItemSku::getProviderSpecifiedCode, Function.identity(), (a, b) -> a));
    final List<PurchaseCostChangeRecordRef> collect =
        skuCodeSet.stream()
            .map(
                val -> {
                  PurchaseCostChangeRecordRef recordRef = new PurchaseCostChangeRecordRef();
                  recordRef.setNo(no);
                  recordRef.setRecordId(id);
                  recordRef.setSkuCode(val);
                  ItemSku itemSku = map1.get(val);
                  if (Objects.isNull(itemSku)) {
                    itemSku = map2.get(val);
                  }
                  recordRef.setItemId(Objects.nonNull(itemSku) ? itemSku.getItemId() : 0L);
                  return recordRef;
                })
            .collect(toList());

    if (CollUtil.isNotEmpty(collect)) {
      iPurchaseCostChangeRecordRefService.saveBatch(collect);
    }
  }

  @Override
  public List<MultiSkuPriceFullDTO> importExcel(MultipartFile file, Integer type) {
    List<MultiSkuPriceFullDTO> sheetRowsRaw;
    try {
      sheetRowsRaw =
          EasyExcel.read(file.getInputStream())
              .headRowNumber(1)
              .head(MultiSkuPriceFullDTO.class)
              .sheet(0)
              .doReadSync();
    } catch (IOException e) {
      throw new RuntimeException(e);
    }

    final List<MultiSkuPriceFullDTO> collect = sheetRowsRaw.stream().distinct().collect(toList());
    collect.forEach(
        val -> {
          if (StrUtil.isNotBlank(val.getMonth())) {
            Assert.state(
                DateUtil.isValidYearMonth(val.getMonth()),
                val.getMonth() + "此字段值非法，【月份】正确例子 2025/09");
          }
          if (StrUtil.isNotBlank(val.getActivityStartDate())) {
            Assert.state(
                DateUtil.isValidDateTime(val.getActivityStartDate()),
                val.getActivityStartDate() + "此字段值非法，【开始时间】正确例子 2025-09-01 00:00:00");
          }
          if (StrUtil.isNotBlank(val.getActivityEndDate())) {
            Assert.state(
                DateUtil.isValidDateTime(val.getActivityEndDate()),
                val.getActivityEndDate() + "此字段值非法，【结束时间】正确例子 2025-09-01 00:00:00");
          }
          val.setPriceType(type);
        });
    return collect;
  }

  private String getNo() {
    String codePrefix = "CB" + cn.hutool.core.date.DateUtil.today().replace("-", "");
    String initOrderNo = codePrefix + "0001";
    AtomicReference<String> reference = new AtomicReference<>(initOrderNo);

    Optional<PurchaseCostChangeRecord> optional =
        iPurchaseCostChangeRecordService.getLatestCode(codePrefix);
    optional.ifPresent(
        purchaseOrder -> {
          String no = optional.get().getNo();
          String s = no.replaceAll(codePrefix, "");
          reference.set(codePrefix + df.format(Long.parseLong(s) + 1));
        });

    return reference.get();
  }

  @Override
  public SingleResponse<Boolean> delete(Long id) {
    iPurchaseCostChangeRecordService.removeById(id);

    operateLogGateway.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.PURCHASE_COST_CHANGE, id, "删除采购成本变更审核单", null);

    return SingleResponse.of(true);
  }

  @Override
  public SingleResponse<Boolean> submit(Long id) {
    final PurchaseCostChangeRecord val = iPurchaseCostChangeRecordService.getById(id);
    Assert.notNull(val, "ID 非法");
    final List<Integer> integers =
        ListUtil.of(
            CostAuditStatus.WAIT_SUBMIT.getValue(),
            CostAuditStatus.CANCEL.getValue(),
            CostAuditStatus.REJECT.getValue());
    Assert.state(integers.contains(val.getStatus()), "只有待审核/审核拒绝/审核撤回才允许提交单据");

    final Collection<String> skuCodeWhitList =
        providerGateway.skuCodeListByProviderId(val.getProviderId());
    if (val.getChangeType() == 2) {
      List<MultiSkuPriceFullDTO> list =
          JsonUtil.parseList(val.getActivityPriceList(), MultiSkuPriceFullDTO.class);
      final List<String> errroSkuCodeList =
          list.stream()
              .filter(v -> !skuCodeWhitList.contains(val.getSkuCode()))
              .map(MultiSkuPriceFullDTO::getSkuCode)
              .collect(Collectors.toList());
      Assert.state(
          CollUtil.isEmpty(errroSkuCodeList),
          StrUtil.format("{}这批 SKU 不属于此供应商，请重新编辑商品活动价格", String.join(",", errroSkuCodeList)));
    }
    if (val.getChangeType() == 3) {
      List<MultiSkuPriceFullDTO> list =
          JsonUtil.parseList(val.getMultiPriceList(), MultiSkuPriceFullDTO.class);
      final List<String> errroSkuCodeList =
          list.stream()
              .filter(v -> !skuCodeWhitList.contains(val.getSkuCode()))
              .map(MultiSkuPriceFullDTO::getSkuCode)
              .collect(Collectors.toList());
      Assert.state(
          CollUtil.isEmpty(errroSkuCodeList),
          StrUtil.format("{}这批 SKU 不属于此供应商，请重新编辑商品阶梯供价", String.join(",", errroSkuCodeList)));
    }

    Assert.hasText(val.getBuyerId(), "采购员信息不得为空");
    final Long userId = UserContext.getUserId();
    Assert.state(
        ListUtil.of(val.getBuyerId().split(",")).stream()
            .map(Long::valueOf)
            .collect(toList())
            .contains(userId),
        "必须是当前单据的采购员才允许提交审核");

    //    if (CostAuditStatus.WAIT_SUBMIT.getValue().equals(val.getStatus())) {
    operateLogGateway.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.PURCHASE_COST_CHANGE, val.getId(), "提交审核", null);
    final SingleResponse<String> response = daddylabWorkbenchGateway.syncPurchaseCostChange(id);
    Assert.state(response.isSuccess(), response.getErrMessage());
    val.setWorkbenchProcessId(response.getData());
    val.setStatus(CostAuditStatus.WAIT_AUDIT.getValue());
    iPurchaseCostChangeRecordService.updateById(val);

    //    }
    //    if (CostAuditStatus.ROLLBACK_AUDIT.getValue().equals(val.getStatus())) {
    //      Assert.hasText(val.getWorkbenchProcessId(), "审核撤回后重新发起，流程 ID 不得为空");
    //      operateLogGateway.addOperatorLog(
    //          UserContext.getUserId(),
    //          OperateLogTarget.PURCHASE_COST_CHANGE,
    //          val.getId(),
    //          "审核撤回后，重新提交审核",
    //          null);
    //      val.setWorkbenchProcessId(val.getWorkbenchProcessId());
    //    }

    return SingleResponse.of(true);
  }

  @Override
  @Subscribe
  @DistributedLock
  public void workbenchCallback(WorkbenchCallbackEvent event) {
    log.info("[采购成本变更审核]工作台回调: {}", event);
    if (event.getBusinessType() != ProcessTypeEnum.PURCHASE_COST_CHANGE) {
      return;
    }
    PurchaseCostChangeRecord record =
        iPurchaseCostChangeRecordService.getById(event.getBusinessId());
    if (record == null) {
      return;
    }

    // 审核通过，审核拒绝，审核撤销。
    // 三个终态
    if (ListUtil.of(
            CostAuditStatus.PASS.getValue(),
            CostAuditStatus.REJECT.getValue(),
            CostAuditStatus.CANCEL.getValue())
        .contains(record.getStatus())) {
      return;
    }

    String ol =
        StrUtil.isNotBlank(event.getTaskName())
            ? event.getTaskName() + "，审核流工作台回调: " + event.getCallBackState().getDesc()
            : "审核流工作台回调: " + event.getCallBackState().getDesc();
    operateLogGateway.addOperatorLog(
        UserContext.getUserId(), OperateLogTarget.PURCHASE_COST_CHANGE, record.getId(), ol, null);

    if (event.getCallBackState() == WorkbenchCallbackState.AGREE
        && event.getTaskName().contains("采购主管审核")) {
      // 只有待审核状态才能允许更新为审核完成
      if (record.getStatus().equals(CostAuditStatus.WAIT_AUDIT.getValue())) {
        record.setStatus(CostAuditStatus.PASS.getValue());
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.PURCHASE_COST_CHANGE,
            record.getId(),
            "工作台审核流，审核通过。",
            null);
        // 审核通过，后续数据处理
        try {
          passHandler(record);
        } catch (Exception e) {
          log.error("purchaseCostChange handle error", e);
        }
      }

    } else if (event.getCallBackState() == WorkbenchCallbackState.REJECT
        && event.getTaskName().contains("采购主管审核")) {
      if (record.getStatus().equals(CostAuditStatus.WAIT_AUDIT.getValue())) {
        record.setStatus(CostAuditStatus.REJECT.getValue());
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.PURCHASE_COST_CHANGE,
            record.getId(),
            "工作台审核流，审核拒绝。",
            null);
      }
    } else if (Arrays.asList(WorkbenchCallbackState.ABANDON, WorkbenchCallbackState.REVOKE)
            .contains(event.getCallBackState())
        && event.getTaskName().contains("采购主管审核")) {
      if (record.getStatus().equals(CostAuditStatus.WAIT_AUDIT.getValue())) {
        record.setStatus(CostAuditStatus.CANCEL.getValue());
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.PURCHASE_COST_CHANGE,
            record.getId(),
            "工作台审核流，审核废弃/删除或者审核撤销。",
            null);
      }
    }
    iPurchaseCostChangeRecordService.updateById(record);
  }

  public void passHandler(PurchaseCostChangeRecord record) {

    // 日常成本
    if (1 == record.getChangeType()) {
      final String costPriceList = record.getCostPriceList();
      if (StrUtil.isNotBlank(costPriceList)) {
        final List<ItemCostDto> itemCostDtoList =
            JsonUtil.parseList(costPriceList, ItemCostDto.class);

        List<ItemSkuPrice> addItemSkuPriceList = new LinkedList<>();
        itemCostDtoList.forEach(
            val -> {
              final ItemSku itemSku = itemSkuGateway.getBySkuCode(val.getSkuCode());
              if (Objects.nonNull(itemSku)) {
                ItemSkuPrice itemSkuPrice = new ItemSkuPrice();
                itemSkuPrice.setSkuId(itemSkuPrice.getId());
                itemSkuPrice.setSkuCode(val.getSkuCode());
                itemSkuPrice.setPrice(val.getNewCostPrice());
                itemSkuPrice.setStartTime(record.getStartTime());
                itemSkuPrice.setType(0);
                addItemSkuPriceList.add(itemSkuPrice);

                itemSkuService
                    .lambdaUpdate()
                    .set(ItemSku::getCostPrice, val.getNewCostPrice())
                    .eq(ItemSku::getId, itemSkuPrice.getId())
                    .update();

                if (Objects.nonNull(val.getNewDailyPrice())) {
                  iNewGoodsService
                      .lambdaUpdate()
                      .set(NewGoods::getDailyPrice, val.getNewDailyPrice())
                      .eq(NewGoods::getSkuCode, val.getSkuCode())
                      .update();

                  itemSkuService
                      .lambdaUpdate()
                      .set(ItemSku::getSalePrice, val.getNewDailyPrice())
                      .eq(ItemSku::getId, itemSku.getId())
                      .update();
                }
              }
            });
        if (CollUtil.isNotEmpty(addItemSkuPriceList)) {
          iItemSkuPriceService.saveBatch(addItemSkuPriceList);
        }
      }
    }

    // 活动价格
    if (2 == record.getChangeType()) {
      final List<MultiSkuPriceFullDTO> list =
          JsonUtil.parseList(record.getActivityPriceList(), MultiSkuPriceFullDTO.class);
      List<ChangeActivityCostRes> resList = new LinkedList<>();
      for (MultiSkuPriceFullDTO dto : list) {
        try {
          resList.add(updatePurchase(dto));
        } catch (Exception e) {
          log.error("更改采购活动价异常.no:{},req:{}", record.getNo(), JsonUtil.toJson(dto), e);

          ChangeActivityCostRes res = new ChangeActivityCostRes();
          res.setSuccess(false);
          res.setErrorMsg("操作异常了，sku:" + dto.getSkuCode() + ",月份:" + dto.getMonth());
          resList.add(res);
        }
      }
      if (CollUtil.isNotEmpty(resList)) {
        operateLogGateway.addOperatorLog(
            UserContext.getUserId(),
            OperateLogTarget.PURCHASE_COST_CHANGE,
            record.getId(),
            "采购活动价更改。"
                + String.join(
                    "。",
                    resList.stream()
                        .map(ChangeActivityCostRes::getErrorMsg)
                        .collect(Collectors.joining("。"))),
            null);
      }
    }

    // 阶梯供价
    if (3 == record.getChangeType()) {

      List<MultiSkuPriceFullDTO> list =
          JsonUtil.parseList(record.getActivityPriceList(), MultiSkuPriceFullDTO.class).stream()
              .filter(val -> StrUtil.isNotBlank(val.getSkuCode()))
              .distinct()
              .peek(val -> val.setSkuCode(val.getSkuCode().trim()))
              .collect(toList());
      if (CollUtil.isEmpty(list)) {
        return;
      }
      list =
          list.stream()
              .filter(
                  val ->
                      Objects.nonNull(val.getActualProductQuantity())
                          && StrUtil.isNotBlank(val.getPriceDiscountSettlementCost())
                          && isPureNumber(val.getPriceDiscountSettlementCost()))
              .distinct()
              .collect(toList());
      int costType;
      // 2 4 都是活动价
      if (list.get(0).getPriceType() == 2 || list.get(0).getPriceType() == 4) {
        costType = 2;
      } else {
        costType = 1;
      }

      // SKU 价格，日常价格，活动价格
      if (list.get(0).getPriceType() == 1 || list.get(0).getPriceType() == 2) {

        List<MultiSkuPriceFullDTO> matchedCodes = new LinkedList<>();
        for (MultiSkuPriceFullDTO dto : list) {
          if (list.get(0).getPriceType() == 1) {
            boolean deleteSuccess =
                iPurchaseSingleSkuCombinationPriceService
                    .lambdaUpdate()
                    .eq(PurchaseSingleSkuCombinationPrice::getCode, dto.getSkuCode())
                    .eq(PurchaseSingleSkuCombinationPrice::getPriceType, costType)
                    .remove();
            if (deleteSuccess) {
              matchedCodes.add(dto);
            }
          }
          if (list.get(0).getPriceType() == 2) {
            boolean deleteSuccess =
                iPurchaseSingleSkuCombinationPriceService
                    .lambdaUpdate()
                    .eq(PurchaseSingleSkuCombinationPrice::getCode, dto.getSkuCode())
                    .eq(PurchaseSingleSkuCombinationPrice::getPriceType, costType)
                    .eq(PurchaseSingleSkuCombinationPrice::getStartTime, dto.getActivityStartDate())
                    .eq(PurchaseSingleSkuCombinationPrice::getEndTime, dto.getActivityEndDate())
                    .remove();
            if (deleteSuccess) {
              matchedCodes.add(dto);
            }
          }
        }

        if (CollUtil.isNotEmpty(matchedCodes)) {
          List<PurchaseSingleSkuCombinationPrice> saveList = new LinkedList<>();
          matchedCodes.stream()
              .collect(Collectors.groupingBy(MultiSkuPriceFullDTO::getSkuCode))
              .forEach(
                  (skuCode, rlist) -> {
                    final List<ExportSingleSkuRow> collect =
                        rlist.stream()
                            .map(
                                val -> {
                                  ExportSingleSkuRow row = new ExportSingleSkuRow();
                                  row.setSkuCode(val.getSkuCode());
                                  row.setActivityStartTime(val.getActivityStartDate());
                                  row.setActivityEndTime(val.getActivityEndDate());
                                  row.setQuantity(val.getActualProductQuantity());
                                  row.setPrice(val.getPriceDiscountSettlementCost());
                                  return row;
                                })
                            .collect(toList());
                    saveList.add(readExcelController.singleHandler0(collect, skuCode, costType));
                  });
          if (CollUtil.isNotEmpty(saveList)) {
            iPurchaseSingleSkuCombinationPriceService.saveBatch(saveList);
          }
        }
      }

      // SPU 价格，日常价格，活动价格
      if (list.get(0).getPriceType() == 3 || list.get(0).getPriceType() == 4) {

        List<MultiSkuPriceFullDTO> matchedCodes = new LinkedList<>();
        for (MultiSkuPriceFullDTO dto : list) {

          if (list.get(0).getPriceType() == 3) {
            boolean deleteSuccess =
                ipurchaseRandomSkuCombinationPriceService
                    .lambdaUpdate()
                    .like(PurchaseRandomSkuCombinationPrice::getCode, dto.getSkuCode())
                    .eq(PurchaseRandomSkuCombinationPrice::getPriceType, costType)
                    .remove();
            if (deleteSuccess) {
              matchedCodes.add(dto);
            }
          }
          if (list.get(0).getPriceType() == 4) {
            boolean deleteSuccess =
                ipurchaseRandomSkuCombinationPriceService
                    .lambdaUpdate()
                    .like(PurchaseRandomSkuCombinationPrice::getCode, dto.getSkuCode())
                    .eq(PurchaseRandomSkuCombinationPrice::getPriceType, costType)
                    .eq(PurchaseRandomSkuCombinationPrice::getStartTime, dto.getActivityStartDate())
                    .eq(PurchaseRandomSkuCombinationPrice::getEndTime, dto.getActivityEndDate())
                    .remove();
            if (deleteSuccess) {
              matchedCodes.add(dto);
            }
          }

          if (CollUtil.isNotEmpty(matchedCodes)) {
            List<PurchaseRandomSkuCombinationPrice> saveList = new LinkedList<>();
            int priceType = dto.getPriceType() == 3 ? 1 : 2;
            list.stream()
                .collect(Collectors.groupingBy(MultiSkuPriceFullDTO::getSpu))
                .forEach(
                    (spuCode, rList) -> {
                      final List<ExportSingleSkuRow> collect =
                          rList.stream()
                              .map(
                                  val -> {
                                    ExportSingleSkuRow row = new ExportSingleSkuRow();
                                    row.setSpuCode(val.getSpu());
                                    row.setSkuCode(val.getSkuCode());
                                    row.setActivityStartTime(val.getActivityStartDate());
                                    row.setActivityEndTime(val.getActivityEndDate());
                                    row.setQuantity(val.getActualProductQuantity());
                                    row.setPrice(val.getPriceDiscountSettlementCost());
                                    return row;
                                  })
                              .collect(toList());
                      saveList.add(
                          readExcelController.randomHandler0(
                              collect, RandomUtil.randomString(6), priceType));
                    });
            if (CollUtil.isNotEmpty(saveList)) {
              ipurchaseRandomSkuCombinationPriceService.saveBatch(saveList);
            }
          }
        }
      }
    }
  }

  @Data
  private static class ChangeActivityCostRes {
    private Boolean success;
    private String errorMsg;
  }

  private ChangeActivityCostRes updatePurchase(MultiSkuPriceFullDTO dto) {

    ChangeActivityCostRes res = new ChangeActivityCostRes();

    // 按价格优惠结算成本 + 按数量优惠结算成本
    if (!isPureNumber(dto.getPriceDiscountSettlementCost())
        || !isPureNumber(dto.getQuantityDiscountUnits())) {
      res.setSuccess(false);
      res.setErrorMsg(
          "按价格优惠结算成本 "
              + dto.getPriceDiscountSettlementCost()
              + " 或者 按数量优惠结算成本 "
              + dto.getQuantityDiscountUnits()
              + " 俩者 必须都是有效数字");
      return res;
    }
    if (!DateUtil.isValidYearMonth(dto.getMonth())) {
      res.setSuccess(false);
      res.setErrorMsg(dto.getMonth() + " 此月份格式非法，正确例子 2025/01");
      return res;
    }

    PurchaseIsActive isActive =
        dto.getIsPureActivityProduct().contains("是") ? PurchaseIsActive.YES : PurchaseIsActive.NO;
    FavourableType favourableType =
        dto.getDiscountType().contains("时间")
            ? FavourableType.ACCORDING_TIME
            : FavourableType.FAVOURABLE_TIME;
    PurchaseType purchaseType =
        StrUtil.isBlank(dto.getMethod())
            ? PurchaseType.NOT_HAVE
            : (dto.getMethod().contains("大促") ? PurchaseType.SALE : PurchaseType.LIVE);
    final boolean update =
        iPurchaseService
            .lambdaUpdate()
            .set(Purchase::getItemName, dto.getProductName())
            .set(Purchase::getIsActive, isActive)
            .set(Purchase::getFavourableType, favourableType)
            .set(
                Purchase::getPlatformType,
                IEnum.getEnumByDesc(PurchasePlatform.class, dto.getPlatformName().trim()))
            .set(Purchase::getActiveType, purchaseType)
            .set(Purchase::getStartTime, dto.getActivityStartDate())
            .set(Purchase::getEndTime, dto.getActivityEndDate())
            .set(Purchase::getOrderCount, dto.getOrderQuantity())
            .set(Purchase::getFinalCount, dto.getActualProductQuantity())
            .set(Purchase::getContent, dto.getSupplyPriceDiscountContent())
            .set(Purchase::getPriceCost, new BigDecimal(dto.getPriceDiscountSettlementCost()))
            .set(Purchase::getNumCost, new BigDecimal(dto.getQuantityDiscountUnits()))
            .eq(Purchase::getMonth, dto.getMonth())
            .eq(Purchase::getItemSku, dto.getSkuCode())
            .update();
    res.setSuccess(update);
    if (update) {
      res.setErrorMsg("SKU：" + dto.getSkuCode() + "月份：" + dto.getMonth() + "，活动成本更改成功");
      return res;
    } else {
      res.setErrorMsg("只更改不更新，确保此 SKU：" + dto.getSkuCode() + "在目标月份：" + dto.getMonth() + "存在采购成本");
      return res;
    }
  }

  public static boolean isPureNumber(String str) {
    if (str == null || str.isEmpty()) return false;
    for (char c : str.toCharArray()) {
      if (!Character.isDigit(c)) {
        return false;
      }
    }
    return true;
  }

  @Override
  public PurchaseCostChangeProcessForm buildWorkbenchForm(Long id) {

    PurchaseCostChangeRecord record = iPurchaseCostChangeRecordService.getById(id);

    CostPageVo costPageVo =
        page(new CostPageQuery() {
              private static final long serialVersionUID = -6367670624764902999L;

              {
                setId(id);
              }
            })
            .getData()
            .get(0);

    PurchaseCostChangeProcessForm form = new PurchaseCostChangeProcessForm();
    form.setLoginUserId(UserContext.getUserId());
    form.setId(id);
    form.setProvider(costPageVo.getProviderName());
    form.setCorpType(costPageVo.getCorpTypeStr());
    form.setType(
        costPageVo.getType() == 1 ? "日常成本" : (costPageVo.getType() == 2 ? "活动成本" : "阶梯供价"));
    form.setBuyer(costPageVo.getBuyerDtoList().get(0).getBuyerUserId());
    form.setChangeReason(record.getChangeReason());
    form.setStartTime(costPageVo.getStartTimeStr());

    Integer changeType = record.getChangeType();

    // 成本类型 1
    if (changeType == 1) {
      List<Title> titles =
          Arrays.asList(
              new Title("商品 SKU", "skuCode"),
              new Title("商品名称", "itemName"),
              new Title("规格名称", "spec"),
              new Title("当前成本价", "currentCost"),
              new Title("当前日销价", "currentDailyPrice"),
              new Title("新成本价", "newCost"),
              new Title("新日销价", "newDailyPrice"),
              new Title("备注", "remark"));

      List<Map<String, Object>> rows =
          JsonUtil.parseList(record.getCostPriceList(), ItemCostDto.class).stream()
              .map(
                  val ->
                      Dict.of(
                          "skuCode", val.getSkuCode(),
                          "itemName", val.getItemName(),
                          "spec", val.getSpec(),
                          "currentCost", val.getCostPrice(),
                          "currentDailyPrice", val.getDailyPrice(),
                          "newCost", val.getNewCostPrice(),
                          "newDailyPrice", val.getNewDailyPrice(),
                          "remark", val.getRemark()))
              .collect(Collectors.toList());

      form.setDetail(JsonUtil.toJson(Dict.of("title", titles, "row", rows)));
      return form;
    }

    // 成本类型 2 或 3
    List<MultiSkuPriceFullDTO> list;
    Title firstTitle;
    if (changeType == 2) {
      list = JsonUtil.parseList(record.getActivityPriceList(), MultiSkuPriceFullDTO.class);
      firstTitle = new Title("月份", "month");
    } else {
      list = JsonUtil.parseList(record.getMultiPriceList(), MultiSkuPriceFullDTO.class);
      firstTitle = new Title("SPU", "spu");
    }

    List<Title> titles =
        new ArrayList<>(
            Arrays.asList(
                firstTitle,
                new Title("商品SKU", "skuCode"),
                new Title("商品名称", "itemName"),
                new Title("是否纯活动商品", "isPureActivityProduct"),
                new Title("优惠类型", "discountType"),
                new Title("平台名称", "platformName"),
                new Title("方式", "method"),
                new Title("开始时间", "activityStartDate"),
                new Title("结束时间", "activityEndDate"),
                new Title("订单拍下份数", "orderQuantity"),
                new Title("实发单品数量", "actualProductQuantity"),
                new Title("按价格优惠结算成本", "priceDiscountSettlementCost"),
                new Title("按数量优惠结算成本", "quantityDiscountUnits"),
                new Title("供价优惠内容", "supplyPriceDiscountContent")));

    List<Map<String, Object>> rows =
        list.stream()
            .map(
                val -> {
                  Map<String, Object> map = new HashMap<>();
                  if (changeType == 2) map.put("month", val.getMonth());
                  if (changeType == 3) map.put("spu", val.getSpu());
                  map.put("skuCode", val.getSkuCode());
                  map.put("itemName", val.getProductName());
                  map.put("isPureActivityProduct", val.getIsPureActivityProduct());
                  map.put("discountType", val.getDiscountType());
                  map.put("platformName", val.getPlatformName());
                  map.put("method", val.getMethod());
                  map.put("activityStartDate", val.getActivityStartDate());
                  map.put("activityEndDate", val.getActivityEndDate());
                  map.put("orderQuantity", val.getOrderQuantity());
                  map.put("actualProductQuantity", val.getActualProductQuantity());
                  map.put("priceDiscountSettlementCost", val.getPriceDiscountSettlementCost());
                  map.put("quantityDiscountUnits", val.getQuantityDiscountUnits());
                  map.put("supplyPriceDiscountContent", val.getSupplyPriceDiscountContent());
                  return map;
                })
            .collect(Collectors.toList());

    form.setDetail(JsonUtil.toJson(Dict.of("title", titles, "row", rows)));
    return form;
  }

  @Override
  @DistributedLock
  public SingleResponse<Boolean> cancel(Long id) {

    final PurchaseCostChangeRecord record = iPurchaseCostChangeRecordService.getById(id);
    Assert.notNull(record, "ID 非法");
    Assert.state(record.getStatus().equals(CostAuditStatus.WAIT_AUDIT.getValue()), "只有待审核状态才能撤回审核");
    Assert.hasText(record.getWorkbenchProcessId(), "审核流程 ID 不得为空");
    //    boolean bb =
    //        ListUtil.of(record.getBuyerId().split(",")).stream()
    //            .map(Long::valueOf)
    //            .collect(toList())
    //            .contains(UserContext.getUserId());
    //    Assert.state(bb, "只有当前单据的采购员才允许撤回审核");

    WorkbenchCommonDto req =
        new WorkbenchCommonDto(record.getWorkbenchProcessId(), UserContext.getLoginName());
    log.info("purchaseCostChange cancel req:{}", JsonUtil.toJson(req));

    DaddylabWorkbenchResponse cancel;
    try {
      cancel = workbenchFeignClient.cancel(req);
      log.info("采购成本变更单 取消审核响应:{}", JsonUtil.toJson(cancel));

      operateLogGateway.addOperatorLog(
          UserContext.getUserId(),
          OperateLogTarget.PURCHASE_COST_CHANGE,
          record.getId(),
          "采购员主动取消审核，审核撤回。",
          null);

      if (!cancel.isSuccess()) {
        throw ExceptionPlusFactory.bizException(
            ErrorCode.API_RESP_ERROR, "取消审核失败: " + cancel.getMsg());
      }
      if (cancel.isSuccess()) {
        record.setStatus(CostAuditStatus.CANCEL.getValue());
        iPurchaseCostChangeRecordService.updateById(record);
      }
    } catch (Exception e) {
      log.error("调用工作台取消审核流程接口异常", e);
      throw ExceptionPlusFactory.bizException(
          ErrorCode.API_RESP_ERROR, "取消审核失败: " + e.getMessage());
    }

    operateLogGateway.addOperatorLog(
        UserContext.getUserId(),
        OperateLogTarget.PURCHASE_COST_CHANGE,
        id,
        "取消审核，响应结果：" + cancel.getData(),
        null);
    return SingleResponse.of(true);
  }

  @Override
  public SingleResponse<Boolean> updateStatus(Long id, Integer status) {
    final PurchaseCostChangeRecord record = iPurchaseCostChangeRecordService.getById(id);
    Assert.notNull(record, "ID非法");
    if (status.equals(CostAuditStatus.CLOSE.getValue())) {
      Assert.state(
          ListUtil.of(CostAuditStatus.REJECT.getValue(), CostAuditStatus.CANCEL.getValue())
              .contains(record.getStatus()),
          "只有审核拒绝和审核撤回状态才允许关闭审核");
      Assert.state(
          ListUtil.of(record.getBuyerId().split(",")).stream()
              .map(Long::valueOf)
              .collect(Collectors.toSet())
              .contains(UserContext.getUserId()),
          "只有此单据发起者（采购员）才允许关闭审核");
    }
    iPurchaseCostChangeRecordService
        .lambdaUpdate()
        .set(PurchaseCostChangeRecord::getStatus, status)
        .eq(PurchaseCostChangeRecord::getId, id)
        .update();

    return SingleResponse.of(true);
  }
}
