package com.daddylab.supplier.item.application.item;

import static java.util.stream.Collectors.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.Response;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.daddylab.ark.sailor.item.common.base.vo.Result;
import com.daddylab.ark.sailor.item.domain.vo.category.CategoryListVO;
import com.daddylab.ark.sailor.item.domain.vo.item.ItemListVO;
import com.daddylab.job.core.handler.annotation.XxlJob;
import com.daddylab.job.extend.autoRegister.XxlJobAutoRegister;
import com.daddylab.supplier.item.application.bizLevelDivision.BizLevelDivisionConvert;
import com.daddylab.supplier.item.application.drawer.ItemDrawerMergeBizService;
import com.daddylab.supplier.item.application.drawer.ItemDrawerService;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationBizServiceImpl;
import com.daddylab.supplier.item.application.item.combinationItem.dto.query.ComposeSkuPageQuery;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuVO;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.ComposeSkuWithPriceVO;
import com.daddylab.supplier.item.application.item.event.ItemLaunchStatusChangeEvent;
import com.daddylab.supplier.item.application.item.models.ImportItemVO;
import com.daddylab.supplier.item.application.itemReference.ItemReferenceBizService;
import com.daddylab.supplier.item.application.itemTag.ItemTagBizService;
import com.daddylab.supplier.item.application.psys.PsysCategoryConfig;
import com.daddylab.supplier.item.common.ExceptionPlusFactory;
import com.daddylab.supplier.item.common.ResponseAssert;
import com.daddylab.supplier.item.common.enums.ErrorCode;
import com.daddylab.supplier.item.common.enums.PoolEnum;
import com.daddylab.supplier.item.common.trans.ItemTransMapper;
import com.daddylab.supplier.item.common.trans.SkuTransMapper;
import com.daddylab.supplier.item.common.util.ThreadUtil;
import com.daddylab.supplier.item.controller.item.ArkSailorItemBizService;
import com.daddylab.supplier.item.controller.item.ShortSkuVO;
import com.daddylab.supplier.item.controller.item.dto.*;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailPriceVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailRunningVo;
import com.daddylab.supplier.item.controller.item.dto.detail.ItemDetailVo;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemConfirmCmd;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemConfirmVo;
import com.daddylab.supplier.item.controller.item.dto.partner.PartnerItemVo;
import com.daddylab.supplier.item.controller.provider.dto.ProviderShopDetail;
import com.daddylab.supplier.item.domain.brand.entity.BrandEntity;
import com.daddylab.supplier.item.domain.brand.gateway.BrandGateway;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.domain.common.enums.Platform;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageFileTypeEnum;
import com.daddylab.supplier.item.domain.drawer.enums.ItemDrawerImageTypeEnum;
import com.daddylab.supplier.item.domain.item.event.ItemQcChangeEvent;
import com.daddylab.supplier.item.domain.item.gateway.BuyerGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemImageGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemProcurementGateway;
import com.daddylab.supplier.item.domain.item.gateway.ItemSkuGateway;
import com.daddylab.supplier.item.domain.item.service.ItemDomainService;
import com.daddylab.supplier.item.domain.itemStock.gateway.WarehouseStockGateway;
import com.daddylab.supplier.item.domain.operateLog.enums.OperateLogTarget;
import com.daddylab.supplier.item.domain.operateLog.service.OperateLogDomainService;
import com.daddylab.supplier.item.domain.platformItem.service.event.PlatformItemUpdateEvent;
import com.daddylab.supplier.item.domain.provider.gateway.ProviderGateway;
import com.daddylab.supplier.item.domain.user.gateway.UserGateway;
import com.daddylab.supplier.item.infrastructure.bizLevelDivision.BizDivisionContext;
import com.daddylab.supplier.item.infrastructure.common.UserContext;
import com.daddylab.supplier.item.infrastructure.config.RefreshConfig;
import com.daddylab.supplier.item.infrastructure.config.event.EventBusUtil;
import com.daddylab.supplier.item.infrastructure.domain.Entity;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CascadedDivisionLevel;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ComposeSkuDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.SkuAttrRefDO;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerPlatformLink;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.enums.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemSkuMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemMapper;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.ArkSailorItemFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.PartnerFeignClient;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ItemSyncMiniProgramRequest.ErpItemAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ItemSyncMiniProgramRequest.ErpItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.ItemSyncMiniProgramRequest.ErpItemSkuAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.itemSyncMiniProgram.ApplicablePopulation;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.itemSyncMiniProgram.ErpItemExtend;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.feign.dto.itemSyncMiniProgram.FilingEfficacy;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.dto.PartnerItemResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums.PSysBusinessTypeEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.item.enums.PSysCooperatorEnum;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.mall.MallShopGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.provider.dto.PartnerProviderResp;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.user.StaffInfo;
import com.daddylab.supplier.item.infrastructure.goclient.Rsp;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.lock.DistributedLock;
import com.daddylab.supplier.item.infrastructure.timing.StopWatch;
import com.daddylab.supplier.item.infrastructure.utils.*;
import com.daddylab.supplier.item.infrastructure.utils.DiffUtil.ChangePropertyObj;
import com.daddylab.supplier.item.types.item.MiniProgramCategoryListQuery;
import com.daddylab.supplier.item.types.partner.CooperateModeNotify;
import com.daddylab.supplier.item.types.partner.QcChangeNotify;
import com.diffplug.common.base.Errors;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.annotation.Resource;
import lombok.Data;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/9 2:40 下午
 * @description
 */
@Service
@Slf4j
public class ItemBizServiceImpl implements ItemBizService {

  /** 「待修改」「待上架」「已上架」才可以同步 */
  private final List<Integer> canSyncItemStatusList =
      Arrays.asList(
          ItemLaunchStatus.TO_BE_UPDATED.getValue(),
          ItemLaunchStatus.TO_BE_RELEASED.getValue(),
          ItemLaunchStatus.HAS_BE_RELEASED.getValue());

  @Autowired IItemService itemService;

  @Autowired IItemExpressTemplateService iItemExpressTemplateService;

  @Autowired IItemProcurementService iItemProcurementService;

  @Autowired OperateLogDomainService operateLogDomainService;

  @Autowired ItemDomainService itemDomainService;

  @Autowired ProviderGateway providerGateway;

  @Autowired ItemGateway itemGateway;

  @Autowired BrandGateway brandGateway;

  @Autowired WarehouseStockGateway warehouseStockGateway;

  @Autowired KingDeeTemplate kingDeeTemplate;

  @Autowired ICombinationItemService iCombinationItemService;

  @Autowired CombinationBizServiceImpl combinationBizService;

  @Autowired ItemImageGateway itemImageGateway;

  @Autowired
  @Qualifier("UserGatewayCacheImpl")
  UserGateway userGateway;

  @Autowired IItemSkuService iItemSkuService;

  @Resource IItemSkuPriceService iItemSkuPriceService;

  @Resource IItemService iItemService;

  @Resource IItemPriceService iItemPriceService;

  @Resource ItemSkuMapper itemSkuMapper;

  @Resource IProviderService iProviderService;
  @Autowired IThirdPlatformSyncService thirdPlatformSyncService;
  @Resource IThirdPlatformSyncLogService iThirdPlatformSyncLogService;

  @Autowired private ArkSailorItemFeignClient arkSailorItemFeignClient;
  @Autowired private ArkSailorItemBizService arkSailorItemBizService;
  @Autowired private ItemDrawerService itemDrawerService;
  @Autowired private IItemDrawerPlatformLinkService itemDrawerPlatformItemService;
  @Autowired private IItemDrawerImageService itemDrawerImageService;
  @Autowired private INewGoodsService newGoodsService;
  @Autowired private ICategoryService categoryService;
  @Autowired private CategoryGateway categoryGateway;

  @Autowired ISkuService iSkuService;
  @Autowired private ItemDrawerMergeBizService itemDrawerMergeBizService;
  @Autowired private ItemTagBizService itemTagBizService;
  @Autowired private IBizLevelDivisionService bizLevelDivisionService;

  @Autowired BuyerGateway buyerGateway;

  @Autowired private PartnerFeignClient partnerFeignClient;
  @Autowired private ItemProcurementGateway itemProcurementGateway;
  @Autowired private IItemProcurementService itemProcurementService;

  /**
   * 商品名字下拉选
   *
   * @param name 商品名字模糊查询
   * @return com.daddylab.supplier.item.controller.item.dto.NameDropDownVo
   */
  @Override
  public MultiResponse<NameDropDownVo> nameDropDownList(
      String name,
      Integer pageIndex,
      Integer pageSie,
      Boolean useForItemLaunchPlan,
      Long providerId) {
    pageIndex = pageIndex == 0 ? 1 : pageIndex;
    final List<NameDropDownVo> nameDropDownVos =
        itemService.dropDownList(
            name, (pageIndex - 1) * pageSie, pageSie, useForItemLaunchPlan, providerId);
    return MultiResponse.of(nameDropDownVos);
  }

  @Autowired private IItemLaunchPlanService itemLaunchPlanService;

  /**
   * 商品分页查询
   *
   * @param pageQuery 分页查询封装
   * @return 商品显示。
   */
  @Override
  public PageResponse<ItemPageVo> queryPage(ItemPageQuery pageQuery) {
    Page<ItemPageVo> page = new Page<>(pageQuery.getPageIndex(), pageQuery.getPageSize());
    // 设置子查询分页偏移量
    int index = 0 == pageQuery.getPageIndex() ? 1 : pageQuery.getPageIndex();
    long offSet = (long) (index - 1) * pageQuery.getPageSize();
    pageQuery.setOffsetVal(offSet);
    page.setTotal(
        BizDivisionContext.invoke(
            BizUnionTypeEnum.SPU, () -> itemService.queryPageCount(pageQuery)));
    final List<ItemPageVo> itemPageVos =
        BizDivisionContext.invoke(BizUnionTypeEnum.SPU, () -> itemService.queryPage(pageQuery));

    List<Long> itemIds = itemPageVos.stream().map(ItemPageVo::getId).distinct().collect(toList());
    List<String> itemCodes =
        itemPageVos.stream().map(ItemPageVo::getCode).distinct().collect(toList());
    List<Long> providerIds =
        itemPageVos.stream().map(ItemPageVo::getProviderId).distinct().collect(toList());
    final Map<String, BigDecimal> itemStockSum =
        Boolean.TRUE.equals(pageQuery.getNotQueryStock())
            ? Collections.emptyMap()
            : warehouseStockGateway.getItemStockSum(itemCodes);
    Map<Long, ItemBuyerDto> itemsBuyerMap = itemService.getItemsBuyerMap(itemIds);
    final Map<Long, Long> estimateSaleTimeBatch =
        itemLaunchPlanService.getEstimateSaleTimeBatch(itemIds);

    Map<Long, PartnerItemResp> partnerItemRespMap = queryPartnerItemBatch(itemIds);

    final Map<Long, List<String>> tagsMap = itemTagBizService.listByItemIds(itemIds);

    final Map<Long, String> providerMap =
        providerGateway.queryBatchByIds(providerIds).stream()
            .collect(toMap(Provider::getId, Provider::getName));

    final HashMap<String, ItemListVO> mallItemMap = new HashMap<>();
    if (Boolean.TRUE.equals(pageQuery.getQueryMallStatus())) {
      final MultiResponse<ItemListVO> mallItemResponse =
          arkSailorItemBizService.listItemByItemNos(itemCodes);
      ResponseAssert.assertJust(mallItemResponse);
      mallItemResponse
          .getData()
          .forEach(mallItem -> mallItemMap.put(mallItem.getItemNo(), mallItem));
    }
    final IBizLevelDivisionService bizLevelDivisionService =
        SpringUtil.getBean(IBizLevelDivisionService.class);
    itemPageVos.parallelStream()
        .forEach(
            vo -> {
              MultiResponse<ItemPriceVO> costPriceList = itemCostPriceList(vo.getId(), "采购成本");
              vo.setPurchasePrice(
                  Optional.ofNullable(costPriceList)
                      .map(MultiResponse::getData)
                      .orElse(Collections.emptyList())
                      .stream()
                      .findFirst()
                      .map(ItemPriceVO::getPrice)
                      .map(BigDecimal::toString)
                      .orElse(""));

              // 商品上新时间现在改为从上新记录获取
              final Long shelfTime = estimateSaleTimeBatch.get(vo.getId());
              if (shelfTime != null) {
                vo.setShelfTime(shelfTime);
              }
              vo.setStock(
                  Optional.ofNullable(itemStockSum.get(vo.getCode()))
                      .map(BigDecimal::intValue)
                      .orElse(-1));
              vo.setSkuCount(itemGateway.getSkuCount(vo.getId()));
              vo.setImageUrl(itemImageGateway.getItemMainImgUrl(vo.getId()));

              ItemBuyerDto itemBuyerDto = itemsBuyerMap.get(vo.getId());
              vo.setItemBuyerUserId(
                  Optional.ofNullable(itemBuyerDto).map(ItemBuyerDto::getBuyerUserId).orElse(0L));
              StaffInfo staffInfo1 = userGateway.queryStaffInfoById(vo.getItemBuyerUserId());
              vo.setItemBuyerUserName(Objects.nonNull(staffInfo1) ? staffInfo1.getUserName() : "");
              vo.setItemBuyerNickName(Objects.nonNull(staffInfo1) ? staffInfo1.getNickname() : "");

              String qcIds =
                  Optional.ofNullable(itemBuyerDto).map(ItemBuyerDto::getQcUserIds).orElse("");
              if (StringUtils.hasText(qcIds)) {
                List<String> qcIdList = Arrays.asList(qcIds.split(","));
                List<Long> qcUserIds = qcIdList.stream().map(Long::valueOf).collect(toList());
                Map<Long, StaffInfo> staffInfoMap = userGateway.batchQueryStaffInfoByIds(qcUserIds);
                List<ItemPageVo.QcUserInfo> qcUserInfos = new ArrayList<>();
                for (Long qcUserId : qcUserIds) {
                  ItemPageVo.QcUserInfo qcUserInfo = new ItemPageVo.QcUserInfo();
                  StaffInfo staffInfo = staffInfoMap.get(qcUserId);
                  qcUserInfo.setQcUserId(
                      Optional.ofNullable(staffInfo).map(StaffInfo::getUserId).orElse(0L));
                  qcUserInfo.setQcUserName(
                      Optional.ofNullable(staffInfo).map(StaffInfo::getUserName).orElse(""));
                  qcUserInfo.setQcUserNickName(
                      Optional.ofNullable(staffInfo).map(StaffInfo::getNickname).orElse(""));
                  qcUserInfos.add(qcUserInfo);
                }
                vo.setQcUserInfoList(qcUserInfos);
              }

              // 是否老爸抽检
              vo.setIsDadCheck(
                  Optional.ofNullable(partnerItemRespMap.getOrDefault(vo.getId(), null))
                      .map(PartnerItemResp::isDadCheck)
                      .orElse(0));
              // 商品标签
              vo.setTags(tagsMap.getOrDefault(vo.getId(), Collections.emptyList()));
              vo.setProviderName(providerMap.getOrDefault(vo.getProviderId(), ""));
              //
              final ItemListVO itemListVO = mallItemMap.get(vo.getCode());
              if (itemListVO != null) {
                vo.setMallShelfStatus(itemListVO.getShelfStatus());
              }
              final List<CorpBizTypeDTO> corpBizType =
                  bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, vo.getId());
              vo.setCorpBizType(corpBizType);
              vo.setDownFrameTime(vo.getDownFrameTime());
            });
    page.setRecords(itemPageVos);
    return PageResponse.of(
        page.getRecords(),
        (int) page.getTotal(),
        pageQuery.getPageSize(),
        pageQuery.getPageIndex());
  }

  @Override
  public MultiResponse<ItemSimpleViewVo> querySimple(ItemSimpleViewCmd cmd) {
    if (1 != cmd.getType() && 2 != cmd.getType() && 3 != cmd.getType()) {
      throw ExceptionPlusFactory.bizException(ErrorCode.ITEM_BIZ_ERROR, "商品信息简单搜搜，搜索类型非法");
    }
    return MultiResponse.of(itemService.queryItemSimple(cmd));
  }

  @Override
  public SingleResponse<ItemDetailVo> queryDetail(Long itemId) {
    final ItemDetailVo itemDetail = itemDomainService.getItemDetail(itemId);

    return SingleResponse.of(itemDetail);
  }

  @Override
  public SingleResponse<ItemDetailRunningVo> queryRunningDetail(Long itemId) {
    final ItemDetailRunningVo itemRunningDetail = itemDomainService.getItemRunningDetail(itemId);
    return SingleResponse.of(itemRunningDetail);
  }

  @Override
  public SingleResponse<ItemDetailPriceVo> queryPrice(Long itemId) {
    return SingleResponse.of(itemDomainService.getItemPrice(itemId));
  }

  @Override
  public Response removePrice(Long priceId) {
    iItemProcurementService.removeById(priceId);
    return Response.buildSuccess();
  }

  @Override
  public MultiResponse<ItemExpressVo> getItemExpress(Long itemId) {
    QueryWrapper<ItemExpressTemplate> wrapper = new QueryWrapper<>();
    wrapper.lambda().eq(ItemExpressTemplate::getItemId, itemId);
    final List<ItemExpressTemplate> list = iItemExpressTemplateService.list(wrapper);
    return MultiResponse.of(ItemTransMapper.INSTANCE.expressDdToDtos(list));
  }

  @Override
  public PageResponse<ItemSkuSpecVo> getSpecList(ItemSpecPageQuery query) {
    return itemGateway.getListBySpec(query);
  }

  @Autowired ItemSyncWdtBizService itemSyncWdtBizService;

  @Autowired ItemSyncBanniuBizService itemSyncBanniuBizService;

  @Autowired RefreshConfig refreshConfig;

  @Resource ItemSkuGateway itemSkuGateway;

  @Autowired ItemReferenceBizService itemReferenceBizService;

  @Override
  public SingleResponse<Long> saveItem(SaveItemCmd cmd) throws Exception {
    // 保存商品信息
    Item item = itemDomainService.saveItem(cmd);

    // 将商品的变更同步到SPU和SKU表
    itemReferenceBizService.syncItemSku(item.getCode(), UserContext.getUserId());

    String activeProfile = SpringUtil.getActiveProfile();
    if ("gray".equals(activeProfile) || "prod".equals(activeProfile)) {
      // 同步物料
      kingDeeTemplate.syncItem(item.getId());
    }

    // 测试同步旺店通（生产环境由于金蝶开启了奇门同步旺店通，所以如果两边同时推送有可能会导致金蝶异常，所以暂时只在测试环境启用直推旺店通）
    // 生产环境添加开关（准备切换为直推旺店通）
    if (refreshConfig.getItemPushToWdt()
        || ApplicationContextUtil.isActiveProfile("local", "dev", "test")) {
      itemSyncWdtBizService.syncItemToWdtManaged(item.getId());
    }

    // 同步班牛
    try {
      itemSyncBanniuBizService.syncOneByItemId(item.getId());
    } catch (Exception e) {
      log.error("【班牛发货仓同步】同步异常 itemId={}", item.getId(), e);
    }

    return SingleResponse.of(item.getId());
  }

  /** 同步后端商品到拌牛 */
  private void syncBanniu(Item item) {}

  @Override
  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  @DistributedLock
  public Response updateRunning(EditRunningCmd cmd) {
    itemDomainService.editItemRunning(cmd);
    return Response.buildSuccess();
  }

  @Override
  public Response exportItem(ExportCmd cmd) {
    itemDomainService.exportItemInfo(cmd);
    return Response.buildSuccess();
  }

  @Override
  public MultiResponse<PartnerItemVo> queryPartnerItem(PartnerItemCmd cmd) {
    return MultiResponse.of(itemDomainService.queryPartnerItem(cmd));
  }

  /**
   * 同步逻辑 如果从P系统带过来的商品信息中，不包含供应商和品牌。弹出提示，新建供应商或者品牌，新建供应商和品牌再进行在商品中心中系统的关联。
   * 如果从P系统带过来的信息中，包含品牌和供应商，品牌根据名称，供应商根据名称+信用编码，如果两者在这边商品中心系统里面查不到，弹出手动新建品牌或者供应商的提示
   * 如果从P系统带过来的信息中，包含品牌和供应商，品牌根据名称，供应商根据名称+信用编码，如果在商品中心系统中能查到。OK。
   *
   * @param cmd
   * @return
   */
  @Override
  @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
  public SingleResponse<PartnerItemConfirmVo> confirmPartnerItem(PartnerItemConfirmCmd cmd) {
    final PartnerItemConfirmVo partnerItemConfirmVo =
        ItemTransMapper.INSTANCE.partnerItemConfirmCmdToVo(cmd);

    String organizationNo =
        StringUtil.isBlank(cmd.getOrganizationNo()) ? "" : cmd.getOrganizationNo();
    String partnerProviderName =
        StringUtil.isBlank(cmd.getPartnerProviderName()) ? "" : cmd.getPartnerProviderName();
    Provider hadProvider = providerGateway.getOne(partnerProviderName, organizationNo);
    if (Objects.isNull(hadProvider)) {
      partnerItemConfirmVo.setProviderId(0L);
    } else {
      partnerItemConfirmVo.setProviderId(hadProvider.getId());
    }

    String brandName = StringUtil.isBlank(cmd.getBrand()) ? "" : cmd.getBrand();
    final Long hadBrandId = brandGateway.getBrandId(brandName);
    if (0 == hadBrandId) {
      partnerItemConfirmVo.setBrandId(0L);
    } else {
      partnerItemConfirmVo.setBrandId(hadBrandId);
    }

    if (NumberUtil.isPositive(partnerItemConfirmVo.getBuyerUserId())) {
      final StaffInfo staffInfo =
          userGateway.queryStaffInfoById(((long) partnerItemConfirmVo.getBuyerUserId()));
      if (staffInfo != null) {
        partnerItemConfirmVo.setBuyerNickName(staffInfo.getNickname());
      }
    }

    final List<Integer> cooperator = cmd.getCooperator();
    final List<Integer> businessType = cmd.getBusinessType();
    final List<CorpBizTypeDTO> corpBizTypeDTOS =
        BizLevelDivisionConvert.INSTANCE.pSysCooperatorToCorpBizType(cooperator, businessType);
    partnerItemConfirmVo.setCorpBizType(corpBizTypeDTOS);

    return SingleResponse.of(partnerItemConfirmVo);
  }

  @Override
  public boolean existBrandAssociativeItem(Long brandId) {
    return itemGateway.existBrandAssociativeItem(brandId);
  }

  @Override
  public PageResponse<ComposeSkuVO> pageSku(ComposeSkuPageQuery query) {
    log.info("combinationItemBizService pageSku param:{}", query);
    final List<ComposeSkuDO> skuCombinationDos = iCombinationItemService.listSkuDetail(query);

    final List<Long> skuIdList =
        skuCombinationDos.stream().map(ComposeSkuDO::getSkuId).collect(toList());
    Map<Long, List<SkuAttrRefDO>> attrMap = combinationBizService.getSkuAttrRefMap(skuIdList);

    final Map<Long, ItemSku> skuMap =
        iItemSkuService.lambdaQuery().in(ItemSku::getId, skuIdList).list().stream()
            .collect(toMap(ItemSku::getId, Function.identity()));

    final List<String> skuCodes =
        skuCombinationDos.stream().map(ComposeSkuDO::getSkuCode).collect(toList());
    final Map<String, BigDecimal> stockSum = warehouseStockGateway.getStockSum(skuCodes);

    final Set<Long> itemIdSet =
        skuCombinationDos.stream().map(ComposeSkuDO::getItemId).collect(toSet());
    final Map<Long, List<CorpBizTypeDTO>> divisionMap =
        bizLevelDivisionService.queryByItemId(itemIdSet);

    final Map<String, ItemSkuPrice> itemSkuPriceMap =
        CollUtil.isNotEmpty(skuCodes)
            ? iItemSkuPriceService
                .lambdaQuery()
                .in(ItemSkuPrice::getSkuCode, skuCodes)
                .eq(ItemSkuPrice::getType, 0)
                .orderByDesc(ItemSkuPrice::getStartTime)
                .list()
                .stream()
                .collect(
                    toMap(
                        ItemSkuPrice::getSkuCode,
                        Function.identity(),
                        (existing, replacement) -> existing))
            : new HashMap<>();

    //    final Map<String, BigDecimal> dailyPriceMap =
    //        CollUtil.isNotEmpty(skuCodes)
    //            ? newGoodsService.lambdaQuery().in(NewGoods::getSkuCode, skuCodes).list().stream()
    //                .collect(toMap(NewGoods::getSkuCode, NewGoods::getDailyPrice))
    //            : new HashMap<>();

    List<ComposeSkuVO> voList = new LinkedList<>();
    skuCombinationDos.forEach(
        skuCombinationDO -> {
          final Long skuId = skuCombinationDO.getSkuId();
          final Long itemId = skuCombinationDO.getItemId();
          final List<SkuAttrRefDO> orDefault = attrMap.getOrDefault(skuId, new LinkedList<>());
          final List<ItemAttrDto> itemAttrDtoList = SkuTransMapper.INSTANCE.toAttrDtos(orDefault);

          final Long itemTotalStock =
              Optional.ofNullable(stockSum.get(skuCombinationDO.getSkuCode()))
                  .map(BigDecimal::longValue)
                  .orElse(0L);

          if (query.getWithPrice()) {
            final ComposeSkuWithPriceVO skuListVO =
                SkuTransMapper.INSTANCE.toVoWithPrice(skuCombinationDO);
            skuListVO.setSpecifications(itemAttrDtoList);
            skuListVO.setStockCount(itemTotalStock);
            skuListVO.setCorpBizType(divisionMap.get(itemId));
            skuListVO.setProcurement(
                itemSkuPriceMap
                    .getOrDefault(skuCombinationDO.getSkuCode(), new ItemSkuPrice())
                    .getPrice());
            boolean isSku =
                voList.stream()
                    .anyMatch(
                        composeSkuVO -> composeSkuVO.getItemCode().equals(skuListVO.getItemCode()));
            if (!isSku) {
              voList.add(skuListVO);
            }
            skuListVO.setDailyPrice(skuMap.get(skuId).getSalePrice());
          } else {
            final ComposeSkuVO skuListBaseVO =
                SkuTransMapper.INSTANCE.toVoNoPrice(skuCombinationDO);
            boolean isSku =
                voList.stream()
                    .anyMatch(
                        composeSkuVO ->
                            composeSkuVO.getItemCode().equals(skuListBaseVO.getItemCode()));
            skuListBaseVO.setSpecifications(itemAttrDtoList);
            skuListBaseVO.setStockCount(itemTotalStock);
            skuListBaseVO.setCorpBizType(divisionMap.get(itemId));
            if (!isSku) {
              voList.add(skuListBaseVO);
            }
            skuListBaseVO.setDailyPrice(skuMap.get(skuId).getSalePrice());
          }
        });

    final Integer count = iCombinationItemService.countSkuDetail(query);
    return PageResponse.of(voList, count, query.getPageSize(), query.getPageIndex());
  }

  @Override
  @Transactional(rollbackFor = Throwable.class)
  public Boolean updateLaunchStatus(Long itemId, ItemLaunchStatus itemLaunchStatus) {
    final ItemLaunchStatus currentLaunchStatus = itemService.getItemLaunchStatus(itemId);
    log.info(
        "更改上新状态，itemId:{},currentState:{},toBeState:{}",
        itemId,
        currentLaunchStatus,
        itemLaunchStatus);
    if (itemLaunchStatus == currentLaunchStatus) {
      return false;
    }
    final boolean update = itemService.updateLaunchStatusById(itemId, itemLaunchStatus);
    if (update) {
      EventBusUtil.post(
          ItemLaunchStatusChangeEvent.of(
              UserContext.getUserId(), itemId, currentLaunchStatus, itemLaunchStatus));
    }
    return update;
  }

  @Override
  public Boolean updateAuditStatus(Long itemId, ItemAuditStatus itemAuditStatus) {
    return itemService
        .lambdaUpdate()
        .set(Item::getAuditStatus, itemAuditStatus.getValue())
        .lt(Item::getAuditStatus, itemAuditStatus.getValue())
        .eq(Item::getId, itemId)
        .update();
  }

  @Override
  public boolean updateStatusById(Long itemId, ItemStatus itemStatus, ItemStatus expectStatus) {
    final LambdaUpdateChainWrapper<Item> updateChainWrapper =
        itemService
            .lambdaUpdate()
            .eq(Item::getId, itemId)
            .eq(
                expectStatus != null,
                Item::getStatus,
                expectStatus != null ? expectStatus.getValue() : null)
            .set(Item::getStatus, itemStatus.getValue());
    final Long currentTime = DateUtil.currentTime();
    if (itemStatus == ItemStatus.ON) {
      updateChainWrapper.set(Item::getUpFrameTime, currentTime);
      // 若首次上架时间为0，则设置为当前时间
      updateChainWrapper.setSql(
          String.format(
              "`%1$s` = IF(`%1$s` != 0, `%1$s`, %2$s)", "first_up_frame_time", currentTime));
    } else if (itemStatus == ItemStatus.OUT) {
      updateChainWrapper.set(Item::getDownFrameTime, currentTime);
    }
    return updateChainWrapper.update();
  }

  @Override
  public boolean updateStatusById(Long itemId, ItemStatus itemStatus) {
    return updateStatusById(itemId, itemStatus, null);
  }

  @Override
  public Boolean updateLaunchTime(List<Long> itemIds, Long launchTime) {
    if (CollectionUtil.isEmpty(itemIds)) {
      return false;
    }
    return itemService
        .lambdaUpdate()
        .set(Item::getEstimateSaleTime, launchTime)
        .in(Item::getId, itemIds)
        .update();
  }

  @Override
  public Boolean resetLaunchState(List<Long> itemIds) {
    if (CollectionUtil.isEmpty(itemIds)) {
      return false;
    }
    return itemService
        .lambdaUpdate()
        .set(Item::getLaunchStatus, ItemLaunchStatus.TO_BE_SELECTED.getValue())
        .set(Item::getEstimateSaleTime, 0L)
        .in(Item::getId, itemIds)
        .update();
  }

  @Override
  public void qcChange(QcChangeNotify notify) {
    if (notify == null) {
      return;
    }

    if (CollectionUtil.isNotEmpty(notify.getItems())) {
      final List<String> partnerProviderItemSns =
          notify.getItems().stream().map(QcChangeNotify.Item::getItemNo).collect(toList());
      final Map<String, List<Long>> partnerItemSnToItemIdMap =
          itemService
              .lambdaQuery()
              .in(Item::getPartnerProviderItemSn, partnerProviderItemSns)
              .select(Item::getPartnerProviderItemSn, Item::getId)
              .list()
              .stream()
              .collect(
                  groupingBy(
                      Item::getPartnerProviderItemSn, Collectors.mapping(Item::getId, toList())));

      for (QcChangeNotify.Item item : notify.getItems()) {
        final List<Long> itemIds = partnerItemSnToItemIdMap.get(item.getItemNo());
        if (CollectionUtil.isEmpty(itemIds)) {
          continue;
        }
        for (Long itemId : itemIds) {
          String qcIds = "";
          if (CollectionUtil.isNotEmpty(item.getQcIds())) {
            qcIds = Joiner.on(',').join(item.getQcIds()) + ",";
          }

          final ItemProcurement itemProcurement =
              iItemProcurementService.lambdaQuery().eq(ItemProcurement::getItemId, itemId).one();
          if (itemProcurement != null) {
            final String qcIdsL = itemProcurement.getQcIds();
            if (!Objects.equals(qcIdsL, qcIds)) {
              operateLogDomainService.addOperatorLog(
                  0L,
                  OperateLogTarget.ITEM,
                  itemId,
                  "P系统修改QC负责人同步",
                  Collections.singletonList(new ChangePropertyObj("qcIds", qcIdsL, qcIds)));

              iItemProcurementService
                  .lambdaUpdate()
                  .set(ItemProcurement::getQcIds, qcIds)
                  .eq(ItemProcurement::getItemId, itemId)
                  .update();

              EventBusUtil.post(new ItemQcChangeEvent(itemId, qcIdsL, qcIds), true);
            }

            // 采购员更新
            if (Objects.nonNull(item.getPurchaseId())) {
              final Optional<Buyer> buyerOpt = buyerGateway.addBuyerByUid(item.getPurchaseId());
              buyerOpt.ifPresent(
                  val -> {
                    // 采购员发生了改变
                    if (!val.getId().equals(itemProcurement.getBuyerId())) {
                      operateLogDomainService.addOperatorLog(
                          0L, OperateLogTarget.ITEM, itemId, "P系统修改采购员同步");
                      iItemProcurementService
                          .lambdaUpdate()
                          .set(ItemProcurement::getBuyerId, val.getId())
                          .eq(ItemProcurement::getItemId, itemId)
                          .update();
                    }
                  });
            }
          }
        }
      }
    }
  }

  @Resource ReqTemplate reqTemplate;

  @Override
  public SingleResponse<ItemSkuDetailVo> getItemSkuDetail(Long itemId) {
    if (itemId == null || itemId <= 0) {
      throw ExceptionPlusFactory.bizException(ErrorCode.VERIFY_PARAM, "商品ID入参不合法");
    }
    ItemSkuDetailVo vo = itemService.getItemSkuDetail(itemId);
    return SingleResponse.of(vo);
  }

  @Override
  public PageResponse<ItemMatchMiniProgramVo> itemMatchMiniProgramV1(
      ItemMatchMiniProgramParam param) {
    List<ItemMatchMiniProgramParam.ItemMatchMiniProgramInternalParam> params = param.getParams();
    // 转成 feign 请求参数
    List<ItemSyncMiniProgramMatchParam> matchParams = convertToFeignRequestParam(params);

    // feign 请求
    log.info("请求商品服务同步匹配，feign请求参数：" + JSONObject.toJSONString(matchParams));
    ArkSailorItemResponse<List<ItemSyncMiniProgramMatchResult>> response;
    try {
      response = arkSailorItemFeignClient.syncMatch(matchParams);
    } catch (Exception e) {
      throw ExceptionPlusFactory.bizException(
          ErrorCode.ITEM_BIZ_ERROR, "请求商品服务同步匹配发生异常：" + e.getMessage());
    }
    if (!Objects.equals(ArkSailorItemResponse.SUCCESS_CODE, response.getCode())) {
      throw ExceptionPlusFactory.bizException(
          ErrorCode.ITEM_BIZ_ERROR, "请求商品服务同步匹配失败：" + response.getMsg());
    }
    log.info("商品服务同步匹配，feign响应结果：" + JSONObject.toJSONString(response));

    // 内存分页
    List<ItemSyncMiniProgramMatchResult> responseData = response.getData();
    List<ItemSyncMiniProgramMatchResult> pageRecords =
        responseData.stream().skip(param.getOffset()).limit(param.getPageSize()).collect(toList());
    List<ItemMatchMiniProgramVo> pageVos = new ArrayList<>();
    for (ItemSyncMiniProgramMatchResult record : pageRecords) {
      ItemMatchMiniProgramVo itemMatchMiniProgramVo = new ItemMatchMiniProgramVo();
      itemMatchMiniProgramVo.setName(record.getName());
      itemMatchMiniProgramVo.setItemNo(record.getItemNo());
      itemMatchMiniProgramVo.setSkuNum(record.getSkuNum());
      itemMatchMiniProgramVo.setItemNum(record.getItemNum());
      List<ItemSyncMiniProgramMatchResult.ErpItemListVO> itemList = record.getItemList();
      List<ItemMatchMiniProgramVo.MiniProgramItemVo> itemVos = new ArrayList<>();
      for (ItemSyncMiniProgramMatchResult.ErpItemListVO erpItemListVO : itemList) {
        ItemMatchMiniProgramVo.MiniProgramItemVo vo =
            new ItemMatchMiniProgramVo.MiniProgramItemVo();
        vo.setId(erpItemListVO.getId());
        vo.setName(erpItemListVO.getName());
        vo.setPrice(erpItemListVO.getPrice());
        vo.setAuditStatus(erpItemListVO.getAuditStatus());
        vo.setCreatedAt(erpItemListVO.getCreatedAt());
        vo.setShelfStatus(erpItemListVO.getShelfStatus());
        itemVos.add(vo);
      }
      itemMatchMiniProgramVo.setMiniProgramItemList(itemVos);
      pageVos.add(itemMatchMiniProgramVo);
    }
    return PageResponse.of(pageVos, responseData.size(), param.getPageSize(), param.getPageIndex());
  }

  @Autowired private MallShopGateway mallShopGateway;

  @Override
  public PageResponse<ItemMatchMiniProgramVo> itemMatchMiniProgram(
      ItemMatchMiniProgramParam param) {
    List<ItemMatchMiniProgramParam.ItemMatchMiniProgramInternalParam> params = param.getParams();

    // 转成 feign 请求参数
    List<ItemSyncMiniProgramMatchParam> matchParamsMix = convertToFeignRequestParam(params);

    final Map<Long, List<ItemSyncMiniProgramMatchParam>> paramsGroupingByShopId =
        matchParamsMix.stream().collect(groupingBy(ItemSyncMiniProgramMatchParam::getShopId));

    List<ItemMatchMiniProgramVo> pageVos = new ArrayList<>();
    for (Map.Entry<Long, List<ItemSyncMiniProgramMatchParam>> paramGroup :
        paramsGroupingByShopId.entrySet()) {
      final Long shopId = paramGroup.getKey();
      final List<ItemSyncMiniProgramMatchParam> matchParams = paramGroup.getValue();
      final Map<String, ItemSyncMiniProgramMatchParam> paramsMap =
          matchParams.stream()
              .collect(toMap(ItemSyncMiniProgramMatchParam::getItemNo, Function.identity()));
      final ItemSyncMiniProgramMatchParamV2 itemSyncMiniProgramMatchParamV2 =
          new ItemSyncMiniProgramMatchParamV2();
      itemSyncMiniProgramMatchParamV2.setSyncMatchForms(matchParams);
      itemSyncMiniProgramMatchParamV2.setShopId(shopId);
      // feign 请求
      log.info("请求商品服务同步匹配，feign请求参数：{}", JSONObject.toJSONString(itemSyncMiniProgramMatchParamV2));
      ArkSailorItemResponse<List<ItemSyncMiniProgramMatchResult>> response;
      try {
        response = arkSailorItemFeignClient.syncMatchV2(itemSyncMiniProgramMatchParamV2);
      } catch (Exception e) {
        throw ExceptionPlusFactory.bizException(
            ErrorCode.ITEM_BIZ_ERROR, "请求商品服务同步匹配发生异常：" + e.getMessage());
      }
      if (!Objects.equals(ArkSailorItemResponse.SUCCESS_CODE, response.getCode())) {
        throw ExceptionPlusFactory.bizException(
            ErrorCode.ITEM_BIZ_ERROR, "请求商品服务同步匹配失败：" + response.getMsg());
      }
      log.info("商品服务同步匹配，feign响应结果：" + JSONObject.toJSONString(response));

      // 内存分页
      List<ItemSyncMiniProgramMatchResult> responseData = response.getData();
      for (ItemSyncMiniProgramMatchResult record : responseData) {
        ItemMatchMiniProgramVo itemMatchMiniProgramVo = new ItemMatchMiniProgramVo();
        itemMatchMiniProgramVo.setName(record.getName());
        itemMatchMiniProgramVo.setItemNo(record.getItemNo());
        itemMatchMiniProgramVo.setSkuNum(record.getSkuNum());
        itemMatchMiniProgramVo.setItemNum(record.getItemNum());
        List<ItemSyncMiniProgramMatchResult.ErpItemListVO> itemList = record.getItemList();
        List<ItemMatchMiniProgramVo.MiniProgramItemVo> itemVos = new ArrayList<>();
        for (ItemSyncMiniProgramMatchResult.ErpItemListVO erpItemListVO : itemList) {
          ItemMatchMiniProgramVo.MiniProgramItemVo vo =
              new ItemMatchMiniProgramVo.MiniProgramItemVo();
          vo.setId(erpItemListVO.getId());
          vo.setName(erpItemListVO.getName());
          vo.setPrice(erpItemListVO.getPrice());
          vo.setAuditStatus(erpItemListVO.getAuditStatus());
          vo.setCreatedAt(erpItemListVO.getCreatedAt());
          vo.setShelfStatus(erpItemListVO.getShelfStatus());
          vo.setCategoryId(erpItemListVO.getCategoryId());
          if (NumberUtil.isPositive(erpItemListVO.getCategoryId())) {
            final SingleResponse<CategoryListVO> miniProgramCategory =
                getMiniProgramCategory(shopId, erpItemListVO.getCategoryId());
            ResponseAssert.builderFor(miniProgramCategory)
                .businessAssert(Objects::nonNull)
                .businessErrCode(ErrorCode.MALL_API_ERROR)
                .build()
                .doAssert();
            vo.setCategory(miniProgramCategory.getData());
          }
          itemVos.add(vo);
        }
        itemMatchMiniProgramVo.setMiniProgramItemList(itemVos);
        if (itemList.isEmpty()) {
          final ItemSyncMiniProgramMatchParam itemSyncMiniProgramMatchParam =
              paramsMap.get(record.getItemNo());
          final CategoryListQuery categoryQuery = new CategoryListQuery();
          categoryQuery.setSellerId(shopId);
          final Category category =
              categoryService.getById(itemSyncMiniProgramMatchParam.getErpCategoryId());
          categoryQuery.setName(category.getName());
          final MultiResponse<CategoryListVO> miniProgramCategoryList =
              getMiniProgramCategoryList(categoryQuery);
          ResponseAssert.builderFor(miniProgramCategoryList)
              .errCode(ErrorCode.MALL_API_ERROR)
              .build()
              .doAssert();
          final List<CategoryListVO> miniCategoryData = miniProgramCategoryList.getData();
          if (!miniCategoryData.isEmpty()) {
            List<Category> upperCategoryList =
                categoryGateway.getUpperCategoryList(category.getId());
            final String categoryPath =
                upperCategoryList.stream()
                    .map(Category::getName)
                    .filter(v -> !v.equals("新类目"))
                    .collect(Collectors.joining(","));
            miniCategoryData.stream()
                .filter(v -> categoryPath.equals(v.getCategoryNamePath()))
                .findFirst()
                .ifPresent(
                    categoryListVO -> {
                      itemMatchMiniProgramVo.setCategoryId(categoryListVO.getId());
                      itemMatchMiniProgramVo.setCategory(categoryListVO);
                    });
          }
        }
        final List<Long> shopIds =
            matchParams.stream()
                .flatMap(v -> v.getShopList().stream())
                .distinct()
                .collect(toList());
        final Collection<ProviderShopDetail> providerShopDetails =
            mallShopGateway.queryShopDetail(shopIds);
        final List<ItemMatchMiniProgramVo.Shop> shopList =
            providerShopDetails.stream()
                .map(v -> new ItemMatchMiniProgramVo.Shop(v.getShopId(), v.getShopName()))
                .collect(toList());
        itemMatchMiniProgramVo.setShopList(shopList);
        itemMatchMiniProgramVo.setMallShopId(shopId);
        pageVos.add(itemMatchMiniProgramVo);
      }
    }
    final List<ItemMatchMiniProgramVo> page =
        ListUtil.page(param.getPageIndex() - 1, param.getPageSize(), pageVos);
    return PageResponse.of(page, pageVos.size(), param.getPageSize(), param.getPageIndex());
  }

  @NotNull
  private List<ItemSyncMiniProgramMatchParam> convertToFeignRequestParam(
      List<ItemMatchMiniProgramParam.ItemMatchMiniProgramInternalParam> params) {
    // 批量查询商品
    List<String> itemNos =
        params.stream()
            .map(ItemMatchMiniProgramParam.ItemMatchMiniProgramInternalParam::getItemNo)
            .distinct()
            .collect(toList());
    Map<String, Item> noToItemMap = itemService.getNoToItemMap(itemNos);
    final List<Long> providerIds =
        noToItemMap.values().stream().map(Item::getProviderId).collect(toList());
    final List<Provider> providers =
        providerIds.isEmpty() ? Collections.emptyList() : iProviderService.listByIds(providerIds);
    final Map<Long, Provider> providerMap =
        providers.stream().collect(toMap(Entity::getId, Function.identity()));
    final Map<String, Provider> itemId2providerMap =
        noToItemMap.values().stream()
            .filter(
                item -> item.getProviderId() != 0 && providerMap.containsKey(item.getProviderId()))
            .collect(toMap(Item::getCode, item -> providerMap.get(item.getProviderId())));

    List<ItemSyncMiniProgramMatchParam> matchParams = new ArrayList<>();
    for (ItemMatchMiniProgramParam.ItemMatchMiniProgramInternalParam
        itemMatchMiniProgramInternalParam : params) {
      ItemSyncMiniProgramMatchParam matchParam = new ItemSyncMiniProgramMatchParam();
      final String itemNo = itemMatchMiniProgramInternalParam.getItemNo();
      matchParam.setItemNo(itemNo);
      matchParam.setName(itemMatchMiniProgramInternalParam.getItemStandardName());
      matchParam.setSkuNos(itemMatchMiniProgramInternalParam.getSkuNos());
      final Long selectMallShopId = itemMatchMiniProgramInternalParam.getMallShopId();
      final Long providerMallShopId =
          Optional.ofNullable(itemId2providerMap.get(itemNo))
              .map(Provider::getMallShopId)
              .map(Long::parseLong)
              .orElse(0L);
      final Long defaultMallShopId = refreshConfig.getDefaultMallShopId();
      // noinspection OptionalGetWithoutIsPresent
      matchParam.setShopId(
          Stream.of(selectMallShopId, providerMallShopId, defaultMallShopId)
              .filter(id -> id != null && id > 0L)
              .findFirst()
              .get());
      matchParam.setShopList(
          Stream.of(providerMallShopId, defaultMallShopId)
              .filter(id -> id != null && id > 0)
              .distinct()
              .collect(toList()));
      matchParam.setErpCategoryId(
          Optional.ofNullable(noToItemMap.get(itemNo)).map(Item::getCategoryId).orElse(0L));
      matchParam.setErpItemId(
          Optional.ofNullable(noToItemMap.get(itemNo)).map(Item::getId).orElse(0L));
      matchParams.add(matchParam);
    }
    return matchParams;
  }

  @Override
  public SingleResponse<Boolean> itemSyncMiniProgram(List<ItemSyncMiniProgramParam> params) {
    // 批量查询商品
    List<String> itemNos =
        params.stream().map(ItemSyncMiniProgramParam::getItemNo).distinct().collect(toList());
    Map<String, Item> noToItemMap = itemService.getNoToItemMap(itemNos);

    // 组装 feign 请求参数
    List<ItemSyncMiniProgramRequest> requests = assembleFeignRequestParam(params, noToItemMap);

    // 商品一个一个去请求，但是请求的数据结构依然是集合
    for (ItemSyncMiniProgramRequest request : requests) {
      Item item = noToItemMap.get(request.getItemNo());
      if (item == null) {
        continue;
      }

      Object requestToUse = null;
      ArkSailorItemResponse<Boolean> response = null;
      try {
        final List<ItemSyncMiniProgramRequest> itemSyncForms = Collections.singletonList(request);
        requestToUse = itemSyncForms;
        if (!request.getErrors().isEmpty()) {
          ThirdPlatformSyncState syncState = ThirdPlatformSyncState.ERROR;
          final String errMsg = String.join(";", request.getErrors());
          saveThirdPlatformSyncForSingleItem(
              noToItemMap,
              request.getItemNo(),
              syncState,
              request.getSyncType(),
              errMsg,
              request.getSkuNum(),
              request.getItemNum(),
              request.getParam(),
              requestToUse,
              null);
          continue;
        }
        if (refreshConfig.getEnableMultiShopVersion()) {
          final ItemSyncMiniProgramRequestV2 itemSyncMiniProgramRequestV2 =
              new ItemSyncMiniProgramRequestV2(request.getMallShopId(), itemSyncForms);
          requestToUse = itemSyncMiniProgramRequestV2;
          log.info("请求商品服务同步商品，feign入参数（V2）：" + JsonUtil.toJson(itemSyncMiniProgramRequestV2));
          response = arkSailorItemFeignClient.syncV2(itemSyncMiniProgramRequestV2);
        } else {
          log.info("请求商品服务同步商品，feign入参数：" + JsonUtil.toJson(itemSyncForms));
          response = arkSailorItemFeignClient.sync(itemSyncForms);
        }
        log.info("请求商品服务同步商品, feign响应结果：" + JsonUtil.toJson(response));
      } catch (Exception e) {
        log.error("请求电商商品服务发生异常", e);
        ThirdPlatformSyncState syncState = ThirdPlatformSyncState.ERROR;
        String errMsg = e.getMessage();
        saveThirdPlatformSyncForSingleItem(
            noToItemMap,
            request.getItemNo(),
            syncState,
            request.getSyncType(),
            errMsg,
            request.getSkuNum(),
            request.getItemNum(),
            request.getParam(),
            requestToUse,
            response);
        continue;
      }
      if (!Objects.equals(response.getCode(), ArkSailorItemResponse.SUCCESS_CODE)) {
        ThirdPlatformSyncState syncState = ThirdPlatformSyncState.ERROR;
        String errMsg = response.getMsg();
        saveThirdPlatformSyncForSingleItem(
            noToItemMap,
            request.getItemNo(),
            syncState,
            request.getSyncType(),
            errMsg,
            request.getSkuNum(),
            request.getItemNum(),
            request.getParam(),
            requestToUse,
            response);
        continue;
      }
      ThirdPlatformSyncState syncState = ThirdPlatformSyncState.FINISH;
      String errMsg = "";
      saveThirdPlatformSyncForSingleItem(
          noToItemMap,
          request.getItemNo(),
          syncState,
          request.getSyncType(),
          errMsg,
          request.getSkuNum(),
          request.getItemNum(),
          request.getParam(),
          requestToUse,
          response);
    }
    return SingleResponse.of(true);
  }

  @Override
  public Map<Long, PartnerItemResp> queryPartnerItemBatch(List<Long> itemIds) {
    if (CollectionUtil.isEmpty(itemIds)) {
      return Collections.emptyMap();
    }
    final Map<Long, String> itemIdToPartnerProviderSnMap =
        itemService
            .lambdaQuery()
            .in(Entity::getId, itemIds)
            .select(Entity::getId, Item::getPartnerProviderItemSn)
            .list()
            .stream()
            .collect(Collectors.toMap(Entity::getId, Item::getPartnerProviderItemSn));
    final ThreadPoolTaskExecutor threadPool = ThreadUtil.getThreadPool(PoolEnum.COMMON_POOL);
    return itemIdToPartnerProviderSnMap.entrySet().stream()
        .map(
            entry ->
                CompletableFuture.supplyAsync(
                    () -> {
                      PartnerItemResp partnerItemResp;
                      try {
                        final PartnerItemCmd cmd = new PartnerItemCmd();
                        cmd.setSearchType(1);
                        cmd.setContext(entry.getValue());
                        cmd.setPageIndex(1);
                        cmd.setPageSize(1);
                        partnerItemResp =
                            itemGateway.partnerQuery(cmd).stream().findFirst().orElse(null);
                      } catch (Exception ignored) {
                        partnerItemResp = null;
                      }
                      return Pair.of(entry.getKey(), partnerItemResp);
                    },
                    threadPool))
        .map(cf -> Errors.rethrow().wrap(() -> cf.get()).get())
        .filter(v -> Objects.nonNull(v.getRight()))
        .collect(Collectors.toMap(Pair::getLeft, Pair::getRight));
  }

  private void saveThirdPlatformSyncForSingleItem(
      Map<String, Item> noToItemMap,
      String itemNo,
      ThirdPlatformSyncState syncState,
      Integer syncType,
      String errMsg,
      Integer skuNum,
      Integer itemNum,
      ItemSyncMiniProgramParam syncParams,
      Object request,
      ArkSailorItemResponse response) {
    Item item = noToItemMap.get(itemNo);
    if (item == null) {
      return;
    }

    ThirdPlatformSync thirdPlatformSync = new ThirdPlatformSync();
    thirdPlatformSync.setPlatformType(PlatformType.WE_CHAT);
    thirdPlatformSync.setItemId(item.getId());
    thirdPlatformSync.setItemCode(item.getCode());
    thirdPlatformSync.setState(syncState);
    if (Objects.equals(syncType, ItemSyncMiniProgramParam.SYNC_TYPE_NEW)) {
      thirdPlatformSync.setType(ThirdPlatformSyncType.MINI_NEW);
    } else if (Objects.equals(syncType, ItemSyncMiniProgramParam.SYNC_TYPE_OVERRIDE)) {
      thirdPlatformSync.setType(ThirdPlatformSyncType.MINI_OVERRIDE);
    } else {
      thirdPlatformSync.setType(ThirdPlatformSyncType.MINI_EDIT);
    }
    thirdPlatformSync.setSkuCount(skuNum == null ? 0 : skuNum);
    thirdPlatformSync.setPlatformItemCount(itemNum == null ? 0 : itemNum);
    thirdPlatformSync.setName(item.getName());
    thirdPlatformSync.setError(errMsg);
    thirdPlatformSync.setSyncParam(JsonUtil.toJson(syncParams));
    thirdPlatformSync.setEnv(ApplicationContextUtil.getActiveProfile());
    thirdPlatformSyncService.save(thirdPlatformSync);

    iThirdPlatformSyncLogService.save(
        ThirdPlatformSyncLog.builder()
            .syncId(thirdPlatformSync.getId())
            .platformType(PlatformType.WE_CHAT)
            .itemId(item.getId())
            .req(JsonUtil.toJson(request))
            .resp(JsonUtil.toJson(response))
            .errorLevel(
                StringUtil.isNotEmpty(errMsg)
                    ? PlatformSyncErrorLevel.ERROR
                    : PlatformSyncErrorLevel.NONE)
            .error(errMsg)
            .build());
  }

  private void saveThirdPlatformSync(
      List<ItemSyncMiniProgramParam> params,
      ThirdPlatformSyncState syncState,
      String errMsg,
      Map<String, Item> noToItemMap) {
    List<ThirdPlatformSync> thirdPlatformSyncList = new ArrayList<>();
    for (ItemSyncMiniProgramParam param : params) {
      Item item = noToItemMap.get(param.getItemNo());
      if (item == null) {
        continue;
      }
      ThirdPlatformSync thirdPlatformSync = new ThirdPlatformSync();
      thirdPlatformSync.setPlatformType(PlatformType.WE_CHAT);
      thirdPlatformSync.setItemId(item.getId());
      thirdPlatformSync.setItemCode(item.getCode());
      thirdPlatformSync.setState(syncState);
      if (Objects.equals(param.getSyncType(), ItemSyncMiniProgramParam.SYNC_TYPE_NEW)) {
        thirdPlatformSync.setType(ThirdPlatformSyncType.MINI_NEW);
      } else if (Objects.equals(param.getSyncType(), ItemSyncMiniProgramParam.SYNC_TYPE_OVERRIDE)) {
        thirdPlatformSync.setType(ThirdPlatformSyncType.MINI_OVERRIDE);
      } else {
        thirdPlatformSync.setType(ThirdPlatformSyncType.MINI_EDIT);
      }
      thirdPlatformSync.setSkuCount(param.getSkuNum() == null ? 0 : param.getSkuNum());
      thirdPlatformSync.setPlatformItemCount(param.getItemNum() == null ? 0 : param.getItemNum());
      thirdPlatformSync.setName(item.getName());
      thirdPlatformSync.setError(errMsg);
      thirdPlatformSync.setEnv(ApplicationContextUtil.getActiveProfile());
      thirdPlatformSyncList.add(thirdPlatformSync);
    }
    thirdPlatformSyncService.saveBatch(thirdPlatformSyncList);
  }

  @NotNull
  private List<ItemSyncMiniProgramRequest> assembleFeignRequestParam(
      List<ItemSyncMiniProgramParam> params, Map<String, Item> noToItemMap) {
    List<ItemSyncMiniProgramRequest> requests = new ArrayList<>();
    for (ItemSyncMiniProgramParam param : params) {
      Item item = noToItemMap.get(param.getItemNo());
      if (item == null) {
        continue;
      }
      Long itemId = item.getId();
      ItemDrawer itemDrawer = itemDrawerService.getMergeMainItemDrawer(itemId);
      if (itemDrawer == null) {
        continue;
      }
      BrandEntity brand = brandGateway.getBrand(item.getBrandId());
      List<ItemDrawerImage> itemDrawerImages =
          itemDrawerImageService.selectSortedListByDrawerId(itemDrawer.getId());
      List<MainImage> mainImages = new ArrayList<>();
      MainVideo mainVideo = new MainVideo();
      StringBuilder detailHtml = new StringBuilder();
      for (ItemDrawerImage drawerImage : itemDrawerImages) {
        // 主图图片
        if (Objects.equals(ItemDrawerImageTypeEnum.ITEM, drawerImage.getType())
            && Objects.equals(
                ItemDrawerImageFileTypeEnum.IMAGE.getValue(), drawerImage.getFileType())) {
          MainImage mainImage = new MainImage();
          mainImage.setFileName(drawerImage.getFilename());
          final String url = urlEncode(drawerImage.getUrl());
          mainImage.setUrl(url);
          mainImage.setHeight(0);
          mainImage.setWidth(0);
          // 缩略图
          mainImage.setThumbnail(url + "!200");
          mainImages.add(mainImage);
        }
        // 主图视频
        if (Objects.equals(ItemDrawerImageTypeEnum.MAIN_IMAGE_VIDEO, drawerImage.getType())
            && Objects.equals(
                ItemDrawerImageFileTypeEnum.VIDEO.getValue(), drawerImage.getFileType())) {
          mainVideo.setFileName(drawerImage.getFilename());
          mainVideo.setUrl(urlEncode(drawerImage.getUrl()));
          mainVideo.setCover(urlEncode(drawerImage.getFirstImage()));
          mainVideo.setDuration("");
          mainVideo.setSize("");
        }
        // 商品详情
        if (Objects.equals(ItemDrawerImageTypeEnum.DETAIL, drawerImage.getType())) {
          String img =
              "<img src=\"" + urlEncode(drawerImage.getUrl()) + "\" style=\"\" data-link=\"\">";
          detailHtml.append(img);
        }
      }
      // to jsonString
      String mainImagesJsonStr = JSONObject.toJSONString(mainImages);
      String mainVideoJsonStr = JSONObject.toJSONString(mainVideo);

      ItemSyncMiniProgramRequest request = new ItemSyncMiniProgramRequest();
      request.setParam(param);
      request.setMiniItemIds(param.getMiniItemIds());
      request.setItemNo(param.getItemNo());
      String tbTitle = itemDrawer.getTbTitle();
      request.setName(tbTitle);

      List<String> categoryNames = categoryService.getCategoryNames(param.getItemNo());
      if (CollectionUtil.isNotEmpty(categoryNames) && "新类目".equals(categoryNames.get(0))) {
        categoryNames.remove(0);
      }
      request.setCategoryList(categoryNames);

      request.setSaleKey(Optional.of(itemDrawer).map(ItemDrawer::getHomeCopy).orElse(""));

      request.setBrandName(Optional.ofNullable(brand).map(BrandEntity::getName).orElse(""));
      request.setProductName(item.getName());
      request.setImages(mainImagesJsonStr);
      request.setVideo(mainVideoJsonStr);

      ItemProcurement itemProcurement = iItemProcurementService.getByItemId(itemId);
      // 转换成小程序那边的发货类型
      Integer miniProgramShipmentType =
          convertToMiniProgramShipmentType(
              Optional.ofNullable(itemProcurement).map(ItemProcurement::getDelivery).orElse(""));
      request.setShipmentType(miniProgramShipmentType);
      request.setDetailHtml(detailHtml.toString());
      // 商品对应的所有 sku
      List<ItemSku> itemSkus = iItemSkuService.selectListByItemId(itemId);
      List<String> skuCodes = itemSkus.stream().map(ItemSku::getSkuCode).collect(toList());
      Map<String, SkuWithNewGoodsDto> skuWithNewGoodsDtoMap =
          newGoodsService.skuCodeToDtoMap(skuCodes);
      List<ItemSyncMiniProgramRequest.ErpItemSku> erpItemSkus = new ArrayList<>();
      for (ItemSku sku : itemSkus) {
        ItemSyncMiniProgramRequest.ErpItemSku erpItemSku =
            new ItemSyncMiniProgramRequest.ErpItemSku();
        // fix 同步时 sku 编码若有需取供货指定编码
        erpItemSku.setSkuNo(sku.getSupplierCode());

        // 划线价和日销价从新品库中获取
        SkuWithNewGoodsDto skuWithNewGoodsDto = skuWithNewGoodsDtoMap.get(sku.getSkuCode());
        erpItemSku.setAdvicePrice(skuWithNewGoodsDto.getLinePrice());
        erpItemSku.setSalePrice(skuWithNewGoodsDto.getDailyPrice());
        // 规格属性
        List<AttrDto> skuAttrDtos = iItemSkuService.selectSkuAttrDtosBySkuId(sku.getId());
        List<ItemSyncMiniProgramRequest.ErpItemSkuAttr> erpItemSkuAttrs = new ArrayList<>();
        for (AttrDto skuAttrDto : skuAttrDtos) {
          ItemSyncMiniProgramRequest.ErpItemSkuAttr erpItemSkuAttr =
              new ItemSyncMiniProgramRequest.ErpItemSkuAttr();
          String attrName = skuAttrDto.getKey();
          String attrValue = skuAttrDto.getValue();
          if (StringUtil.isBlank(attrValue)) {
            continue;
          }
          erpItemSkuAttr.setKey(attrName);
          erpItemSkuAttr.setValue(attrValue);
          erpItemSkuAttrs.add(erpItemSkuAttr);
        }
        erpItemSku.setAttrList(erpItemSkuAttrs);
        erpItemSkus.add(erpItemSku);
      }
      request.setSkuList(erpItemSkus);
      // 组成规格的所有属性
      List<AttrDto> itemAttrDtos = itemService.selectItemAttrDtosByItemId(item.getId());
      // 相同 key 聚合到一起
      Map<String, List<ItemSyncMiniProgramRequest.ErpItemAttr.AttrValue>> map = new HashMap<>(16);
      for (AttrDto itemAttrDto : itemAttrDtos) {
        String key = itemAttrDto.getKey();
        String value = itemAttrDto.getValue();

        List<ItemSyncMiniProgramRequest.ErpItemAttr.AttrValue> attrValues =
            map.computeIfAbsent(key, k -> new ArrayList<>());

        if (StringUtil.isBlank(itemAttrDto.getImageUrl())) {
          attrValues.add(new ItemSyncMiniProgramRequest.ErpItemAttr.AttrValue(value, ""));
        } else {
          AttrImage attrImage = new AttrImage();
          attrImage.setFileName(
              itemAttrDto.getImageFileName() == null ? "" : itemAttrDto.getImageFileName());
          final String imageUrl = urlEncode(itemAttrDto.getImageUrl());
          attrImage.setUrl(imageUrl);
          attrImage.setThumbnail(imageUrl + "!200");
          attrImage.setHeight(0);
          attrImage.setWidth(0);
          String attrImageJson = JSONObject.toJSONString(attrImage);
          attrValues.add(
              new ItemSyncMiniProgramRequest.ErpItemAttr.AttrValue(value, attrImageJson));
        }
      }
      List<ItemSyncMiniProgramRequest.ErpItemAttr> itemAttrList = new ArrayList<>();
      for (Map.Entry<String, List<ItemSyncMiniProgramRequest.ErpItemAttr.AttrValue>> entry :
          map.entrySet()) {
        ItemSyncMiniProgramRequest.ErpItemAttr itemAttr =
            new ItemSyncMiniProgramRequest.ErpItemAttr(entry.getKey(), entry.getValue());
        itemAttrList.add(itemAttr);
      }
      request.setAttrList(itemAttrList);
      request.setItemNum(param.getItemNum());
      request.setSkuNum(param.getSkuNum());
      request.setSyncType(param.getSyncType());

      if (Objects.equals(param.getSyncType(), ItemSyncMiniProgramParam.SYNC_TYPE_OVERRIDE)) {
        request.setOverride(true);
      }

      // 传入P系统商品ID
      final Long partnerItemId =
          Optional.ofNullable(item.getPartnerProviderItemSn())
              .filter(StringUtil::isNotBlank)
              .map(v -> StringUtil.removePrefix(v, "S"))
              .map(Long::parseLong)
              .orElse(0L);
      request.setQcItemId(partnerItemId);

      final Long mallShopId = getMallShopId(item);
      request.setMallShopId(mallShopId);

      filterInvalidAttr(request);

      // 商品参数需求
      setItemExtendInfo(item, request, itemSkus);

      request.setCategoryId(param.getSellerCategoryId());
      request.setMallShopId(param.getMallShopId());
      requests.add(request);
    }
    return requests;
  }

  private Long getMallShopId(Item item) {
    final Provider provider = providerGateway.getById(item.getProviderId());
    return Optional.ofNullable(provider)
        .map(Provider::getMallShopId)
        .map(Long::parseLong)
        .filter(v -> v != 0)
        .orElse(refreshConfig.getDefaultMallShopId());
  }

  @Autowired private PsysCategoryConfig psysCategoryConfig;

  private void setItemExtendInfo(
      Item item, ItemSyncMiniProgramRequest request, List<ItemSku> itemSkus) {
    final Long itemId = item.getId();
    final Category category = categoryService.getById(item.getCategoryId());
    Objects.requireNonNull(category, "未关联商品类目");
    final Optional<PartnerItemResp> partnerItemRespOptional = queryPartnerItemByItemId(itemId);
    if (!partnerItemRespOptional.isPresent()) {
      request.getErrors().add("从P系统获取商品数据失败，商品可能未通过审核");
      return;
    }
    partnerItemRespOptional
        .filter(partnerItemResp -> partnerItemResp.isBeautyAndSkincareCategory(psysCategoryConfig))
        .ifPresent(
            partnerItemResp -> {
              // 个护美妆默认展示扩展信息
              request.setIsItemExtendShow(1);
              final ErpItemExtend erpItemExtend = new ErpItemExtend();
              erpItemExtend.setFilingName(partnerItemResp.getName());
              erpItemExtend.setBrandName(partnerItemResp.getBrand());
              erpItemExtend.setRegisterNo(partnerItemResp.getProductFilingNo());
              final ArrayList<FilingEfficacy> filingEfficacyList = Lists.newArrayList();
              final List<String> productFilingEfficiency =
                  partnerItemResp.getProductFilingEfficiency();
              int[] filingEfficacySort = {1};
              if (CollectionUtil.isNotEmpty(productFilingEfficiency)) {
                productFilingEfficiency.sort(Comparator.naturalOrder());
                for (String efficiency : productFilingEfficiency) {
                  final FilingEfficacy filingEfficacy = new FilingEfficacy();
                  filingEfficacy.setEfficacy(getEfficiency(efficiency));
                  filingEfficacy.setSunProtectionIndex("");
                  filingEfficacy.setPaValue("");
                  filingEfficacy.setSort(filingEfficacySort[0]++);
                  filingEfficacyList.add(filingEfficacy);
                }
              }
              if (partnerItemResp.getSunProtectionExponent() != null) {
                final Optional<FilingEfficacy> sunscreenEfficacy =
                    filingEfficacyList.stream()
                        .filter(v -> v.getEfficacy().equals("防晒"))
                        .findFirst();
                sunscreenEfficacy.ifPresent(
                    filingEfficacy -> {
                      filingEfficacy.setSunProtectionIndex(
                          partnerItemResp.getSunProtectionExponent());
                      filingEfficacy.setPaValue(partnerItemResp.getPmValue());
                      filingEfficacy.setSort(filingEfficacySort[0]);
                    });
              }
              erpItemExtend.setFilingEfficacyList(filingEfficacyList);
              if (CollectionUtil.isNotEmpty(partnerItemResp.getApplicablePerson())) {
                erpItemExtend.setApplicablePopulations(
                    partnerItemResp.getApplicablePerson().stream()
                        .map(ApplicablePopulation::new)
                        .collect(toList()));
              }
              if (CollectionUtil.isNotEmpty(partnerItemResp.getApplicableSkinTypeNew())) {
                erpItemExtend.setSuitableSkin(
                    String.join("、", partnerItemResp.getApplicableSkinTypeNew()));
              } else {
                erpItemExtend.setSuitableSkin(partnerItemResp.getApplicableSkinType());
              }
              final boolean isSpecialPurpose =
                  "是".equals(partnerItemResp.getIsSpecialUseCosmetics());
              erpItemExtend.setIsSpecialPurpose(isSpecialPurpose ? 1 : 0);
              if (isSpecialPurpose) {
                erpItemExtend.setRegisterPerson(partnerItemResp.getDomesticResponsiblePerson());
                erpItemExtend.setRegisterAddress(
                    partnerItemResp.getDomesticResponsiblePersonAddr());
              } else {
                erpItemExtend.setFilingPerson(partnerItemResp.getDomesticResponsiblePerson());
                erpItemExtend.setFilingAddress(partnerItemResp.getDomesticResponsiblePersonAddr());
              }
              erpItemExtend.setNetContents(getJoinedItemSkuProps(itemSkus, "netContent"));
              erpItemExtend.setShelfLifes(getJoinedItemSkuProps(itemSkus, "shelfLife"));
              erpItemExtend.setCustomAttributeList(Collections.emptyList());
              request.setErpItemExtend(erpItemExtend);
            });
  }

  private static String getEfficiency(String efficiency) {
    return efficiency.replaceFirst("\\d+\\s", "");
  }

  private static String getJoinedItemSkuProps(List<ItemSku> itemSkus, String netContent) {
    return itemSkus.stream()
        .sorted(Comparator.comparing(ItemSku::getId))
        .map(ItemSku::getProps)
        .filter(Objects::nonNull)
        .map(props -> (String) props.getOrDefault(netContent, ""))
        .filter(StringUtil::isNotBlank)
        .distinct()
        .collect(joining(" "));
  }

  private String urlEncode(String url) {
    if (refreshConfig.getEncodeUrlForSyncMiniItem()) {
      return URLUtil.encode(URLUtil.decode(url));
    }
    return url;
  }

  /**
   * 过滤SKU属性和商品属性中可能存在"/"，若某个属性下仅有一个属性值"/"，则认为该属性无效，将其过滤
   *
   * @param request 商品同步小程序请求封装
   */
  public void filterInvalidAttr(ItemSyncMiniProgramRequest request) {
    final List<String> filteredAttrNames =
        request.getAttrList().stream()
            .filter(
                erpItemAttr ->
                    erpItemAttr.getValues().stream()
                        .allMatch(attrValue -> "/".equals(attrValue.getName())))
            .map(ErpItemAttr::getKey)
            .collect(toList());
    if (!filteredAttrNames.isEmpty()) {
      for (String filteredAttrName : filteredAttrNames) {
        final List<ErpItemAttr> filteredItemAttrList =
            request.getAttrList().stream()
                .filter(erpItemAttr -> !erpItemAttr.getKey().equals(filteredAttrName))
                .collect(toList());
        request.setAttrList(filteredItemAttrList);
        final List<ErpItemSku> skuList = request.getSkuList();
        for (ErpItemSku erpItemSku : skuList) {
          final List<ErpItemSkuAttr> filteredSkuAttrList =
              erpItemSku.getAttrList().stream()
                  .filter(attr -> !attr.getKey().equals(filteredAttrName))
                  .collect(toList());
          erpItemSku.setAttrList(filteredSkuAttrList);
        }
      }
    }
  }

  // 小程序那边：发货类型 1-工厂发货 2-自营发货
  // ERP 这边：0-仓库发货。1-工厂发货。2-仓库工厂均发货
  private Integer convertToMiniProgramShipmentType(String delivery) {
    if (!StringUtils.hasText(delivery)) {
      // 默认用工厂发货
      return 1;
    }
    if ("2".equals(delivery)) {
      return 1;
    }
    List<String> deliveryTypes = Arrays.asList(delivery.split(","));
    if (deliveryTypes.contains("1")) {
      return 1;
    } else {
      return 2;
    }
  }

  @Data
  public static class MainImage {
    private String fileName;
    private String url;
    private String thumbnail;
    private Integer width;
    private Integer height;
  }

  @Data
  public static class MainVideo {
    private String fileName;
    private String url;
    private String cover;
    private String duration;
    private String size;
  }

  @Data
  public static class AttrImage {
    private String fileName;
    private String url;
    private String thumbnail;
    private Integer width;
    private Integer height;
  }

  private Long updateProvider(String name, String no) {
    IProviderService bean = SpringUtil.getBean(IProviderService.class);
    Optional<Provider> provider =
        bean.lambdaQuery().like(Provider::getName, name).select().oneOpt();
    if (provider.isPresent()) {
      boolean needUpdate = !no.equals(provider.get().getProviderNo());
      if (needUpdate) {
        bean.lambdaUpdate()
            .set(Provider::getProviderNo, no)
            .eq(Provider::getId, provider.get().getId());
      }
      return provider.get().getId();
    }

    // 新建供应商
    Provider provider1 = new Provider();
    provider1.setName(name);
    provider1.setStatus(ProviderStatus.COOPERATION);
    provider1.setType(ProviderType.SELF_SUPPORT);
    provider1.setUnifySocialCreditCodes("excel新增" + System.currentTimeMillis());
    provider1.setProviderNo(no);
    bean.save(provider1);
    return provider1.getId();
  }

  @Override
  public MultiResponse<ItemSkuPriceVO> skuPriceList(String skuCode, Integer type) {
    // 默认查询类型为0，即SKU成本价格
    if (Objects.isNull(type)) type = 0;
    List<ItemSkuPriceVO> collect =
        iItemSkuPriceService
            .lambdaQuery()
            .eq(ItemSkuPrice::getSkuCode, skuCode)
            .eq(ItemSkuPrice::getType, type)
            .orderByDesc(ItemSkuPrice::getStartTime)
            .orderByDesc(ItemSkuPrice::getId)
            .select()
            .list()
            .stream()
            .map(
                itemSkuPrice -> {
                  ItemSkuPriceVO one = new ItemSkuPriceVO();
                  one.setSkuId(itemSkuPrice.getSkuId());
                  one.setSkuCode(itemSkuPrice.getSkuCode());
                  one.setPrice(itemSkuPrice.getPrice());
                  one.setStartTime(itemSkuPrice.getStartTime());
                  one.setEndTime(itemSkuPrice.getEndTime());
                  return one;
                })
            .collect(toList());

    return MultiResponse.of(collect);
  }

  @Override
  public MultiResponse<ItemPriceVO> itemCostPriceList(Long itemId, String priceName) {

    List<ItemPriceVO> collect =
        iItemPriceService
            .lambdaQuery()
            .eq(ItemPrice::getItemId, itemId)
            .eq(ItemPrice::getCustomName, priceName)
            .eq(ItemPrice::getRang, 1)
            .orderByDesc(ItemPrice::getStartTime)
            .orderByDesc(ItemPrice::getCreatedAt)
            .select(
                ItemPrice::getItemId,
                ItemPrice::getPrice,
                ItemPrice::getStartTime,
                ItemPrice::getEndTime,
                ItemPrice::getCustomName)
            .list()
            .stream()
            .map(
                val -> {
                  ItemPriceVO one = new ItemPriceVO();
                  one.setItemId(val.getItemId());
                  one.setPrice(val.getPrice());
                  one.setStartTime(val.getStartTime());
                  one.setEndTime(val.getEndTime());
                  one.setCustomName(val.getCustomName());
                  return one;
                })
            .collect(toList());
    return MultiResponse.of(collect);
  }

  @Override
  public Boolean deletePartnerSn(Long itemId) {
    iItemService
        .lambdaUpdate()
        .set(Item::getPartnerProviderItemSn, "")
        .eq(Item::getId, itemId)
        .update();
    return true;
  }

  @Override
  public SingleResponse<ShortSkuVO> querySku(String skuCode) {
    ItemSku itemSku = itemSkuGateway.getBySkuCode(skuCode);
    if (Objects.isNull(itemSku)) {
      return SingleResponse.of(new ShortSkuVO());
    }
    ShortSkuVO vo = new ShortSkuVO();
    vo.setSkuCode(itemSku.getSkuCode());
    vo.setPurchaseTaxRate(itemSku.getPurchaseTaxRate());
    vo.setSpecifications(itemSku.getSpecifications());

    Item item = itemGateway.getItem(itemSku.getItemId());
    if (Objects.nonNull(item)) {
      vo.setItemName(item.getName());
    }
    return SingleResponse.of(vo);
  }

  @Override
  @Transactional(rollbackFor = Exception.class)
  public SingleResponse<Boolean> deleteSku(String codes) {
    Assert.hasText(codes, "sku编码不得为空");
    List<String> skuCodes = StringUtil.splitTrim(codes, ",");
    Assert.notEmpty(skuCodes, "sku编码英文逗号隔开，不得为空");
    return deleteSku(skuCodes);
  }

  @Override
  public SingleResponse<Boolean> deleteSku(List<String> skuCodes) {
    if (CollectionUtil.isEmpty(skuCodes)) {
      return SingleResponse.of(true);
    }
    final List<ItemSku> itemSkus =
        iItemSkuService
            .lambdaQuery()
            .in(ItemSku::getSkuCode, skuCodes)
            .or()
            .in(ItemSku::getProviderSpecifiedCode, skuCodes)
            .list();
    Assert.notEmpty(itemSkus, "未找到指定编码的SKU");
    return deleteSku0(itemSkus);
  }

  @NonNull
  private SingleResponse<Boolean> deleteSku0(List<ItemSku> itemSkus) {
    if (CollectionUtil.isEmpty(itemSkus)) {
      return SingleResponse.of(true);
    }
    final List<Long> itemSkuIdsToRemoved = itemSkus.stream().map(ItemSku::getId).collect(toList());
    final List<String> itemSkuCodesToRemoved =
        itemSkus.stream().map(ItemSku::getSkuCode).collect(toList());

    // 删除后端商品SKU
    iItemSkuService.removeByIdsWithTime(itemSkuIdsToRemoved);

    // 删除商品参照库SKU
    iSkuService.lambdaUpdate().in(Sku::getSkuCode, itemSkuCodesToRemoved).remove();

    final IItemSkuAttrRefService itemSkuAttrRefService =
        SpringUtil.getBean(IItemSkuAttrRefService.class);
    final List<ItemSkuAttrRef> itemSkuAttrsToRemoved =
        itemSkuAttrRefService
            .lambdaQuery()
            .in(ItemSkuAttrRef::getItemSkuId, itemSkuIdsToRemoved)
            .list();
    // 删除商品SKU属性关联
    itemSkuAttrRefService.removeByIdsWithTime(
        itemSkuAttrsToRemoved.stream().map(ItemSkuAttrRef::getId).collect(toList()));

    // 删除新品商品
    INewGoodsService iNewGoodsService = SpringUtil.getBean(INewGoodsService.class);
    iNewGoodsService.lambdaUpdate().in(NewGoods::getSkuCode, itemSkuCodesToRemoved).remove();

    @SuppressWarnings("OptionalGetWithoutIsPresent")
    final Long itemId = itemSkus.stream().map(ItemSku::getItemId).findAny().get();
    final ItemMapper itemMapper = (ItemMapper) itemService.getDaddyBaseMapper();

    // 删除无效商品属性
    itemMapper.deleteInvalidItemAttrs(itemId);

    // 操作记录
    operateLogDomainService.addOperatorLog(
        UserContext.getUserId(),
        OperateLogTarget.ITEM,
        itemId,
        "删除规格 " + String.join(",", itemSkuCodesToRemoved));

    return SingleResponse.of(true);
  }

  @Override
  public SingleResponse<Boolean> deleteSkuByIds(List<Long> skuIds) {
    if (CollectionUtil.isEmpty(skuIds)) {
      return SingleResponse.of(true);
    }
    return deleteSku0(iItemSkuService.listByIds(skuIds));
  }

  @Override
  public void queryAllItemPartnerSysType() {
    List<Item> list =
        iItemService
            .lambdaQuery()
            .ne(Item::getPartnerProviderItemSn, "")
            .isNotNull(Item::getPartnerProviderItemSn)
            .isNull(Item::getPartnerSysType)
            .list();
    list.forEach(
        val -> {
          try {
            final PartnerItemCmd cmd = new PartnerItemCmd();
            cmd.setSearchType(1);
            cmd.setContext(val.getPartnerProviderItemSn());
            cmd.setPageIndex(1);
            cmd.setPageSize(1);
            Optional<PartnerItemResp> first = itemGateway.partnerQuery(cmd).stream().findFirst();
            if (first.isPresent()) {
              PartnerItemResp partnerItemResp = first.get();
              Integer type = partnerItemResp.getType();
              val.setPartnerSysType(type);
              iItemService.updateById(val);
            }
          } catch (Exception e) {
            log.error("清洗item的技术类目操作异常。itemId:{}", val.getId(), e);
          }
        });
    log.error("清洗item的技术类目操作全部完成。itemSize:{}", list.size());
  }

  @Override
  @XxlJob("ItemBizServiceImpl.syncPsysItemInfo")
  @XxlJobAutoRegister(cron = "0 0 2 * * ? *", author = "徵乌", jobDesc = "同步P系统商品信息")
  @SuppressWarnings({"rawtypes", "unchecked"})
  public void syncPsysItemInfo() {
    List<Item> list = iItemService.lambdaQuery().ne(Item::getPartnerProviderItemSn, "").list();
    final StopWatch stopWatch = new StopWatch();
    stopWatch.start();
    log.info("[同步P系统商品信息] 开始同步，{} 个商品", list.size());
    Flux.fromIterable(list)
        .zipWith(
            Flux.fromIterable(list)
                .publishOn(Schedulers.boundedElastic())
                .map(
                    item -> {
                      final PartnerItemCmd cmd = new PartnerItemCmd();
                      cmd.setSearchType(1);
                      cmd.setContext(item.getPartnerProviderItemSn());
                      cmd.setPageIndex(1);
                      cmd.setPageSize(10);
                      return itemGateway.partnerQuery(cmd).stream().findFirst();
                    })
                .onErrorResume(
                    throwable -> {
                      log.error("[同步P系统商品信息] 查询P系统商品信息异常", throwable);
                      return Mono.just(Optional.empty());
                    }))
        .publishOn(Schedulers.boundedElastic())
        .buffer(50)
        .doOnNext(
            items -> {
              log.info("[同步P系统商品信息] 开始处理 {} 个商品", items.size());
              final ArrayList<Item> backendItems = new ArrayList<>();
              final QcChangeNotify qcChangeNotify = new QcChangeNotify();
              final ArrayList<QcChangeNotify.Item> notifyItems = Lists.newArrayList();
              qcChangeNotify.setItems(notifyItems);
              for (Tuple2<Item, Optional<PartnerItemResp>> itemTuple : items) {
                final Optional<PartnerItemResp> partnerItemResp = itemTuple.getT2();
                partnerItemResp.ifPresent(
                    resp -> {
                      final QcChangeNotify.Item notifyItem = new QcChangeNotify.Item();
                      notifyItem.setId(Long.valueOf(resp.getId()));
                      notifyItem.setItemNo(resp.getItemNo());
                      notifyItem.setQcIds(resp.getQcIds());
                      Optional.ofNullable(resp.getPurchaseId())
                          .map(Long::valueOf)
                          .ifPresent(notifyItem::setPurchaseId);
                      notifyItems.add(notifyItem);

                      final Item item = itemTuple.getT1();
                      final Item itemUpdateObj = new Item();
                      itemUpdateObj.setId(item.getId());
                      itemUpdateObj.setPartnerSysType(resp.getType());
                      backendItems.add(itemUpdateObj);
                    });
              }
              if (!backendItems.isEmpty()) {
                log.info("[同步P系统商品信息] 更新后端商品 {}", JSON.toJSONString(backendItems));
                itemService.updateBatchById(backendItems);
              }
              if (!notifyItems.isEmpty()) {
                log.info("[同步P系统商品信息] 主动触发QC变更事件 {} ", notifyItems);
                qcChange(qcChangeNotify);
              }
            })
        .onErrorContinue(
            (throwable, items) -> {
              log.error("[同步P系统商品信息] 同步异常", throwable);
            })
        .blockLast();
    stopWatch.stop();
    log.info("[同步P系统商品信息] 同步完成 {} by {}ms", list.size(), stopWatch.getTotalTimeMillis());
  }

  @Override
  public Response updateStatus(
      Long itemId,
      ItemStatus newStatus,
      Long downFrameTime,
      String downFrameReason,
      String remark) {
    final Item item = itemService.getById(itemId);
    Objects.requireNonNull(item, "商品查询异常");
    if (itemDomainService.updateStatus(item, newStatus, downFrameTime, downFrameReason, remark)) {
      return Response.buildSuccess();
    }
    return Response.buildFailure(ErrorCode.STATE_ERR.getCode(), "商品状态无需变更");
  }

  @Autowired PlatformItemMapper platformItemMapper;

  private ItemStatus convertItemStatus(PlatformItemStatus platformItemStatus) {
    switch (platformItemStatus) {
      case WAIT_SALE:
        return ItemStatus.WAIT;
      case ON_SALE:
        return ItemStatus.ON;
      case SALE_OUT:
        return ItemStatus.OUT;
      default:
        return null;
    }
  }

  @Override
  public void listenPlatformItemUpdate(PlatformItemUpdateEvent event) {
    final PlatformItem platformItem = event.getPlatformItem();
    if (!NumberUtil.isPositive(platformItem.getItemId())) {
      return;
    }
    final Item item = itemService.getById(platformItem.getItemId());
    // 查询当前后端商品的在售平台商品数量（仅统计自研商城平台商品）
    final long onSalePlatformItemCountMall =
        platformItemMapper.countOnSalePlatformItem(item.getId(), Platform.LAOBASHOP.getValue());
    final ItemBizService itemBizService = (ItemBizService) AopContext.currentProxy();

    Integer oldStatus = item.getStatus();
    // 上新状态【待上架】&& 在售平台商品数量 > 0 ->> 更新上新状态到【已上架】
    if (item.getLaunchStatus().compareTo(ItemLaunchStatus.TO_BE_RELEASED.getValue()) == 0
        && onSalePlatformItemCountMall > 0) {
      if (itemBizService.updateLaunchStatus(item.getId(), ItemLaunchStatus.HAS_BE_RELEASED)) {
        operateLogDomainService.addOperatorLog(
            0L,
            OperateLogTarget.ITEM_UP,
            item.getId(),
            "检测到待上架商品已上架平台商品，系统自动修改商品状态为'在售中'、上新状态为'已上新'");

        final LambdaUpdateChainWrapper<Item> updateChainWrapper =
            itemService.lambdaUpdate().eq(Entity::getId, item.getId());
        final Long currentTime = DateUtil.currentTime();
        updateChainWrapper.set(Item::getUpFrameTime, currentTime);
        if (item.getFirstUpFrameTime() == 0) {
          updateChainWrapper.set(Item::getFirstUpFrameTime, currentTime);
        }
        updateChainWrapper.update();
      }
    }

    // // 商品状态为待上架，在售平台商品数大于0
    // if (ItemStatus.WAIT.getValue().equals(oldStatus) &&
    // onSalePlatformItemCountMall > 0) {
    // // ERP后台创建的商品需检查上新流程状态
    // if (DataSource.SELF == item.getSource()) {
    // // 当上新流程状态为【待上架】、【已上架】时才允许状态同步
    // if
    // (item.getLaunchStatus().compareTo(ItemLaunchStatus.TO_BE_RELEASED.getValue())
    // >= 0) {
    // if (updateStatusById(item.getId(), ItemStatus.ON, ItemStatus.WAIT)) {
    // operateLogDomainService.addOperatorLog(
    // 0L, OperateLogTarget.ITEM_UP, item.getId(),
    // "平台商品上架，系统自动将商品状态从'待上架'修改为'在售中'");
    // }
    // } else {
    // log.info(
    // "检测到【{}】在售商品数（{}）大于0，因商品上新流程未到达【待上架】，暂不做状态同步",
    // item.getCode(),
    // onSalePlatformItemCountMall);
    // }
    // } else {
    // if (updateStatusById(item.getId(), ItemStatus.ON, ItemStatus.WAIT)) {
    // operateLogDomainService.addOperatorLog(
    // 0L, OperateLogTarget.ITEM_UP, item.getId(),
    // "平台商品上架，系统自动将商品状态从'待上架'修改为'在售中'");
    // }
    // }
    // }
    //
    // // 商品状态为已上架，在售平台商品数为0
    // if (ItemStatus.ON.getValue().equals(oldStatus) && onSalePlatformItemCountMall
    // <= 0) {
    // if (updateStatusById(item.getId(), ItemStatus.WAIT, ItemStatus.ON)) {
    // operateLogDomainService.addOperatorLog(
    // 0L,
    // OperateLogTarget.ITEM_DOWN_TEMPORARY,
    // item.getId(),
    // "因平台商品全部下架，系统自动将商品状态从'在售中'修改为'待上架'");
    // }
    // }
  }

  @Override
  public MultiResponse<CategoryListVO> getMiniProgramCategoryList(
      MiniProgramCategoryListQuery categoryQuery) {
    final Item item = itemGateway.getItem(categoryQuery.getItemId());
    Assert.notNull(item, "商品不存在");
    final Long mallShopId = getMallShopId(item);
    final CategoryListQuery arkCategoryQuery = new CategoryListQuery();
    arkCategoryQuery.setSellerId(mallShopId);
    arkCategoryQuery.setName(categoryQuery.getName());
    arkCategoryQuery.setParentId(categoryQuery.getParentId());
    arkCategoryQuery.setCategoryId(categoryQuery.getCategoryId());
    final Result<List<CategoryListVO>> categoryList =
        arkSailorItemFeignClient.getCategoryList(arkCategoryQuery);
    if (categoryList.getCode() != Result.SUCCESSFUL_CODE) {
      // noinspection unchecked
      return MultiResponse.buildFailure(ErrorCode.MALL_API_ERROR.getCode(), categoryList.getMsg());
    }
    return MultiResponse.of(categoryList.getData());
  }

  @Override
  public void cooperateMode(CooperateModeNotify notify) {
    final String itemNo = notify.getItemNo();
    final List<Integer> psysCooperator = notify.getCooperator();
    if (psysCooperator == null || psysCooperator.isEmpty()) {
      return;
    }
    final List<PSysCooperatorEnum> pSysCooperatorEnums =
        psysCooperator.stream()
            .map(PSysCooperatorEnum::valueOf)
            .filter(Objects::nonNull)
            .collect(toList());
    if (pSysCooperatorEnums.isEmpty()) {
      return;
    }
    final List<PSysBusinessTypeEnum> businessType =
        Optional.ofNullable(notify.getBusinessType())
            .map(it -> it.stream().map(PSysBusinessTypeEnum::valueOf).collect(toList()))
            .orElseGet(Collections::emptyList);
    final Map<PSysCooperatorEnum, List<PSysBusinessTypeEnum>> businessTypeGroup =
        businessType.stream()
            .filter(it -> it.getCooperator() != null)
            .collect(groupingBy(PSysBusinessTypeEnum::getCooperator));
    final ArrayList<CascadedDivisionLevel> cascadedDivisionLevels = new ArrayList<>();
    for (PSysCooperatorEnum pSysCooperatorEnum : pSysCooperatorEnums) {
      final DivisionLevelValueEnum corpTypeEnum =
          DivisionLevelValueEnum.mapFromPSysCooperatorEnum(pSysCooperatorEnum);
      if (corpTypeEnum == null) {
        continue;
      }
      final CascadedDivisionLevel cascadedDivisionLevel = new CascadedDivisionLevel(corpTypeEnum);
      final List<PSysBusinessTypeEnum> pSysBusinessTypeEnums =
          businessTypeGroup.get(pSysCooperatorEnum);
      if (!pSysBusinessTypeEnums.isEmpty()) {
        pSysBusinessTypeEnums.stream()
            .map(DivisionLevelValueEnum::mapFromPSysBusinessTypeEnum)
            .filter(Objects::nonNull)
            .forEach(cascadedDivisionLevel::addSubValue);
      }
      cascadedDivisionLevels.add(cascadedDivisionLevel);
    }
    for (Item item : itemService.lambdaQuery().in(Item::getPartnerProviderItemSn, itemNo).list()) {
      bizLevelDivisionService.saveCascadedLevels(
          BizUnionTypeEnum.SPU,
          item.getId(),
          item.getSupplierCode(),
          cascadedDivisionLevels,
          Arrays.asList(DivisionLevelEnum.BUSINESS_TYPE, DivisionLevelEnum.COOPERATION));

      final String logMsg =
          String.format(
              "[收到P系统商品合作模式修改通知] 修改后端商品[后端商品ID=%s,P系统款号=%s]业务权限: %s",
              item.getId(), item.getPartnerProviderItemSn(), cascadedDivisionLevels);
      log.info(logMsg);
      operateLogDomainService.addOperatorLog(0L, OperateLogTarget.ITEM, item.getId(), logMsg);
    }
  }

  public MultiResponse<CategoryListVO> getMiniProgramCategoryList(CategoryListQuery query) {
    final Result<List<CategoryListVO>> categoryList =
        arkSailorItemFeignClient.getCategoryList(query);
    if (categoryList.getCode() != Result.SUCCESSFUL_CODE) {
      // noinspection unchecked
      return MultiResponse.buildFailure(ErrorCode.MALL_API_ERROR.getCode(), categoryList.getMsg());
    }
    return MultiResponse.of(categoryList.getData());
  }

  public SingleResponse<CategoryListVO> getMiniProgramCategory(Long shopId, Long categoryId) {
    if (Objects.isNull(categoryId)) {
      return SingleResponse.of(null);
    }
    final CategoryListQuery arkCategoryQuery = new CategoryListQuery();
    arkCategoryQuery.setSellerId(shopId);
    arkCategoryQuery.setCategoryId(categoryId);
    final Result<List<CategoryListVO>> categoryList =
        arkSailorItemFeignClient.getCategoryList(arkCategoryQuery);
    if (categoryList.getCode() != Result.SUCCESSFUL_CODE) {
      // noinspection unchecked
      return SingleResponse.buildFailure(ErrorCode.MALL_API_ERROR.getCode(), categoryList.getMsg());
    }
    final List<CategoryListVO> data = categoryList.getData();
    if (!data.isEmpty()) {
      return SingleResponse.of(data.get(0));
    }
    return SingleResponse.of(null);
  }

  @Override
  public PageResponse<SpuWindowPageVo> spuWindowsPageQuery(SpuWindowPageQuery pageQuery) {
    final PageInfo<Item> itemPageInfo =
        PageHelper.startPage(pageQuery.getPageIndex(), pageQuery.getPageIndex())
            .doSelectPageInfo(
                () -> {
                  final LambdaQueryChainWrapper<Item> wrapper = itemService.lambdaQuery();
                  if (StrUtil.isNotBlank(pageQuery.getName())) {
                    wrapper.like(Item::getName, pageQuery.getName());
                  }
                  if (StrUtil.isNotBlank(pageQuery.getCode())) {
                    wrapper.like(Item::getCode, pageQuery.getCode());
                  }
                  if (Objects.nonNull(pageQuery.getBrandId())) {
                    wrapper.eq(Item::getBrandId, pageQuery.getBrandId());
                  }
                  if (StrUtil.isNotBlank(pageQuery.getSNo())) {
                    wrapper.eq(Item::getPartnerProviderItemSn, pageQuery.getSNo());
                  }
                  if (Objects.nonNull(pageQuery.getCategoryId())) {
                    wrapper.eq(Item::getCategoryId, pageQuery.getCategoryId());
                  }
                  if (Objects.nonNull(pageQuery.getProviderId())) {
                    wrapper.eq(Item::getProviderId, pageQuery.getProviderId());
                  }
                  if (Objects.nonNull(pageQuery.getStatus())) {
                    wrapper.eq(Item::getStatus, pageQuery.getStatus());
                  }
                  if (CollUtil.isNotEmpty(pageQuery.getBusinessLines())) {
                    wrapper.and(
                        w ->
                            pageQuery
                                .getBusinessLines()
                                .forEach(
                                    val -> {
                                      wrapper.like(Item::getBusinessLine, val);
                                      wrapper.or();
                                    }));
                  }
                  wrapper.orderByDesc(Item::getId);
                  wrapper.list();
                });

    if (CollUtil.isEmpty(itemPageInfo.getList())) {
      return PageResponse.of(
          new LinkedList<>(),
          (int) itemPageInfo.getTotal(),
          pageQuery.getPageSize(),
          pageQuery.getPageIndex());
    }

    final Set<Long> brandIdSet =
        itemPageInfo.getList().stream().map(Item::getBrandId).collect(toSet());
    final Map<Long, Brand> brandMap = brandGateway.batchQueryBrandById(brandIdSet);
    final Set<Long> providerIdSet =
        itemPageInfo.getList().stream().map(Item::getProviderId).collect(toSet());
    final Map<Long, PartnerProviderResp> providerMap =
        providerGateway.partnerBatchQueryByIds(providerIdSet);
    final Set<Long> categoryIdSet =
        itemPageInfo.getList().stream().map(Item::getCategoryId).collect(toSet());
    final Map<Long, Category> categoryMap = categoryService.batchQueryById(categoryIdSet);

    final List<SpuWindowPageVo> voList =
        itemPageInfo.getList().stream()
            .map(
                val -> {
                  SpuWindowPageVo vo = new SpuWindowPageVo();
                  vo.setItemId(val.getId());
                  vo.setItemCode(val.getCode());
                  vo.setName(val.getName());
                  vo.setStatus(val.getStatus());
                  final Brand brand = brandMap.get(val.getBrandId());
                  vo.setBrand(Objects.nonNull(brand) ? brand.getName() : "");
                  final PartnerProviderResp partnerProviderResp =
                      providerMap.get(val.getProviderId());
                  vo.setProvider(
                      Objects.nonNull(partnerProviderResp)
                          ? partnerProviderResp.getOrganizationName()
                          : "");
                  vo.setSNo(val.getPartnerProviderItemSn());
                  List<Integer> b =
                      StrUtil.isNotBlank(val.getBusinessLines())
                          ? Arrays.stream(val.getBusinessLines().split(","))
                              .map(Integer::parseInt)
                              .collect(toList())
                          : new LinkedList<>();
                  vo.setBusinessLines(b);
                  final Category category = categoryMap.get(val.getCategoryId());
                  vo.setCategory(Objects.nonNull(category) ? category.getPath() : "");
                  vo.setOnShelfTime(DateUtil.format(val.getEstimateSaleTime()));

                  return vo;
                })
            .collect(toList());

    return PageResponse.of(
        voList, (int) itemPageInfo.getTotal(), pageQuery.getPageSize(), pageQuery.getPageIndex());
  }

  @Override
  public void notifyBuyerOrQcChange(Long itemId) {
    final Item item = itemService.getById(itemId);
    if (item == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND.getCode(), "商品不存在");
    }
    if (StringUtil.isBlank(item.getPartnerProviderItemSn())) {
      return;
    }
    final ItemProcurement itemProcurement = iItemProcurementService.getByItemId(itemId);
    if (itemProcurement == null) {
      throw ExceptionPlusFactory.bizException(ErrorCode.DATA_NOT_FOUND.getCode(), "商品采购设置记录不存在");
    }
    PartnerSyncChargePersonReq req = new PartnerSyncChargePersonReq();
    req.setItemNo(item.getPartnerProviderItemSn());
    final List<Long> qcIdList =
        itemProcurement.getQcIds() != null
            ? Arrays.stream(itemProcurement.getQcIds().split(StrUtil.COMMA))
                .filter(StrUtil::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList())
            : Collections.emptyList();
    req.setQcIds(qcIdList);
    final Long buyerId = itemProcurement.getBuyerId();
    final Optional<Buyer> buyer = buyerGateway.buyer(buyerId);
    req.setPurchaseId(buyer.map(Buyer::getUserId).orElse(null));
    final PartnerSyncChargePersonReqWrapper reqWrapper =
        PartnerSyncChargePersonReqWrapper.of(ListUtil.of(req));
    final Rsp<Object> objectRsp = partnerFeignClient.notifyBuryOrQcChange(reqWrapper);
    if (!objectRsp.isSuccess()) {
      throw ExceptionPlusFactory.bizException(
          ErrorCode.GATEWAY_ERROR.getCode(), "P系统异常异常:" + objectRsp.getMsg());
    }
  }

  @Override
  public void importExcel(InputStream inputStream) {
    final List<ImportItemVO> importItemVoS =
        EasyExcel.read(inputStream).head(ImportItemVO.class).ignoreEmptyRow(true).doReadAllSync();
    if (CollUtil.isEmpty(importItemVoS)) {
      return;
    }
    int i = 0;
    for (ImportItemVO importItemVo : importItemVoS) {
      i++;
      final Item item = itemService.getByMixedCode(importItemVo.getItemCode());
      if (item == null) {
        log.warn("[后端商品导入]第{}行，商品不存在:{}", i, importItemVo.getItemCode());
        continue;
      }
      final Long itemId = item.getId();
      ArrayList<String> logs = new ArrayList<>();
      if (StrUtil.isNotBlank(importItemVo.getName())
          && !importItemVo.getName().equals(item.getName())) {
        item.setName(importItemVo.getName());
        logs.add("商品名称从'" + item.getName() + "'修改为'" + importItemVo.getName() + "'");
      }
      if (StrUtil.isNotBlank(importItemVo.getShortName())
          && !importItemVo.getShortName().equals(item.getShortName())) {
        item.setShortName(importItemVo.getShortName());
        logs.add("商品简称从'" + item.getShortName() + "'修改为'" + importItemVo.getShortName() + "'");
      }
      if (StrUtil.isNotBlank(importItemVo.getPartnerProviderItemSn())
          && !importItemVo.getPartnerProviderItemSn().equals(item.getPartnerProviderItemSn())) {
        item.setPartnerProviderItemSn(importItemVo.getPartnerProviderItemSn());
        logs.add(
            "P系统款号从'"
                + item.getPartnerProviderItemSn()
                + "'修改为'"
                + importItemVo.getPartnerProviderItemSn()
                + "'");
      }
      if (StrUtil.isNotBlank(importItemVo.getProviderSpecifiedCode())
          && !importItemVo.getProviderSpecifiedCode().equals(item.getProviderSpecifiedCode())) {
        item.setProviderSpecifiedCode(importItemVo.getProviderSpecifiedCode());
        logs.add(
            "供货指定编码从'"
                + item.getProviderSpecifiedCode()
                + "'修改为'"
                + importItemVo.getProviderSpecifiedCode()
                + "'");
      }
      if (!logs.isEmpty()) {
        itemService.updateById(item);
        operateLogDomainService.addOperatorLog(
            0L, OperateLogTarget.ITEM, itemId, "后端商品导入，" + String.join("、", logs));
      }
      if (StrUtil.isNotBlank(importItemVo.getCorpType())) {
        final List<CorpBizTypeDTO> corpBizType =
            bizLevelDivisionService.getCorpBizType(BizUnionTypeEnum.SPU, itemId);
        if (CollUtil.isEmpty(corpBizType)) {
          log.warn("[后端商品导入]商品合作模式不存在:{}", importItemVo.getItemCode());
          continue;
        }
        if (corpBizType.size() > 1) {
          log.warn("[后端商品导入]商品合作模式存在多个合作方:{}", importItemVo.getItemCode());
          continue;
        }
        final DivisionLevelValueEnum corpTypeEnum =
            DivisionLevelValueEnum.valueOfCorpTypeName(importItemVo.getCorpType());
        final ArrayList<CorpBizTypeDTO> newCorpBizType = new ArrayList<>();
        final CorpBizTypeDTO newCorpBizTypeDTO = new CorpBizTypeDTO();
        newCorpBizTypeDTO.setCorpType(corpTypeEnum.getValue());
        newCorpBizTypeDTO.setBizType(corpBizType.get(0).getBizType());
        newCorpBizType.add(newCorpBizTypeDTO);
        final boolean corpBizTypeChanged =
            bizLevelDivisionService.saveCascadedLevels(
                BizUnionTypeEnum.SPU,
                itemId,
                item.getSupplierCode(),
                BizLevelDivisionConvert.INSTANCE.corpBizTypeToCascadedDivisionLevels(
                    newCorpBizType),
                Arrays.asList(DivisionLevelEnum.COOPERATION, DivisionLevelEnum.BUSINESS_TYPE));
        if (corpBizTypeChanged) {
          operateLogDomainService.addOperatorLog(
              0L,
              OperateLogTarget.ITEM,
              itemId,
              String.format(
                  "后端商品导入，修改合作方从'%s'修改为'%s'",
                  BizLevelDivisionConvert.INSTANCE.corpBizTypeListToDescStr(corpBizType),
                  BizLevelDivisionConvert.INSTANCE.corpBizTypeListToDescStr(newCorpBizType)));
        }
        itemDomainService.syncCooperateMode(item);
      }
      if (StrUtil.isNotBlank(importItemVo.getBuyer())) {
        final StaffInfo buyerInfo = userGateway.queryStaffInfoByNickname(importItemVo.getBuyer());
        if (buyerInfo == null) {
          log.warn("[后端商品导入]第{}行，采购员信息异常，未查询到员工信息:{}", i, importItemVo.getBuyer());
          continue;
        }
        final Long buyerUserId = buyerInfo.getUserId();
        final Long buyerId = buyerGateway.saveOrUpdateBuyer(buyerUserId, buyerInfo.getNickname());
        final Buyer oldBuyer = buyerGateway.getByItemId(itemId);
        if (oldBuyer == null || !Objects.equals(buyerId, oldBuyer.getId())) {
          itemProcurementService.setBuyerId(itemId, buyerId);
          operateLogDomainService.addOperatorLog(
              0L,
              OperateLogTarget.ITEM,
              itemId,
              String.format(
                  "[后端商品导入]采购员从 %s 修改为 %s",
                  oldBuyer != null ? oldBuyer.getName() : "/", buyerInfo.getNickname()),
              new Object[] {
                new DiffUtil.ChangePropertyObj(
                    "buyerId", oldBuyer != null ? oldBuyer.getUserId() : 0L, buyerInfo.getUserId())
              });
          log.info("[后端商品导入]第{}行，商品 {} 采购员修改完成", i, itemId);
        } else {
          log.info("[后端商品导入]第{}行，商品 {} 采购员无需变更", i, itemId);
        }

        if ("是".equals(importItemVo.getSyncPsys())) {
          try {
            notifyBuyerOrQcChange(itemId);
            log.info("[后端商品导入]第{}行，商品 {} 采购员&QC修改同步到P系统成功", i, itemId);
          } catch (Exception e) {
            log.error("[后端商品导入]第{}行，商品 {} 采购员&QC修改同步到P系统失败", i, itemId, e);
          }
        }
      }

      if ("是".equals(importItemVo.getSyncKingDee())) {
        try {
          kingDeeTemplate.syncItem(itemId);
          log.info("[后端商品导入]第{}行，商品 {} 已同步至金蝶", i, itemId);
        } catch (Exception e) {
          log.error("[后端商品导入]第{}行，商品 {} 推送金蝶失败", i, itemId, e);
        }
      }

      if ("是".equals(importItemVo.getSyncWdt())) {
        try {
          itemSyncWdtBizService.syncItemToWdtManaged(itemId);
          log.info("[后端商品导入]第{}行，商品 {} 已同步至旺店通", i, itemId);
        } catch (Exception e) {
          log.error("[后端商品导入]第{}行，商品 {} 推送旺店通失败", i, itemId, e);
        }
      }
    }
  }
}
