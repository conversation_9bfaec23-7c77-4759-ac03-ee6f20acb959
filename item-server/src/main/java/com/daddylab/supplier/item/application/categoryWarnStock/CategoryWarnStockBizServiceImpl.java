package com.daddylab.supplier.item.application.categoryWarnStock;

import com.alibaba.excel.EasyExcel;
import com.daddylab.supplier.item.application.categoryWarnStock.dto.CategoryWarnStockImportDto;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryWarnStock;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryWarnStockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CategoryWarnStockBizServiceImpl implements CategoryWarnStockBizService {

    @Resource
    private ICategoryWarnStockService categoryWarnStockService;

    @Override
    public boolean importExcel(InputStream inputStream) {
        List<CategoryWarnStockImportDto> importItems = EasyExcel.read(inputStream)
                .headRowNumber(1)
                .head(CategoryWarnStockImportDto.class)
                .sheet()
                .doReadSync();

        if (importItems.isEmpty()) {
            throw new RuntimeException("导入Excel文件为空");
        }

        List<CategoryWarnStock> entities = importItems.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());

        return categoryWarnStockService.saveBatch(entities);
    }

    private CategoryWarnStock convertToEntity(CategoryWarnStockImportDto dto) {
        CategoryWarnStock entity = new CategoryWarnStock();
        entity.setCategoryId(dto.getCategoryId());
        entity.setWarnStock(dto.getWarnStock());
        entity.setWarehouseId(dto.getWarehouseId());
        return entity;
    }
}