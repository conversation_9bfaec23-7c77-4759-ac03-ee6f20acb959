package com.daddylab.supplier.item.application.categoryWarnStock;

import com.alibaba.excel.EasyExcel;
import com.daddylab.supplier.item.application.categoryWarnStock.dto.CategoryWarnStockImportDto;
import com.daddylab.supplier.item.domain.category.gateway.CategoryGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.Category;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CategoryWarnStock;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.ICategoryWarnStockService;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CategoryWarnStockBizServiceImpl implements CategoryWarnStockBizService {

  @Resource private ICategoryWarnStockService categoryWarnStockService;

  @Resource private CategoryGateway categoryGateway;

  @Override
  public boolean importExcel(InputStream inputStream) {
    List<CategoryWarnStockImportDto> importItems =
        EasyExcel.read(inputStream)
            .headRowNumber(1)
            .head(CategoryWarnStockImportDto.class)
            .sheet()
            .doReadSync();

    if (importItems.isEmpty()) {
      throw new RuntimeException("导入Excel文件为空");
    }

    List<CategoryWarnStock> entities =
        importItems.stream().map(this::convertToEntity).collect(Collectors.toList());

    return categoryWarnStockService.saveBatch(entities);
  }

  private CategoryWarnStock convertToEntity(CategoryWarnStockImportDto dto) {
    CategoryWarnStock entity = new CategoryWarnStock();

    // 根据分类名称查找分类ID
    Long categoryId =
        findCategoryIdByNames(
            dto.getFirstLevelCategory(), dto.getSecondLevelCategory(), dto.getThirdLevelCategory());

    if (categoryId == null) {
      throw new RuntimeException(
          String.format(
              "未找到匹配的分类：%s/%s/%s",
              dto.getFirstLevelCategory(),
              dto.getSecondLevelCategory(),
              dto.getThirdLevelCategory()));
    }

    entity.setCategoryId(categoryId);
    entity.setWarnStock(dto.getWarnStock());
    return entity;
  }

  /** 根据分类名称查找分类ID 优先级：三级分类 > 二级分类 > 一级分类 */
  private Long findCategoryIdByNames(String firstLevel, String secondLevel, String thirdLevel) {
    Category category =
        categoryGateway.getByPath(
            Strings.join(new String[] {firstLevel, secondLevel, thirdLevel}, "/"));
    if (category != null) {
      return category.getId();
    }
    return null;
  }
}
