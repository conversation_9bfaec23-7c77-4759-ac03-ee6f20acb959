<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.PlatformItemSkuMapper">
    <select id="statPlatformItemSku"
            resultType="com.daddylab.supplier.item.application.platformItem.model.PlatformItemSkuStat">
        SELECT IFNULL(COUNT(`id`), 0)                AS `skuNum`
             , IFNULL(SUM(`price`) / COUNT(`id`), 0) AS `avgPrice`
             , IFNULL(SUM(`stock`), 0)               AS `totalStockNum`
             , MAX(`modified`)                       AS `lastModified`
             , IFNULL(COUNT(IF(status = 1, 1, NULL)), 0) AS 'onSaleCount'
             , IFNULL(COUNT(IF(status = 0, 1, NULL)), 0) AS 'notOnSaleCount'
        FROM `platform_item_sku`
        WHERE `platform_item_id` = #{platformItemId}
          AND is_del = 0
    </select>

    <update id="updatePlatformItemStats">
        UPDATE IGNORE `platform_item` `pi` LEFT JOIN
        (SELECT `platform_item_id`
              , IFNULL(COUNT(`id`), 0)                      AS `skuNum`
              , IFNULL(SUM(`price`) / COUNT(`id`), 0)       AS `avgPrice`
              , IFNULL(SUM(`stock`), 0)                     AS `totalStockNum`
              , MAX(`modified`)                             AS `lastModified`
              , IFNULL(COUNT(IF(`status` = 1, 1, NULL)), 0) AS 'onSaleCount'
              , IFNULL(COUNT(IF(`status` = 0, 1, NULL)), 0) AS 'notOnSaleCount'
         FROM `platform_item_sku`
        WHERE
        is_del = 0
        GROUP BY platform_item_id) stats
            ON pi.id = stats.platform_item_id
        SET pi.price  = IFNULL(stats.avgPrice, 0),
        pi.stock      = IFNULL(stats.totalStockNum, 0),
        pi.modified   = stats.lastModified,
        pi.sku_num    = IFNULL(stats.skuNum, 0),
        pi.is_del     = IF(IFNULL(stats.skuNum, 0) = 0, 1, 0),
        pi.deleted_at = IF(IFNULL(stats.skuNum, 0) = 0, unix_timestamp() + pi.id, 0),
        pi.status     = IF(IFNULL(stats.onSaleCount, 0) > 0, 1, 2)
        WHERE pi.id IN
        <foreach item="platformItemId" index="index" collection="platformItemIds" open="(" separator="," close=")">
            #{platformItemId}
        </foreach>
        AND is_del = 0
    </update>


    <select id="selectInvalidSkuList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.PlatformItemSku">
        SELECT *
        FROM `platform_item_sku` WHERE is_del = 0
                                   AND `updated_at` <![CDATA[<]]> #{updateTime}
                                   AND (updated_at > #{timeOffset}
            OR (updated_at = #{timeOffset} AND id <![CDATA[>]]> #{idOffset}))
        <if test="shopNo != null and shopNo != ''">
            AND shop_no = #{shopNo}
        </if>
        ORDER BY updated_at, id
        LIMIT #{limit};
    </select>

    <select id="platformSkuQueryOfComposeSku"
            resultType="com.daddylab.supplier.item.types.platformItem.ComposeSkuPlatformItem">
        SELECT `ci`.`id`               AS `combination_id`,
               `ci`.`code`             AS `combination_code`,
               `cs`.`id`               AS `compose_sku_id`,
               `cs`.`sku_code`         AS `sku_code`,
               `cs`.`item_code`        AS `item_code`,
               `cs`.`sku_id`           AS `sku_id`,
               `cs`.`item_id`          AS `item_id`,
               `cs`.`count`            AS `num`,
               `pis`.`id`              AS `platform_item_sku_id`,
               `pis`.`outer_sku_id`    AS `outer_sku_id`,
               `pis`.`outer_item_id`   AS `outer_item_id`,
               `pis`.`outer_sku_code`  AS `outer_sku_code`,
               `pis`.`outer_item_code` AS `outer_item_code`,
               `pis`.`platform`        AS `platform`,
               `pis`.`shop_no`         AS `shop_no`
        FROM `combination_item` `ci`
            JOIN `compose_sku` `cs` ON `ci`.`id` = `cs`.`combination_id` AND `cs`.`is_del` = 0
            JOIN `platform_item_sku` `pis`
        ON `pis`.`outer_sku_code` = `cs`.`sku_code` AND `pis`.`is_del` = 0
        <if test="query.status != null">
            AND `pis`.`status` = #{query.status}
        </if>
        <if test="query.shopNo != null">
            AND `pis`.`shop_no` = #{query.shopNo}
        </if>
        WHERE `pis`.`is_del` = 0
          AND `pis`.`shop_no` IS NOT NULL
          AND `pis`.`outer_sku_code` != ''
        <if test="query.status != null">
            AND `pis`.`status` = #{query.status}
        </if>
        <if test="query.shopNo != null">
            AND `pis`.`shop_no` = #{query.shopNo}
        </if>
        <if test="query.outerSkuCodes != null">
            AND ci.code IN
            <foreach item="item" index="index" collection="query.outerSkuCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY ci.code, cs.id, pis.id
        ;
    </select>

    <select id="platformSkuQueryOfSameSkuCombinationItem"
            resultType="com.daddylab.supplier.item.types.platformItem.PlatformSkuOfSameSkuCombinationItem">
        SELECT `pis`.`platform`        AS `platform`,
               `pis`.`shop_no`         AS `shop_no`,
               `ci`.`id`               AS `combination_id`,
               `ci`.`code`             AS `combination_code`,
               `cs`.`id`               AS `compose_sku_id`,
               `cs`.`sku_code`         AS `sku_code`,
               `cs`.`item_code`        AS `item_code`,
               `cs`.`sku_id`           AS `sku_id`,
               `cs`.`item_id`          AS `item_id`,
               `cs`.`count`            AS `num`,
               `ci2`.`id`              AS `combination_id2`,
               `ci2`.`code`            AS `combination_code2`,
               `cs2`.`id`              AS `compose_sku_id2`,
               `cs2`.`sku_code`        AS `sku_code2`,
               `cs2`.`item_code`       AS `item_code2`,
               `cs2`.`sku_id`          AS `sku_id2`,
               `cs2`.`item_id`         AS `item_id2`,
               `cs2`.`count`           AS `num2`,
               `pis`.`id`              AS `platform_item_sku_id`,
               `pis`.`outer_sku_id`    AS `outer_sku_id`,
               `pis`.`outer_item_id`   AS `outer_item_id`,
               `pis`.`outer_sku_code`  AS `outer_sku_code`,
               `pis`.`outer_item_code` AS `outer_item_code`
        FROM `combination_item` `ci`
            JOIN
        `compose_sku` `cs`
        ON `ci`.`id` = `cs`.`combination_id`
            AND `cs`.`is_del` = 0
            LEFT JOIN `compose_sku` `cs2`
                      ON `cs2`.`sku_code` = `cs`.`sku_code` AND `cs2`.`is_del` = 0 AND `cs2`.`id` != `cs`.`id`
            LEFT JOIN `combination_item` `ci2`
                      ON `ci2`.`id` = `cs2`.`combination_id` AND `ci2`.`is_del` = 0 AND `ci2`.`id` != `ci`.`id`
            JOIN
        `platform_item_sku` `pis`
        ON `pis`.`outer_sku_code` = `ci2`.`code`
            AND `pis`.`is_del` = 0
        <if test="query.status != null">
            AND `pis`.`status` = #{query.status}
        </if>
        AND `pis`.`shop_no` = #{query.shopNo}
        WHERE `pis`.`is_del` = 0
          AND `pis`.`shop_no` IS NOT NULL
          AND `pis`.`outer_sku_code` != ''
          AND `pis`.`status` = 1
          AND `pis`.`shop_no` = #{query.shopNo}
        <if test="query.outerSkuCodes != null">
            AND ci.code IN
            <foreach item="item" index="index" collection="query.outerSkuCodes" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY `ci`.`code`,
                 `cs`.`id`,
                 `ci2`.`code`,
                 `cs2`.`id`,
                 `pis`.`id`
        ;
    </select>
    <select id="listOuterSkuCode" resultType="java.lang.String">
        select distinct pis.outer_sku_code from platform_item_sku pis
        where pis.is_del = 0 and pis.outer_sku_code != ''
        <if test="query.platformItemSkuStatus != null and query.platformItemSkuStatus.size() > 0">
            and pis.status in
            <foreach item="item" index="index" collection="query.platformItemSkuStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.shopNos != null and query.shopNos.size() > 0">
            and pis.shop_no in
            <foreach item="item" index="index" collection="query.shopNos" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ;
    </select>
</mapper>
