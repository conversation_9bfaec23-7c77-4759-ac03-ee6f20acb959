<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.ItemDrawerPlatformItemMapper">

    <select id="selectByDrawerId" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerPlatformLink">
        SELECT *
        FROM item_drawer_platform_link
        WHERE drawer_id = #{drawerId}
          AND is_del = 0
        ORDER BY platform
    </select>

    <select id="selectByDrawerIdAndPlatform" resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemDrawerPlatformLink">
        SELECT *
        FROM item_drawer_platform_link
        WHERE drawer_id = #{drawerId}
          AND platform = #{platform}
          AND is_del = 0
        LIMIT 1
    </select>

</mapper>
