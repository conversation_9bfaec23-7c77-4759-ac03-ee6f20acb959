<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.OtherOutboundOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OtherOutboundOrder">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="type" property="type" />
        <result column="business_type" property="businessType" />
        <result column="is_marketing_demand" property="isMarketingDemand" />
        <result column="applicant_department" property="applicantDepartment" />
        <result column="sample_purpose" property="samplePurpose" />
        <result column="channel" property="channel" />
        <result column="cost_bearing_department" property="costBearingDepartment" />
        <result column="has_gift" property="hasGift" />
        <result column="purpose" property="purpose" />
        <result column="consignee" property="consignee" />
        <result column="shipping_address" property="shippingAddress" />
        <result column="contact_mobile" property="contactMobile" />
        <result column="detailed_address" property="detailedAddress" />
        <result column="status" property="status" />
        <result column="total_amount" property="totalAmount" />
        <result column="remark" property="remark" />
        <result column="created_at" property="createdAt" />
        <result column="created_uid" property="createdUid" />
        <result column="updated_at" property="updatedAt" />
        <result column="updated_uid" property="updatedUid" />
        <result column="is_del" property="isDel" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_no, type, business_type, is_marketing_demand, applicant_department, sample_purpose, channel, cost_bearing_department, has_gift, purpose, consignee, shipping_address, contact_mobile, detailed_address, status, total_amount, remark, created_at, created_uid, updated_at, updated_uid, is_del
    </sql>

</mapper>
