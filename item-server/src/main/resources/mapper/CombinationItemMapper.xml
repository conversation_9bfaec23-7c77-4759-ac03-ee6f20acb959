<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.mapper.CombinationItemMapper">
    <!--    组合商品组成，sku明细列表-->
    <select id="listSkuDetail"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ComposeSkuDO">
        select
        s.id AS skuId,
        s.sku_code AS skuCode,
        s.provider_specified_code as specialSkuCode,
        s.bar_code AS barCode,
        s.tax_rate as taxRate,
        s.warehouse_no as warehouseNo,
        s.unit as unit,
        ware.name as warehouseName,
        i.is_gift as isGift,
        i.id AS itemId,
        i.CODE AS itemCode,
        i.NAME AS itemName,
        i.status as status,
        ifnull(c.path,'') AS category,
        ifnull(b.NAME,'') AS brandName,
        ifnull(s.cost_price,0) AS procurement,
        ifnull(s.sale_price,0) AS sales,
        ifnull( sto.stock, 0 ) AS stockCount,
        img.image_url as itemImage,
        s.`platform_commission` as platformCommission,
        s.`contract_sale_price` as contractSalePrice,
        s.cost_price as costPrice,
        s.specifications as specifications
        from item_sku as s
        INNER JOIN (
        select
        is_gift,id,`code`,`name`,category_id,brand_id,status from item
        where is_del = 0
        <if test="null != query ">
            <if test="0 != query.providerId and null != query.providerId ">
                and provider_id = #{query.providerId}
            </if>
            <if test="null != query.itemStatus and query.itemStatus.size() > 0 ">
                and status in
                <foreach collection="query.itemStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </if>
        ) as i on s.item_id = i.id
        left join (select id,path from category where is_del = 0) as c on i.category_id = c.id
        left join (select id,name from brand where is_del = 0 ) as b on i.brand_id = b.id
        left join (select sku_id,stock from item_stock where is_del = 0) sto on sto.sku_id = s.id
        left join (select image_url,item_id from item_image where is_main = 1 and type = 1 and is_del = 0) as img on
        i.id = img.item_id
        left join warehouse ware on ware.no = s.warehouse_no
        <where>
            s.is_del = 0
            <if test=" skuIdList != null and skuIdList.size()>0 ">
                and s.id in
                <foreach collection="skuIdList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test=" skuCodeList != null and skuCodeList.size()>0 ">
                and (
                s.sku_code in
                <foreach collection="skuCodeList" item="skuCode" open="(" separator="," close=")">
                    #{skuCode}
                </foreach>
                or s.provider_specified_code in
                <foreach collection="skuCodeList" item="skuCode" open="(" separator="," close=")">
                    #{skuCode}
                </foreach>
                )
            </if>
            <if test="null != query ">
                <if test="'' != query.skuCode and null != query.skuCode ">
                    and( s.sku_code = #{query.skuCode} or s.provider_specified_code = #{query.skuCode} )
                </if>
                <if test="'' != query.itemId and null != query.itemId and 0 != query.itemId">
                    and i.id = #{query.itemId}
                </if>
                <if test="'' != query.itemCode and null != query.itemCode ">
                    and i.code = #{query.itemCode}
                </if>
                <if test="'' != query.categoryId and null != query.categoryId and 0 != query.categoryId">
                    and c.id = #{query.categoryId}
                </if>
                <if test="'' != query.brandId and null != query.brandId and 0 != query.brandId">
                    and b.id = #{query.brandId}
                </if>
            </if>
            <if test="query.bizType != null and query.bizType.size()>0">
                AND EXISTS(
                    SELECT 1
                    FROM `biz_level_division`
                    WHERE
                        `is_del` = 0 AND `type` = 0 AND `level` = 1
                        AND `level_val` IN
                        <foreach item="item" index="index" collection="query.bizType" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        AND `biz_id` = `i`.`id`
                )
            </if>
        </where>
        order by s.id desc
        <if test="null != query ">
            limit #{query.offset},#{query.pageSize}
        </if>
    </select>
    <select id="countSkuDetail" resultType="java.lang.Integer">
        select
        count(1)
        from item_sku as s
        inner join (
        select
        is_gift,id,`code`,`name`,category_id,brand_id from item
        where is_del = 0
        <if test="'' != query.providerId and null != query.providerId ">
            and provider_id = #{query.providerId}
        </if>
        <if test="null != query.itemStatus and query.itemStatus.size() > 0 ">
            and status in
            <foreach collection="query.itemStatus" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ) as i on s.item_id = i.id
        left join (select id,path from category where is_del = 0) as c on i.category_id = c.id
        left join (select id,name from brand where is_del = 0 ) as b on i.brand_id = b.id
        left join (select sku_id,stock from item_stock where is_del = 0) sto on sto.sku_id = s.id
        left join (select image_url,item_id from item_image where is_main = 1 and type = 1 and is_del = 0) as img on
        i.id = img.item_id
        <where>
            s.is_del = 0
            <if test=" skuIdList != null and skuIdList.size()>0 ">
                and s.id in
                <foreach collection="skuIdList" item="skuId" open="(" separator="," close=")">
                    #{skuId}
                </foreach>
            </if>
            <if test=" skuCodeList != null and skuCodeList.size()>0 ">
                and s.sku_code in
                <foreach collection="skuCodeList" item="skuCode" open="(" separator="," close=")">
                    #{skuCode}
                </foreach>
            </if>
            <if test="null != query ">
                <if test="'' != query.skuCode and null != query.skuCode ">
                    and s.sku_code = #{query.skuCode}
                </if>
                <if test="'' != query.itemId and null != query.itemId and 0 != query.itemId">
                    and i.id = #{query.itemId}
                </if>
                <if test="'' != query.itemCode and null != query.itemCode ">
                    and i.code = #{query.itemCode}
                </if>
                <if test="'' != query.categoryId and null != query.categoryId and 0 != query.categoryId">
                    and c.id = #{query.categoryId}
                </if>
                <if test="'' != query.brandId and null != query.brandId and 0 != query.brandId">
                    and b.id = #{query.brandId}
                </if>
            </if>
        </where>
    </select>

    <!--    分页查询组合商品-->
    <select id="listItem"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationDO">
        select
        id,code,name,type,created_at,created_uid,business_line
        from combination_item
        <where>
            is_del = 0
            <if test=" '' != query.id and null != query.id and 0 != query.id">
                and id = #{query.id}
            </if>
            <if test=" '' != query.code and null != query.code ">
                and code = #{query.code}
            </if>
            <if test=" '' != query.name and null != query.name ">
                and `name` LIKE concat('%', #{query.name}, '%')
            </if>
            <if test=" null != query.type ">
                and type = #{query.type.value}
            </if>
            <if test=" '' != query.startTime and null != query.startTime and 0 != query.startTime
                       and '' != query.endTime and null != query.endTime and 0 != query.endTime ">
                and (created_at between #{query.startTime} and #{query.endTime} )
            </if>
<!--            <if test="query.businessLine != null and query.businessLine.size() > 0">-->
<!--                AND-->
<!--                <foreach item="lineId" index="index" collection="query.businessLine" open="(" separator="OR" close=")">-->
<!--                    is_business_line${lineId} = 1-->
<!--                </foreach>-->
<!--            </if>-->
            <if test="query.corpType != null and query.corpType.size() != 0">
                AND EXISTS(
                SELECT 1
                FROM `biz_level_division` bld
                WHERE bld.`is_del` = 0
                AND bld.`type` = 1
                AND bld.`level` = 0
                AND `level_val` IN
                <foreach item="item" index="index" collection="query.corpType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and bld.biz_id = combination_item.id
                )
            </if>
            <if test="query.bizType != null and query.bizType.size() != 0">
                AND EXISTS(
                SELECT 1
                FROM `biz_level_division` bld
                WHERE bld.`is_del` = 0
                AND bld.`type` = 1
                AND bld.`level` = 1
                AND `level_val` IN
                <foreach item="item" index="index" collection="query.bizType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and bld.biz_id = combination_item.id
                )
            </if>
            <if test=" ('' != query.itemId and null != query.itemId and 0 != query.itemId)
                    or ('' != query.itemCode and null != query.itemCode)
                    or ('' != query.skuCode and null != query.skuCode)">
                and id in
                (
                select distinct combination_id from compose_sku
                <where>
                    is_del = 0
                    <if test=" '' != query.itemId and null != query.itemId and 0 != query.itemId">
                        and item_id = #{query.itemId}
                    </if>
                    <if test=" '' != query.itemCode and null != query.itemCode ">
                        and item_code = #{query.itemCode}
                    </if>
                    <if test=" query.skuCodes != null and query.skuCodes.size()>0 ">
                        and sku_code in
                        <foreach collection="query.skuCodes" item="code" open="(" separator="," close=")">
                            #{code}
                        </foreach>
                    </if>
                </where>
                )
            </if>
        </where>
        order by created_at desc
        limit #{query.offset},#{query.pageSize}
    </select>
    <select id="countListItem" resultType="java.lang.Integer">
        select count(1)
        from combination_item
        <where>
            is_del = 0
            <if test="'' != query.id and null != query.id and 0 != query.id">
                and id = #{query.id}
            </if>
            <if test="'' != query.code and null != query.code">
                and code = #{query.code}
            </if>
            <if test="'' != query.name and null != query.name">
                and `name` LIKE concat('%', #{query.name}, '%')
            </if>
            <if test="null != query.type">
                and type = #{query.type.value}
            </if>
            <if test="'' != query.startTime and null != query.startTime and 0 != query.startTime
            and '' != query.endTime and null != query.endTime and 0 != query.endTime">
                and (created_at between #{query.startTime} and #{query.endTime})
            </if>
            <if test="('' != query.itemId and null != query.itemId and 0 != query.itemId)
            or ('' != query.itemCode and null != query.itemCode)
            or ('' != query.skuCode and null != query.skuCode)">
                and id in
                (
                select distinct combination_id
                from compose_sku
                <where>
                    is_del = 0
                    <if test="'' != query.itemId and null != query.itemId and 0 != query.itemId">
                        and item_id = #{query.itemId}
                    </if>
                    <if test="'' != query.itemCode and null != query.itemCode">
                        and item_code = #{query.itemCode}
                    </if>
                    <if test="'' != query.skuCode and null != query.skuCode">
                        and sku_code = #{query.skuCode}
                    </if>
                </where>
                )
            </if>
<!--            <if test="query.businessLine != null and query.businessLine.size() > 0">-->
<!--                and business_line in-->
<!--                <foreach collection="query.businessLine" item="bl" open="(" separator="," close=")">-->
<!--                    #{bl}-->
<!--                </foreach>-->
<!--            </if>-->
            <if test="query.corpType != null and query.corpType.size() != 0">
                AND EXISTS(
                SELECT 1
                FROM `biz_level_division` bld
                WHERE bld.`is_del` = 0
                  AND bld.`type` = 1
                  AND bld.`level` = 0
                  AND `level_val` IN
                <foreach item="item" index="index" collection="query.corpType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and bld.biz_id = combination_item.id
                )
            </if>
            <if test="query.bizType != null and query.bizType.size() != 0">
                AND EXISTS(
                SELECT 1
                FROM `biz_level_division` bld
                WHERE bld.`is_del` = 0
                  AND bld.`type` = 1
                  AND bld.`level` = 1
                  AND `level_val` IN
                <foreach item="item" index="index" collection="query.bizType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and bld.biz_id = combination_item.id
                )
            </if>
        </where>
    </select>

    <!--    protected String combinationName;-->
    <!--    protected String combinationCode;-->
    <!--    protected String combinationBarCode;-->
    <!--    protected BigDecimal combinationWeight;-->
    <!--    protected String itemCode;-->
    <!--    protected String skuCode;-->
    <!--    protected String count;-->
    <!--    protected String stockCount;-->
    <!--    protected BigDecimal itemCostPrice;-->
    <!--    protected BigDecimal itemSalesPrice;-->
    <!--    protected BigDecimal skuCostPrice;-->
    <!--    protected BigDecimal skuSalesPrice;-->

    <!--    @ApiModelProperty("组合商品id")-->
    <!--    private Long id;-->
    <!--    @ApiModelProperty("组合商品编码")-->
    <!--    private String code;-->
    <!--    @ApiModelProperty("单品商品id")-->
    <!--    private Long itemId;-->
    <!--    @ApiModelProperty("单品商品编码")-->
    <!--    private String itemCode;-->
    <!--    @ApiModelProperty("单品商品skuCode")-->
    <!--    private String skuCode;-->
    <!--    @ApiModelProperty("属性")-->
    <!--    private CombinationItemType type;-->
    <!--    @ApiModelProperty("创建开始时间")-->
    <!--    private Long startTime;-->
    <!--    @ApiModelProperty("创建结束时间")-->
    <!--    private Long endTime;-->

    <!--    导出查询-->
    <select id="queryExport"
            resultType="com.daddylab.supplier.item.domain.exportTask.dto.combinationItem.ComposerSkuAllSheet">
        select
        ci.id as combinationId,
        ci.name as combinationName,
        ci.code as combinationCode,
        ci.bar_code as combinationBarCode,
        ci.weight as combinationWeight,
        ci.procurement_price as itemProcurementPrice,
        ci.sales_price as itemSalesPrice,
        cs.item_code as itemCode,
        cs.sku_code as skuCode,
        cs.count as count,
        ifnull(stock.stock,0) as stockCount,
        ifnull(sku.cost_price,0) as skuCostPrice,
        ifnull(sku.sale_price,0) as skuSalesPrice,
        cs.cost_proportion,
        cs.sale_proportion,
        ci.business_line,
        ci.platform_commission as platformCommission,
        ci.`contract_sale_price` as contractSalePrice,
        sku.platform_commission as skuPlatformCommission,
        sku.`contract_sale_price` as skuContractSalePrice
        from compose_sku cs
        left join combination_item ci on cs.combination_id = ci.id
        left join (select id,cost_price,sale_price,`platform_commission`,`contract_sale_price` from item_sku where
        is_del = 0) sku on cs.sku_id = sku.id
        left join ( SELECT sku_id, stock FROM item_stock WHERE is_del = 0 ) stock on cs.sku_id = stock.sku_id
        <where>
            cs.is_del = 0 and ci.is_del = 0
            <if test=" '' != query.id and null != query.id and 0 != query.id ">
                and ci.id = #{query.id}
            </if>
            <if test=" '' != query.code and null != query.code ">
                and ci.code = #{query.code}
            </if>
            <if test=" '' != query.name and null != query.name ">
                and `name` LIKE concat('%', #{query.name}, '%')
            </if>
            <if test=" null != query.type">
                and ci.type = #{query.type.value}
            </if>
            <if test=" '' != query.startTime and null != query.startTime and 0 != query.startTime
                   and '' != query.endTime and null != query.endTime and 0 != query.endTime ">
                and (cs.created_at between #{query.startTime} and #{query.endTime} )
            </if>
            <if test=" '' != query.itemId and null != query.itemId and 0 != query.itemId ">
                and cs.item_id = #{query.itemId}
            </if>
            <if test=" '' != query.itemCode and null != query.itemCode ">
                and cs.item_code = #{query.itemCode}
            </if>
            <if test=" '' != query.skuCode and null != query.skuCode ">
                and cs.sku_code = #{query.skuCode}
            </if>
<!--            <if test=" null != query.businessLine and query.businessLine.size() > 0">-->
<!--                and ci.business_line in-->
<!--                <foreach collection="query.businessLine" item="bl" open="(" separator="," close=")">-->
<!--                    #{bl}-->
<!--                </foreach>-->
<!--            </if>-->
            <if test="query.corpType != null and query.corpType.size() != 0">
                AND EXISTS(
                SELECT 1
                FROM `biz_level_division` bld
                WHERE bld.`is_del` = 0
                AND bld.`type` = 1
                AND bld.`level` = 0
                AND `level_val` IN
                <foreach item="item" index="index" collection="query.corpType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and bld.biz_id = ci.id
                )
            </if>
            <if test="query.bizType != null and query.bizType.size() != 0">
                AND EXISTS(
                SELECT 1
                FROM `biz_level_division` bld
                WHERE bld.`is_del` = 0
                AND bld.`type` = 1
                AND bld.`level` = 1
                AND `level_val` IN
                <foreach item="item" index="index" collection="query.bizType" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and bld.biz_id = ci.id
                )
            </if>
        </where>
        order by ci.created_at asc
        limit #{query.offsetVal},#{query.pageSize}
    </select>
    <select id="getProcurement" resultType="java.math.BigDecimal">
    </select>

    <select id="queryProviderList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationProviderDO">
        select
        citem.code as itemCode ,group_concat(ii.provider_id) as providerIdList
        from combination_item citem inner join
        compose_sku sku on citem.id = sku.combination_id
        inner join item ii on sku.item_id = ii.id
        where citem.code in
        <foreach collection="codeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        group by citem.code
    </select>

    <select id="queryNameList"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.CombinationNameDO">
        select name,code from combination_item
        where is_del = 0 and code in
        <foreach collection="codeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="selectSameGroupSkuBySkuCode"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.custom.ComposeSkuWithPriceDO">
        select tmp.sku_code,
        tmp.count,
        tmp.combination_id,
        tmp.cost_proportion,
        tmp.sale_proportion,
        if(sku.cost_price is null,sku2.cost_price,sku.cost_price) as costPrice,
        if(sku.sale_price is null,sku2.sale_price,sku.sale_price) as salePrice
        from compose_sku tmp
        left join item_sku sku on tmp.sku_code = sku.sku_code
        left join item_sku sku2 on tmp.sku_code = sku2.provider_specified_code
        where tmp.combination_id in (
        select combination_id
        from compose_sku
        where sku_code in
        <foreach collection="skuCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and is_del = 0
        )
        and tmp.is_del = 0;
    </select>

    <select id="queryByCodeIncludeDeleted"
            resultType="com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.CombinationItem">
        SELECT *
        FROM `combination_item`
        WHERE `code` = #{suiteNo}
    </select>

    <select id="getComposeSkuProviderIds" resultType="java.lang.Long">
        select distinct pro.id
        from compose_sku csku
                 inner join item item on csku.item_id = item.id
                 inner join provider pro on item.provider_id = pro.id
        where csku.combination_id = (select id
                                     from combination_item
                                     where code = #{combinationItemCode})
          and csku.is_del = 0
          and item.is_del = 0
          and pro.is_del = 0
    </select>

    <select id="skuBelongToCombination" resultType="java.lang.Integer">
        select count(1)
        from compose_sku
        where sku_id = #{skuId}
          and combination_id = #{combinationItemId}
          and is_del = 0;
    </select>
</mapper>
