package com.daddylab.supplier;

import cn.hutool.core.util.URLUtil;
import com.daddylab.supplier.item.infrastructure.upyun.config.UpyunConfig;
import com.daddylab.supplier.item.infrastructure.upyun.gateway.MediaMeta;
import com.daddylab.supplier.item.infrastructure.upyun.gateway.UpyunGateway;
import com.daddylab.supplier.item.infrastructure.utils.FileUtil;
import java.util.List;
import java.util.Map;
import lombok.NonNull;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2022/6/3
 */
public class UpYunGatewayTest {

    @Test
    public void videoSnapshot() {
        final UpyunGateway upyunGateway = getUpyunGateway();
        final String videoFirstFrame = upyunGateway
                .getVideoFirstFrame("/Upload/supplier/item/加红糖-1654490167434.mp4");
        System.out.println(videoFirstFrame);

        final String videoSnap = upyunGateway
                .getVideoSnapshot("/Upload/supplier/item/加红糖-1654490167434.mp4",
                        "00:00:08");
        System.out.println(videoSnap);

        
    }

    public static void main(String[] args){
        String source = "/Upload/supplier/item/加红糖-1654490167434.mp4";
        String s = FileUtil.getPathDir(source) + FileUtil.getNameWithoutSuffix(source) + "-SNAP-" + "00:00:00"
                + ".jpg";
        System.out.println(s);
    }

    @NonNull
    private UpyunGateway getUpyunGateway() {
        final UpyunConfig upyunConfig = new UpyunConfig();
        upyunConfig.setBucketName("daddylab-test");
        upyunConfig.setOperatorName("test");
        upyunConfig.setOperatorPwd("dkylr0FQRCaoJpd256umPnd0O6amd4r4");
        upyunConfig.setUpyunBaseUrl("https://cdn-test.daddylab.com");
        upyunConfig.setHmacSha1Algorithm("HmacSHA1");
        upyunConfig.setEdAuto("http://p1.api.upyun.com");
        upyunConfig.setDefaultDir("/Upload/supplier/item/default/");
        upyunConfig.setPublicDir("/Upload/supplier/item/");

        final UpyunGateway upyunGateway = new UpyunGateway(upyunConfig);
        return upyunGateway;
    }

    @Test
    public void fileInfoTest() {
        final UpyunGateway upyunGateway = getUpyunGateway();
        String url = "https://cdn.daddylab.com/Upload/supplier/item/image/短柄训练叉勺 (0)-1665287971212.jpg";
        url = URLUtil.encode(URLUtil.decode(url));
        System.out.println(url);
        final MediaMeta mediaMeta = upyunGateway.getMediaMeta(
                url);
        System.out.println(mediaMeta);
        final Map<String, List<String>> fileInfo = upyunGateway.getFileInfo(url);
        System.out.println(fileInfo);
    }


}
