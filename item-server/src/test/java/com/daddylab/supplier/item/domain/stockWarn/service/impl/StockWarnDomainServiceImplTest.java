package com.daddylab.supplier.item.domain.stockWarn.service.impl;

import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnInfo;
import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnSummary;
import com.daddylab.supplier.item.domain.auth.gateway.UserContextGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.email.EmailService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 库存告警领域服务单元测试
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@ExtendWith(MockitoExtension.class)
class StockWarnDomainServiceImplTest {

    @Mock
    private IWdtStockSpecService wdtStockSpecService;

    @Mock
    private ICategoryWarnStockService categoryWarnStockService;

    @Mock
    private IWarehouseService warehouseService;

    @Mock
    private ICategoryService categoryService;

    @Mock
    private IItemService itemService;

    @Mock
    private UserContextGateway userContextGateway;

    @Mock
    private IDadStaffService dadStaffService;

    @Mock
    private OssGateway ossGateway;

    @Mock
    private EmailService emailService;

    @InjectMocks
    private StockWarnDomainServiceImpl stockWarnDomainService;

    private List<CategoryWarnStock> categoryWarnStocks;
    private List<WdtStockSpec> stockSpecs;
    private List<Category> categories;
    private List<Warehouse> warehouses;
    private List<Item> items;
    private List<DadStaff> staffs;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        setupTestData();
    }

    /**
     * 测试检查库存告警 - 正常情况
     */
    @Test
    void testCheckStockWarn_Success() {
        // Given
        when(categoryWarnStockService.list()).thenReturn(categoryWarnStocks);
        when(wdtStockSpecService.list()).thenReturn(stockSpecs);
        when(categoryService.list()).thenReturn(categories);
        when(warehouseService.list()).thenReturn(warehouses);
        when(itemService.list()).thenReturn(items);

        // When
        List<StockWarnInfo> result = stockWarnDomainService.checkStockWarn();

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // 预期有2个告警商品

        // 验证第一个告警商品
        StockWarnInfo firstWarn = result.get(0);
        assertEquals("ITEM001", firstWarn.getItemCode());
        assertEquals("测试商品1", firstWarn.getItemName());
        assertEquals(1L, firstWarn.getCategoryId());
        assertEquals("电子产品", firstWarn.getCategoryName());
        assertEquals(new BigDecimal("5"), firstWarn.getCurrentStock());
        assertEquals(new BigDecimal("10"), firstWarn.getWarnStock());
        assertEquals(1L, firstWarn.getWarehouseId());
        assertEquals("主仓库", firstWarn.getWarehouseName());
        assertNotNull(firstWarn.getOrderPersonnelIds());
        assertEquals(1, firstWarn.getOrderPersonnelIds().size());
        assertEquals(1001L, firstWarn.getOrderPersonnelIds().get(0));

        // 验证第二个告警商品
        StockWarnInfo secondWarn = result.get(1);
        assertEquals("ITEM002", secondWarn.getItemCode());
        assertEquals("测试商品2", secondWarn.getItemName());
        assertEquals(2L, secondWarn.getCategoryId());
        assertEquals("服装", secondWarn.getCategoryName());
        assertEquals(new BigDecimal("3"), secondWarn.getCurrentStock());
        assertEquals(new BigDecimal("8"), secondWarn.getWarnStock());
        assertEquals(2L, secondWarn.getWarehouseId());
        assertEquals("分仓库", secondWarn.getWarehouseName());

        // 验证服务调用
        verify(categoryWarnStockService).list();
        verify(wdtStockSpecService).list();
        verify(categoryService).list();
        verify(warehouseService).list();
        verify(itemService).list();
    }

    /**
     * 测试检查库存告警 - 没有类目警戒库存配置
     */
    @Test
    void testCheckStockWarn_NoCategoryWarnStock() {
        // Given
        when(categoryWarnStockService.list()).thenReturn(Collections.emptyList());

        // When
        List<StockWarnInfo> result = stockWarnDomainService.checkStockWarn();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(categoryWarnStockService).list();
        verify(wdtStockSpecService, never()).list();
    }

    /**
     * 测试检查库存告警 - 没有库存数据
     */
    @Test
    void testCheckStockWarn_NoStockData() {
        // Given
        when(categoryWarnStockService.list()).thenReturn(categoryWarnStocks);
        when(wdtStockSpecService.list()).thenReturn(Collections.emptyList());

        // When
        List<StockWarnInfo> result = stockWarnDomainService.checkStockWarn();

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(categoryWarnStockService).list();
        verify(wdtStockSpecService).list();
    }

    /**
     * 测试按订单员汇总告警信息 - 正常情况
     */
    @Test
    void testSummarizeWarnByOrderPersonnel_Success() {
        // Given
        List<StockWarnInfo> warnInfos = createTestWarnInfos();
        when(dadStaffService.list()).thenReturn(staffs);

        // When
        List<StockWarnSummary> result = stockWarnDomainService.summarizeWarnByOrderPersonnel(warnInfos);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size()); // 预期有2个订单员

        // 验证第一个订单员汇总
        StockWarnSummary firstSummary = result.stream()
                .filter(s -> s.getOrderPersonnelId().equals(1001L))
                .findFirst().orElse(null);
        assertNotNull(firstSummary);
        assertEquals(1001L, firstSummary.getOrderPersonnelId());
        assertEquals("张三", firstSummary.getOrderPersonnelName());
        assertEquals("<EMAIL>", firstSummary.getOrderPersonnelEmail());
        assertEquals(1, firstSummary.getWarnItemCount());
        assertNotNull(firstSummary.getWarnItems());
        assertEquals(1, firstSummary.getWarnItems().size());

        // 验证第二个订单员汇总
        StockWarnSummary secondSummary = result.stream()
                .filter(s -> s.getOrderPersonnelId().equals(1002L))
                .findFirst().orElse(null);
        assertNotNull(secondSummary);
        assertEquals(1002L, secondSummary.getOrderPersonnelId());
        assertEquals("李四", secondSummary.getOrderPersonnelName());
        assertEquals("<EMAIL>", secondSummary.getOrderPersonnelEmail());
        assertEquals(1, secondSummary.getWarnItemCount());
        assertNotNull(secondSummary.getWarnItems());
        assertEquals(1, secondSummary.getWarnItems().size());

        verify(dadStaffService).list();
    }

    /**
     * 测试按订单员汇总告警信息 - 空告警列表
     */
    @Test
    void testSummarizeWarnByOrderPersonnel_EmptyWarnInfos() {
        // Given
        List<StockWarnInfo> emptyWarnInfos = Collections.emptyList();

        // When
        List<StockWarnSummary> result = stockWarnDomainService.summarizeWarnByOrderPersonnel(emptyWarnInfos);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dadStaffService, never()).list();
    }

    /**
     * 测试按订单员汇总告警信息 - 员工信息不存在
     */
    @Test
    void testSummarizeWarnByOrderPersonnel_StaffNotFound() {
        // Given
        List<StockWarnInfo> warnInfos = createTestWarnInfos();
        when(dadStaffService.list()).thenReturn(Collections.emptyList()); // 没有员工信息

        // When
        List<StockWarnSummary> result = stockWarnDomainService.summarizeWarnByOrderPersonnel(warnInfos);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证员工信息为默认值
        StockWarnSummary summary = result.get(0);
        assertEquals("未知", summary.getOrderPersonnelName());
        assertEquals("", summary.getOrderPersonnelEmail());
    }

    /**
     * 测试生成Excel文件并上传 - 正常情况
     */
    @Test
    void testGenerateAndUploadExcel_Success() {
        // Given
        StockWarnSummary summary = createTestSummary();
        String expectedOssUrl = "https://oss.example.com/stock-warn/张三_20250129_143000.xlsx";
        when(ossGateway.put(eq(false), anyString(), any(ByteArrayInputStream.class)))
                .thenReturn(expectedOssUrl);

        // When
        String result = stockWarnDomainService.generateAndUploadExcel(summary);

        // Then
        assertNotNull(result);
        assertEquals(expectedOssUrl, result);
        verify(ossGateway).put(eq(false), anyString(), any(ByteArrayInputStream.class));
    }

    /**
     * 测试生成Excel文件并上传 - 空汇总信息
     */
    @Test
    void testGenerateAndUploadExcel_EmptySummary() {
        // Given
        StockWarnSummary emptySummary = new StockWarnSummary();
        emptySummary.setWarnItems(Collections.emptyList());

        // When
        String result = stockWarnDomainService.generateAndUploadExcel(emptySummary);

        // Then
        assertNull(result);
        verify(ossGateway, never()).put(anyBoolean(), anyString(), any(ByteArrayInputStream.class));
    }

    /**
     * 测试生成Excel文件并上传 - 空告警商品列表
     */
    @Test
    void testGenerateAndUploadExcel_NullWarnItems() {
        // Given
        StockWarnSummary summary = new StockWarnSummary();
        summary.setWarnItems(null);

        // When
        String result = stockWarnDomainService.generateAndUploadExcel(summary);

        // Then
        assertNull(result);
        verify(ossGateway, never()).put(anyBoolean(), anyString(), any(ByteArrayInputStream.class));
    }

    /**
     * 测试发送告警邮件 - 正常情况
     */
    @Test
    void testSendWarnEmail_Success() {
        // Given
        StockWarnSummary summary = createTestSummary();
        String ossUrl = "https://oss.example.com/stock-warn/test.xlsx";
        doNothing().when(emailService).sendSimpleMail(anyString(), anyString(), anyString());

        // When
        assertDoesNotThrow(() -> stockWarnDomainService.sendWarnEmail(summary, ossUrl));

        // Then
        verify(emailService).sendSimpleMail(
                eq("<EMAIL>"),
                contains("仓库商品库存告警"),
                contains("仓库负责人")
        );
    }

    /**
     * 测试发送告警邮件 - 邮件发送异常
     */
    @Test
    void testSendWarnEmail_EmailException() {
        // Given
        StockWarnSummary summary = createTestSummary();
        String ossUrl = "https://oss.example.com/stock-warn/test.xlsx";
        doThrow(new RuntimeException("邮件发送失败")).when(emailService)
                .sendSimpleMail(anyString(), anyString(), anyString());

        // When & Then
        assertThrows(RuntimeException.class, () -> 
                stockWarnDomainService.sendWarnEmail(summary, ossUrl));
    }

    /**
     * 测试生成Excel文件并上传 - OSS上传异常
     */
    @Test
    void testGenerateAndUploadExcel_OssException() {
        // Given
        StockWarnSummary summary = createTestSummary();
        when(ossGateway.put(eq(false), anyString(), any(ByteArrayInputStream.class)))
                .thenThrow(new RuntimeException("OSS上传失败"));

        // When & Then
        assertThrows(RuntimeException.class, () -> 
                stockWarnDomainService.generateAndUploadExcel(summary));
    }

    /**
     * 设置测试数据
     */
    private void setupTestData() {
        // 类目警戒库存配置
        categoryWarnStocks = Arrays.asList(
                createCategoryWarnStock(1L, new BigDecimal("10")),
                createCategoryWarnStock(2L, new BigDecimal("8"))
        );

        // 旺店通库存数据
        stockSpecs = Arrays.asList(
                createWdtStockSpec("ITEM001", "测试商品1", "WH001", 1L, new BigDecimal("5")),
                createWdtStockSpec("ITEM002", "测试商品2", "WH002", 2L, new BigDecimal("3")),
                createWdtStockSpec("ITEM003", "测试商品3", "WH001", 1L, new BigDecimal("15")) // 库存充足，不应告警
        );

        // 类目信息
        categories = Arrays.asList(
                createCategory(1L, "电子产品"),
                createCategory(2L, "服装")
        );

        // 仓库信息
        warehouses = Arrays.asList(
                createWarehouse("WH001", "主仓库", "1001,1003"),
                createWarehouse("WH002", "分仓库", "1002")
        );

        // 商品信息
        items = Arrays.asList(
                createItem("ITEM001", 1L),
                createItem("ITEM002", 2L),
                createItem("ITEM003", 1L)
        );

        // 员工信息
        staffs = Arrays.asList(
                createStaff(1001L, "张三", "<EMAIL>"),
                createStaff(1002L, "李四", "<EMAIL>"),
                createStaff(1003L, "王五", "<EMAIL>")
        );
    }

    /**
     * 创建测试告警信息列表
     */
    private List<StockWarnInfo> createTestWarnInfos() {
        List<StockWarnInfo> warnInfos = new ArrayList<>();

        // 第一个告警商品
        StockWarnInfo warnInfo1 = new StockWarnInfo();
        warnInfo1.setItemCode("ITEM001");
        warnInfo1.setItemName("测试商品1");
        warnInfo1.setCategoryId(1L);
        warnInfo1.setCategoryName("电子产品");
        warnInfo1.setCurrentStock(new BigDecimal("5"));
        warnInfo1.setWarnStock(new BigDecimal("10"));
        warnInfo1.setWarehouseId(1L);
        warnInfo1.setWarehouseName("主仓库");
        warnInfo1.setOrderPersonnelIds(Arrays.asList(1001L));
        warnInfos.add(warnInfo1);

        // 第二个告警商品
        StockWarnInfo warnInfo2 = new StockWarnInfo();
        warnInfo2.setItemCode("ITEM002");
        warnInfo2.setItemName("测试商品2");
        warnInfo2.setCategoryId(2L);
        warnInfo2.setCategoryName("服装");
        warnInfo2.setCurrentStock(new BigDecimal("3"));
        warnInfo2.setWarnStock(new BigDecimal("8"));
        warnInfo2.setWarehouseId(2L);
        warnInfo2.setWarehouseName("分仓库");
        warnInfo2.setOrderPersonnelIds(Arrays.asList(1002L));
        warnInfos.add(warnInfo2);

        return warnInfos;
    }

    /**
     * 创建测试汇总信息
     */
    private StockWarnSummary createTestSummary() {
        StockWarnSummary summary = new StockWarnSummary();
        summary.setOrderPersonnelId(1001L);
        summary.setOrderPersonnelName("张三");
        summary.setOrderPersonnelEmail("<EMAIL>");
        summary.setWarnItems(createTestWarnInfos());
        summary.setWarnItemCount(2);
        return summary;
    }

    /**
     * 创建类目警戒库存配置
     */
    private CategoryWarnStock createCategoryWarnStock(Long categoryId, BigDecimal warnStock) {
        CategoryWarnStock categoryWarnStock = new CategoryWarnStock();
        categoryWarnStock.setCategoryId(categoryId);
        categoryWarnStock.setWarnStock(warnStock);
        return categoryWarnStock;
    }

    /**
     * 创建旺店通库存规格
     */
    private WdtStockSpec createWdtStockSpec(String goodsNo, String goodsName, String warehouseNo, 
                                          Long warehouseId, BigDecimal availableStock) {
        WdtStockSpec stockSpec = new WdtStockSpec();
        stockSpec.setGoodsNo(goodsNo);
        stockSpec.setGoodsName(goodsName);
        stockSpec.setWarehouseNo(warehouseNo);
        stockSpec.setWarehouseId(warehouseId);
        stockSpec.setAvailableStock(availableStock);
        stockSpec.setSpecNo(goodsNo + "_SPEC");
        return stockSpec;
    }

    /**
     * 创建类目
     */
    private Category createCategory(Long id, String name) {
        Category category = new Category();
        category.setId(id);
        category.setName(name);
        return category;
    }

    /**
     * 创建仓库
     */
    private Warehouse createWarehouse(String no, String name, String orderPersonnel) {
        Warehouse warehouse = new Warehouse();
        warehouse.setNo(no);
        warehouse.setName(name);
        warehouse.setOrderPersonnel(orderPersonnel);
        return warehouse;
    }

    /**
     * 创建商品
     */
    private Item createItem(String code, Long categoryId) {
        Item item = new Item();
        item.setCode(code);
        item.setCategoryId(categoryId);
        return item;
    }

    /**
     * 创建员工
     */
    private DadStaff createStaff(Long uid, String nickname, String email) {
        DadStaff staff = new DadStaff();
        staff.setUid(uid);
        staff.setNickname(nickname);
        staff.setEmail(email);
        return staff;
    }
}
