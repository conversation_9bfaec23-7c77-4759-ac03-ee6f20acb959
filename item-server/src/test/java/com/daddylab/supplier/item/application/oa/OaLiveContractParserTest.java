package com.daddylab.supplier.item.application.oa;

import com.daddylab.supplier.item.application.oa.types.OaLiveContractCallbackRequest;
import com.daddylab.supplier.item.application.oa.types.ParsedLiveContractData;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.LiveManage;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.LiveManageItem;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.Assert.*;

/**
 * OA直播合同解析器单元测试
 *
 * <AUTHOR>
 * @since 2025-09-22
 */
public class OaLiveContractParserTest {

  private OaLiveContractParser oaLiveContractParser;

  @Before
  public void setUp() {
    MockitoAnnotations.initMocks(this);
    this.oaLiveContractParser = new OaLiveContractParser(new LiveManageConfig());
  }

  @Test
  public void testParseCallbackData_Success() {
    // 准备测试数据
    OaLiveContractCallbackRequest request = createTestRequest();

    // 执行测试
    ParsedLiveContractData result = oaLiveContractParser.parseCallbackData(request);
    
    System.out.println(JsonUtil.toJson(result));

    // 验证结果
    assertNotNull("解析结果不应为空", result);
    assertTrue("解析结果应该有效", result.isValid());

    // 验证主表数据
    LiveManage liveManage = result.getLiveManage();
    assertNotNull("主表数据不应为空", liveManage);
    assertEquals("OA流程编号应正确", "NHT20250919001", liveManage.getOaProcessNo());
    assertEquals("公司名称应正确", "浙江新火原新材料科技有限公司", liveManage.getCompanyName());
    assertEquals("申请人OA ID应正确", "3814554395358285168", liveManage.getApplyOaId());

    // 验证明细数据
    List<LiveManageItem> items = result.getItems();
    assertNotNull("明细数据不应为空", items);
    assertEquals("明细数量应正确", 2, items.size());
    assertEquals("总商品数量应正确", 2, result.getItemCount());
  }

   /** 创建测试用的回调请求数据 */
   private OaLiveContractCallbackRequest createTestRequest() {
    String json =
        "{\n"
            + "\t\"contract_type3\": \"-6882978803156695876\",\n"
            + "\t\"amount\": \"[100.00]\",\n"
            + "\t\"agreement\": \"[测试2,测试1]\",\n"
            + "\t\"contract_type1\": \"-3247599047144420482\",\n"
            + "\t\"contract_type2\": \"6343187491232434609\",\n"
            + "\t\"end_time\": \"[Tue Sep 30 00:00:00 CST 2025]\",\n"
            + "\t\"apply_time\": \"2025-09-19 13:56:00.0\",\n"
            + "\t\"archive_time\": \"\",\n"
            + "\t\"item_name\": \"[OA,蓝湖]\",\n"
            + "\t\"apply_oaid\": \"3814554395358285168\",\n"
            + "\t\"offline_commission\": \"[0.5000,0.2000]\",\n"
            + "\t\"item_no\": \"[黄色,蓝色]\",\n"
            + "\t\"start_time\": \"[Mon Sep 01 00:00:00 CST 2025]\",\n"
            + "\t\"shop_link\": \"[https://oa.dlab.cn/,https://lanhuapp.com/]\",\n"
            + "\t\"online_commission\": \"[0.4000,0.1000]\",\n"
            + "\t\"company\": \"浙江新火原新材料科技有限公司\",\n"
            + "\t\"id\": \"NHT20250919001\",\n"
            + "\t\"live_date\": \"[2025-09-19,2025-09-12]\",\n"
            + "\t\"status\": \"1\",\n"
            + "\t\"live_price\": \"[0.6000,0.3000]\"\n"
            + "}";
     return JsonUtil.parse(json, OaLiveContractCallbackRequest.class);
   }
}
