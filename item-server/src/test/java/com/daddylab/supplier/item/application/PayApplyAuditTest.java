package com.daddylab.supplier.item.application;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.cola.dto.MultiResponse;
import com.daddylab.supplier.item.application.purchase.order.factory.priceEngine.PurchaseBillService;
import com.daddylab.supplier.item.application.purchasePayable.applyPayAudit.AuditManager;
import com.daddylab.supplier.item.application.purchasePayable.service.PurchasePayableBizService;
import com.daddylab.supplier.item.application.purchasePayable.vo.ApplyPayOrderRelationVO;
import com.daddylab.supplier.item.controller.test.DateScriptService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IPurchaseOrderService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IStockOutOrderService;
import com.daddylab.supplier.item.infrastructure.kingdee.ApiEnum;
import com.daddylab.supplier.item.infrastructure.kingdee.KingDeeTemplate;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqJsonUtil;
import com.daddylab.supplier.item.infrastructure.kingdee.util.ReqTemplate;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR> up
 * @date 2023年02月17日 3:03 PM
 */
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@Slf4j
public class PayApplyAuditTest {

    @Resource
    AuditManager auditManager;

    @Resource
    PurchaseBillService purchaseBillService;

//    @Resource
//    PurchaseBillService purchaseBillService;
//
//    @Test
//    public void test00(){
//        purchaseBillService.createBill("202301");
//    }

    @Resource
    ReqTemplate kingDeeReqTemplate;

    @Resource
    ReqJsonUtil kingDeeJsonUtil;

    @Resource
    PurchasePayableBizService purchasePayableBizService;

    @Resource
    DateScriptService scriptService;

    @Test
    public void test00(){
        System.out.println(scriptService.exportCombinationSkuPrice(true));
//        PricePageQuery query = new PricePageQuery();
//        query.setIsSingle(true);
//        PageResponse<PurchasePriceVo> purchasePriceVoPageResponse = purchaseCombinationPriceController.skuPricePage(query);
//        System.out.println(JsonUtil.toJson(purchasePriceVoPageResponse));
    }


    @Test
    public void test10() throws Exception {
        purchasePayableBizService.deleteApplyPay(ListUtil.of("YFSQ-1677749942"));

//        Map<String, Integer> salesStockOutMap = new HashMap<>(16);
//        salesStockOutMap.put("100005901", 1);

//        String jsonParam = kingDeeJsonUtil.saveSaleOutStock(salesStockOutMap);
//        log.info("json:{}",jsonParam);
//        kingDeeReqTemplate.save(jsonParam);

//        String jsonParam = kingDeeJsonUtil.saveSaleReturnStock(salesStockOutMap);
//        log.info("json:{}", jsonParam);
//        kingDeeReqTemplate.save(jsonParam);

    }

    @Test
    public void test9() {
        purchaseBillService.createBill("202211");
    }

    @Test
    public void test0() {
        MultiResponse<ApplyPayOrderRelationVO> res = purchasePayableBizService.queryPayOrderRelation("YF-RK00101704");
        System.out.println(JsonUtil.toJson(res));
    }

    @SneakyThrows
    @Test
    public void test() {
//        auditManager.createTask(1L, new BigDecimal(50000));

//        List<AuditProcessNodVo> auditStream = auditManager.getAuditStream(1L);
//        System.out.println(JsonUtil.toJson(auditStream));

//        SaveApplyOrderDto cmd = new SaveApplyOrderDto();
//        cmd.setFixedTotalAmount(new BigDecimal(80));
//        SaveApplyOrderDetailDto saveApplyOrderDetailDto = new SaveApplyOrderDetailDto();
//        saveApplyOrderDetailDto.setFixedQuantity(1);
//        saveApplyOrderDetailDto.setFixedTotalAmount(new BigDecimal(80));
//        saveApplyOrderDetailDto.setSkuCode("100014101");
//        saveApplyOrderDetailDto.setIsCustomize(1);
//
//        SaveApplyOrderDetailDto saveApplyOrderDetailDto2 = new SaveApplyOrderDetailDto();
//        saveApplyOrderDetailDto2.setFixedQuantity(1);
//        saveApplyOrderDetailDto2.setFixedTotalAmount(new BigDecimal(81));
//        saveApplyOrderDetailDto2.setSkuCode("100014101");
//        saveApplyOrderDetailDto2.setIsCustomize(1);
//
//        cmd.setDetailCmdList(ListUtil.of(saveApplyOrderDetailDto,saveApplyOrderDetailDto2));
//        cmd.setProviderId(5954L);
//        cmd.setProviderName("测试供应商0527");
//        cmd.setSourcePayTotalAmount(new BigDecimal(88));
//        purchasePayableBizService.saveApplyOrder(cmd);

        purchasePayableBizService.hedgeHandler(102L);

//        ApplyOrderQueryPage page = new ApplyOrderQueryPage();
//        page.setPageSize(1);
//        page.setPageSize(10);
//        purchasePayableBizService.queryApplyOrder(page);

//        AuditNodeCmd cmd = new AuditNodeCmd();
//        cmd.setNodeId(67040849417L);
//        cmd.setStatus(PayApplyAuditStatus.PASS);
//        cmd.setOpinion("手动同意01");
//        auditManager.auditNode(cmd, 8092845L);
//
//        System.in.read();
//        List<AuditProcessNodVo> auditStream = auditManager.getAuditStream(1L);
//        System.out.println(JsonUtil.toJson(auditStream));

//        auditManager.rollbackPreviousNode(37L, 8092845L);

//        auditManager.rollbackDedicatedNodes(1L, 1000731844L, 8092845L);

//        applyPayHandlerJob.handler(41L);

//        ApplyOrderQueryPage page = new ApplyOrderQueryPage();
//        page.setPageIndex(0);
//        page.setPageSize(10);
//        purchasePayableBizService.queryApplyOrder(page);
    }

    @Resource
    IPurchaseOrderService iPurchaseOrderService;


    @Resource
    IStockOutOrderService iStockOutOrderService;

//    @Test
//    public void test2() {
//        StockOutOrder stockOutOrder = iStockOutOrderService.getById(884L);
//        PurchaseOrder purchaseOrder = iPurchaseOrderService.getById(stockOutOrder.getPurchaseOrderId());
//        if (Objects.nonNull(purchaseOrder)) {
//            PurchasePayableCmd cmd = new PurchasePayableCmd();
//            cmd.setType(PurchaseTypeEnum.OUT_STOCK_PAYABLE.getValue());
//            cmd.setRelatedOrderId(stockOutOrder.getId());
//            cmd.setBuyerId(purchaseOrder.getBuyerUserId());
//            cmd.setCreatedUid(purchaseOrder.getCreatedUid());
//            purchasePayableBizService.create(cmd);
//        }
//    }


    // --------------

    @Resource
    KingDeeTemplate kingDeeTemplate;

    @Test
    public void test1() {
//        kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_IN_ORDER, 914L, "");

        kingDeeTemplate.handler(ApiEnum.SAVE_STOCK_OUT_ORDER, 66L, "");
    }

}
