package com.daddylab.supplier.item.infrastructure.third.qianchuan.impl;

import com.daddylab.supplier.item.domain.oauth.TokenRefreshResult;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OauthToken;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.OpenApp;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;

import static org.junit.jupiter.api.Assertions.*;

class QianChuanOAuthServiceImplTest {

  @org.junit.jupiter.api.Test
  void testRefreshToken() {
    OauthToken token = new OauthToken();
    token.setSubjectId("4064232375333911");
    token.setOpenAppId(4L);
    token.setPlatform(3);
    token.setType("QIANCHUAN");
    token.setAccessToken("4fcfe704b0efbedd9c0c11332c08a4d242595ab8");
    token.setExpiredAt(1758781231L);
    token.setRefreshToken("ec14a515cf18c653b1a5f0c9ea268167b2c67944");
    token.setRefreshTokenExpiredAt(1761286851L);
    token.setScopes("");
    token.setTokenData("");

    OpenApp openApp = new OpenApp();
    openApp.setAppId("1843491054448052");
    openApp.setAppName("老爸ERP");
    openApp.setAppKey("1843491054448052");
    openApp.setAppSecret("73aa3ef36e1d0188dc3dbcefbbc72115caad593c");

    TokenRefreshResult tokenRefreshResult =
        new QianChuanOAuthServiceImpl().refreshToken(openApp, token);
    System.out.println(JsonUtil.toJson(tokenRefreshResult));
  }
}
