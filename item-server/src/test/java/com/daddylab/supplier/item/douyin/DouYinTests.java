package com.daddylab.supplier.item.douyin;

import com.aliyun.tea.TeaException;
import com.douyin.openapi.client.Client;
import com.douyin.openapi.client.models.OauthAccessTokenRequest;
import com.douyin.openapi.client.models.OauthRenewRefreshTokenRequest;
import com.douyin.openapi.client.models.OauthRenewRefreshTokenResponse;
import com.douyin.openapi.credential.models.Config;

public class DouYinTests {
    
    public void test() throws Exception {
        try {
            Config config = new Config().setClientKey("tt******").setClientSecret("cbs***"); // 改成自己的app_id跟secret
            Client client = new Client(config);
            OauthRenewRefreshTokenRequest sdkRequest = new OauthRenewRefreshTokenRequest();
            sdkRequest.setClientKey("CcBcVfMt2S");
            sdkRequest.setRefreshToken("ANGHTWHjah");
            OauthRenewRefreshTokenResponse sdkResponse = client.OauthRenewRefreshToken(sdkRequest);
        } catch (TeaException e) {
            System.out.println(e.getMessage());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }
}
