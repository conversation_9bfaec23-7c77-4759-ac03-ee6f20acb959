package com.daddylab.supplier.item;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cola.dto.PageResponse;
import com.alibaba.cola.dto.SingleResponse;
import com.alibaba.fastjson.JSONObject;
import com.daddylab.supplier.item.application.item.combinationItem.CombinationItemBizService;
import com.daddylab.supplier.item.application.item.combinationItem.dto.vo.CombinationItemWithPriceVO;
import com.daddylab.supplier.item.application.itemSync.ThirdPlatformSyncBizService;
import com.daddylab.supplier.item.application.order.settlement.sys.SkuRefundDo;
import com.daddylab.supplier.item.application.purchase.order.factory.CommonCalculator;
import com.daddylab.supplier.item.application.saleItem.dto.DouDianSyncCmd;
import com.daddylab.supplier.item.application.saleItem.service.NewGoodsBizService;
import com.daddylab.supplier.item.application.saleItem.vo.ThirdSyncVO;
import com.daddylab.supplier.item.controller.item.dto.ItemAttrDto;
import com.daddylab.supplier.item.controller.item.dto.ThirdSyncPageQuery;
import com.daddylab.supplier.item.domain.item.gateway.ItemGateway;
import com.daddylab.supplier.item.infrastructure.doudian.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemAttr;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ItemSku;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.ThirdPlatformSyncLog;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IItemSkuService;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.IThirdPlatformSyncLogService;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.doudian.open.api.material_createFolder.MaterialCreateFolderRequest;
import com.doudian.open.api.material_createFolder.MaterialCreateFolderResponse;
import com.doudian.open.api.material_createFolder.param.MaterialCreateFolderParam;
import com.doudian.open.api.material_uploadImageSync.MaterialUploadImageSyncRequest;
import com.doudian.open.api.material_uploadImageSync.MaterialUploadImageSyncResponse;
import com.doudian.open.api.material_uploadImageSync.param.MaterialUploadImageSyncParam;
import com.doudian.open.api.product_addV2.ProductAddV2Request;
import com.doudian.open.api.product_addV2.ProductAddV2Response;
import com.doudian.open.api.product_addV2.param.ProductAddV2Param;
import com.doudian.open.api.product_detail.data.CategoryDetail;
import com.doudian.open.api.product_editV2.ProductEditV2Request;
import com.doudian.open.api.product_editV2.ProductEditV2Response;
import com.doudian.open.api.product_editV2.param.ProductEditV2Param;
import com.doudian.open.api.product_getProductUpdateRule.ProductGetProductUpdateRuleRequest;
import com.doudian.open.api.product_getProductUpdateRule.ProductGetProductUpdateRuleResponse;
import com.doudian.open.api.product_getProductUpdateRule.param.ProductGetProductUpdateRuleParam;
import com.doudian.open.api.product_qualificationConfig.data.ProductQualificationConfigData;
import com.doudian.open.api.shop_getShopCategory.ShopGetShopCategoryRequest;
import com.doudian.open.api.shop_getShopCategory.ShopGetShopCategoryResponse;
import com.doudian.open.api.shop_getShopCategory.param.ShopGetShopCategoryParam;
import com.doudian.open.api.sku_syncStockBatch.SkuSyncStockBatchRequest;
import com.doudian.open.api.sku_syncStockBatch.SkuSyncStockBatchResponse;
import com.doudian.open.api.sku_syncStockBatch.param.SkuSyncListItem;
import com.doudian.open.api.sku_syncStockBatch.param.SkuSyncStockBatchParam;
import com.doudian.open.core.AccessToken;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> up
 * @date 2022年09月19日 4:23 PM
 */
@SpringBootTest(classes = {com.daddylab.supplier.item.ItemApplication.class},
        properties = {
                "spring.profiles.active:test"})
//@ExtendWith(SpringExtension.class)
//@SpringBootTest(classes = ItemApplication.class)
//@ActiveProfiles("test")
@Slf4j
public class DouDianTest {


    @Test
    @DisplayName("并发控制测试")
    public void demoTest() {
        Semaphore semaphore = new Semaphore(2);
        CountDownLatch countDownLatch = new CountDownLatch(5);

        ThreadUtil.concurrencyTest(9, () -> {
            try {
                semaphore.acquire();
                String name = Thread.currentThread().getName();
                log.info("{} start", name);
                if (name.contains("4")) {
                    throw new InterruptedException();
                } else {
                    TimeUnit.SECONDS.sleep(2);
                }
                log.info("{} end", name);
                semaphore.release();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("-- InterruptedException --");
            } finally {
                countDownLatch.countDown();
            }
        });
        try {
            countDownLatch.await();
            log.info("全部完成");
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }


    }

    @Test
    @DisplayName("平台商品查询测试")
    public void testProductEdit() {

    }

    @Resource
    DouDianCommon common;

    /**
     * 商品创建失败: 单张详情图高度不超2000px
     * <p>
     * dy的半身裙测试
     * ID:3572499290137965152
     */
    @Test
    @DisplayName("新建商品demo")
    public void addProduct() {
        ProductAddV2Request request = new ProductAddV2Request();
        ProductAddV2Param param = request.getParam();
        param.setOuterProductId("erp003");
        param.setProductType(0L);
        param.setCategoryLeafId(20219L);
        //param.setProductFormat("货号|8888^上市年份季节|2018年秋季");
        param.setName("[erp测试003]xxx补水液");
        param.setRecommendRemark("这个商品很好啊");
        param.setPic("https://p3-aio.ecombdimg.com/obj/ecom-shop-material/ALSUvYM_m_2864bb6bc16646f71441e3af23072e7c_sx_26453_www200-200");
        param.setDescription("https://p3-aio.ecombdimg.com/obj/ecom-shop-material/ALSUvYM_m_2864bb6bc16646f71441e3af23072e7c_sx_26453_www200-200");
//        param.setPayType(1L);
//        param.setDeliveryMethod(7);
//        param.setCdfCategory("1");
        param.setReduceType(1L);
//        param.setAssocIds("1|2|3");
        param.setFreightId(0L);
//        param.setWeight(1000d);
//        param.setWeightUnit(1L);
        param.setDeliveryDelayDay(9999L);
        param.setPresellType(0L);
        param.setPresellDelay(9999L);
        param.setPresellEndTime("2022-10-10 18:54:27");
        param.setSupply7dayReturn(1L);
        param.setMobile("40012345");
        param.setCommit(false);
        param.setRemark("备注");
        param.setSpecName("颜色-时效");
        param.setSpecs("颜色|白色^发货时效|48小时内发货,5天内发货");

        String specPrices = "[{\"code\":\"Qq1008611\",\"outer_sku_id\":\"\",\"price\":1,\"spec_detail_name1\":\"白色\",\"spec_detail_name2\":\"48小时内发货\",\"step_stock_num\":0,\"stock_num\":1,\"supplier_id\":\"\"},{\"code\":\"Qq1008622\",\"outer_sku_id\":\"\",\"price\":1,\"spec_detail_name1\":\"白色\",\"spec_detail_name2\":\"5天内发货\",\"step_stock_num\":0,\"stock_num\":1,\"supplier_id\":\"\"}]";
        String specPic = "https://p3-aio.ecombdimg.com/obj/ecom-shop-material/v1_ALSUvYM_70630259408755264040798_16991a88a61d1a1f4ab8bc526fcb7762_sx_388998_www800-800";
        String productFormatNew = "{\"1467\":[{\"diy_type\":0,\"name\":\"59%-69%(含)\",\"value\":168368}],\"1551\":[{\"diy_type\":1,\"name\":\"超短裙\",\"value\":22064}],\"1687\":[{\"diy_type\":0,\"name\":\"我是品牌\",\"value\":0}],\"1869\":[{\"diy_type\":0,\"name\":\"条纹\",\"value\":34242}],\"1878\":[{\"diy_type\":1,\"name\":\"高腰\",\"value\":16521}],\"2592\":[{\"diy_type\":1,\"name\":\"甜美\",\"value\":23925}],\"785\":[{\"diy_type\":0,\"name\":\"涤纶\",\"value\":25708}],\"813\":[{\"diy_type\":1,\"name\":\"X型\",\"value\":8017}],\"981\":[{\"diy_type\":1,\"name\":\"蛋糕裙\",\"value\":22253}]}";

        param.setSpecPrices(specPrices);
        param.setProductFormatNew(productFormatNew);
        param.setSpecPic(specPic);
//        String s = "[\n" +
//                "    {\n" +
//                "        \"spec_detail_name1\": \"红色\",\n" +
//                "        \"spec_detail_name2\": \"S\",\n" +
//                "        \"spec_detail_name3\": \"\",\n" +
//                "        \"stock_num\": 11,\n" +
//                "        \"price\": 100,\n" +
//                "        \"code\": \"\",\n" +
//                "        \"step_stock_num\": 0,\n" +
//                "        \"supplier_id\": \"\",\n" +
//                "        \"outer_sku_id\": \"\",\n" +
//                "        \"delivery_infos\": [\n" +
//                "            {\n" +
//                "                \"info_type\": \"weight\",\n" +
//                "                \"info_value\": \"100\",\n" +
//                "                \"info_unit\": \"mg\"\n" +
//                "            }\n" +
//                "        ]\n" +
//                "    },\n" +
//                "    {\n" +
//                "        \"spec_detail_name1\": \"红色\",\n" +
//                "        \"spec_detail_name2\": \"M\",\n" +
//                "        \"spec_detail_name3\": \"\",\n" +
//                "        \"stock_num\": 22,\n" +
//                "        \"price\": 100,\n" +
//                "        \"code\": \"\",\n" +
//                "        \"step_stock_num\": 0,\n" +
//                "        \"supplier_id\": \"\",\n" +
//                "        \"outer_sku_id\": \"\",\n" +
//                "        \"delivery_infos\": [\n" +
//                "            {\n" +
//                "                \"info_type\": \"weight\",\n" +
//                "                \"info_value\": \"100\",\n" +
//                "                \"info_unit\": \"mg\"\n" +
//                "            }\n" +
//                "        ]\n" +
//                "    },\n" +
//                "    {\n" +
//                "        \"spec_detail_name1\": \"黑色\",\n" +
//                "        \"spec_detail_name2\": \"S\",\n" +
//                "        \"spec_detail_name3\": \"\",\n" +
//                "        \"stock_num\": 44,\n" +
//                "        \"price\": 100,\n" +
//                "        \"code\": \"\",\n" +
//                "        \"step_stock_num\": 0,\n" +
//                "        \"supplier_id\": \"\",\n" +
//                "        \"outer_sku_id\": \"\",\n" +
//                "        \"delivery_infos\": [\n" +
//                "            {\n" +
//                "                \"info_type\": \"weight\",\n" +
//                "                \"info_value\": \"100\",\n" +
//                "                \"info_unit\": \"mg\"\n" +
//                "            }\n" +
//                "        ]\n" +
//                "    },\n" +
//                "    {\n" +
//                "        \"spec_detail_name1\": \"黑色\",\n" +
//                "        \"spec_detail_name2\": \"M\",\n" +
//                "        \"spec_detail_name3\": \"\",\n" +
//                "        \"stock_num\": 55,\n" +
//                "        \"price\": 100,\n" +
//                "        \"code\": \"\",\n" +
//                "        \"step_stock_num\": 0,\n" +
//                "        \"supplier_id\": \"\",\n" +
//                "        \"outer_sku_id\": \"\",\n" +
//                "        \"delivery_infos\": [\n" +
//                "            {\n" +
//                "                \"info_type\": \"weight\",\n" +
//                "                \"info_value\": \"100\",\n" +
//                "                \"info_unit\": \"mg\"\n" +
//                "            }\n" +
//                "        ]\n" +
//                "    }\n" +
//                "]";
//        param.setSpecPrices(s);

//        param.setSpecPic("img_url,img_url,img_url");
//        param.setMaximumPerOrder(200L);
//        param.setLimitPerBuyer(1L);
//        param.setMinimumPerOrder(2L);
//
////        String s = "{\\\"405\\\":[{\\\"value\\\":27664,\\\"name\\\":\\\"复习资料\\\",\\\"diy_type\\\":0}],\\\"449\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"佚名\\\",\\\"diy_type\\\":0}],\\\"501\\\":[{\\\"value\\\":7310,\\\"name\\\":\\\"否\\\",\\\"diy_type\\\":0}],\\\"855\\\":[{\\\"value\\\":61683,\\\"name\\\":\\\"北京出版社\\\",\\\"diy_type\\\":0}],\\\"1088\\\":[{\\\"value\\\":407,\\\"name\\\":\\\"小学五年级\\\",\\\"diy_type\\\":0}],\\\"1319\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"1\\\",\\\"diy_type\\\":0}],\\\"1601\\\":[{\\\"value\\\":13911,\\\"name\\\":\\\"通用版\\\",\\\"diy_type\\\":0}],\\\"1618\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"9787218122861\\\",\\\"diy_type\\\":0}],\\\"1831\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"小学英语看图说话写话二年级\\\",\\\"diy_type\\\":0}],\\\"2000\\\":[{\\\"value\\\":34762,\\\"name\\\":\\\"无\\\",\\\"diy_type\\\":0}],\\\"2229\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"1\\\",\\\"diy_type\\\":0}],\\\"2763\\\":[{\\\"value\\\":25193,\\\"name\\\":\\\"英语\\\",\\\"diy_type\\\":0}],\\\"3271\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"1\\\",\\\"diy_type\\\":0}],\\\"3296\\\":[{\\\"value\\\":0,\\\"name\\\":\\\"16.80元\\\",\\\"diy_type\\\":0}]}";
////        param.setProductFormatNew(s);
//        param.setSpuId(14567L);
//        param.setAppointDeliveryDay(2L);
////        param.setThirdUrl("http://img.alicdn.com/xxxx");
////        param.setExtra("略");
////        param.setSrc("略");
        param.setStandardBrandId(596120136L);
//        param.setNeedCheckOut(true);
////        param.setCarVinCode("*****************");
//        param.setPresellConfigLevel(3L);
//        param.setNeedRechargeMode(true);
//        param.setAccountTemplateId("122112");
//        param.setPresellDeliveryType(1L);
////        param.setWhiteBackGroundPicUrl("http://aaaa");
////        param.setLongPicUrl("http://aaaa");
//        param.setSellChannel(ListUtil.of(0L));
//        param.setStartSaleType(0L);
//        param.setMaterialVideoId("vaaaa");
//        param.setPickupMethod("0");
//        param.setSizeInfoTemplateId(101L);
////        param.setSubstituteGoodsUrl("https://xxx.xxx.xxx");
//        param.setSaleChannelType("sameAsOffline");
//        param.setStoreId(12345L);
//        param.setMainProductId(3121213121212L);
//        param.setSaleLimitId(123L);
//        param.setNamePrefix("钛钢木质耳饰");
//        param.setReferencePrice(12300L);

//        param.setProductFormatNew(); = "{\"1467\":[{\"diy_type\":0,\"name\":\"59%-69%(含)\",\"value\":168368}],\"1551\":[{\"diy_type\":1,\"name\":\"超短裙\",\"value\":22064}],\"1687\":[{\"diy_type\":0,\"name\":\"我是品牌\",\"value\":0}],\"1869\":[{\"diy_type\":0,\"name\":\"条纹\",\"value\":34242}],\"1878\":[{\"diy_type\":1,\"name\":\"高腰\",\"value\":16521}],\"2592\":[{\"diy_type\":1,\"name\":\"甜美\",\"value\":23925}],\"785\":[{\"diy_type\":0,\"name\":\"涤纶\",\"value\":25708}],\"813\":[{\"diy_type\":1,\"name\":\"X型\",\"value\":8017}],\"981\":[{\"diy_type\":1,\"name\":\"蛋糕裙\",\"value\":22253}]}"

        ProductAddV2Response response = request.execute(common.getAccessToken());
        log.info("response:{}", JsonUtil.toJson(response));
        //

    }

    @Test
    @DisplayName("上传图片到抖店素材中心")
    public void uploadImage() {
        MaterialUploadImageSyncRequest request = new MaterialUploadImageSyncRequest();
        MaterialUploadImageSyncParam param = request.getParam();
        param.setFolderId("0");
        param.setUrl("https://cdn-test.daddylab.com/Upload/supplier/item/image/1WRf6xeeyBn324dcba14ea27cada03577891b238468b-1663662894817.jpeg");
        param.setMaterialName("erp测试logo.jpg");
        MaterialUploadImageSyncResponse response = request.execute(common.getAccessToken());
        log.info("response:{}", JsonUtil.toJson(response));
        // {"data":{"materialId":"71456627402360425070798","folderId":"0","isNew":true,"auditStatus":1,"byteUrl":"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/ALSUvYM_m_2864bb6bc16646f71441e3af23072e7c_sx_26453_www200-200"},"logId":"202209211101070101501560821E64830F","code":"10000","msg":"success","subCode":"","subMsg":"","originResponse":"{\"code\":10000,\"data\":{\"audit_status\":1,\"byte_url\":\"https://p3-aio.ecombdimg.com/obj/ecom-shop-material/ALSUvYM_m_2864bb6bc16646f71441e3af23072e7c_sx_26453_www200-200\",\"folder_id\":\"0\",\"is_new\":true,\"material_id\":\"71456627402360425070798\"},\"log_id\":\"202209211101070101501560821E64830F\",\"msg\":\"success\",\"sub_code\":\"\",\"sub_msg\":\"\"}","success":true}
    }

    @Test
    @DisplayName("获取商品类目")
    public void getCategory() {
        ShopGetShopCategoryRequest request = new ShopGetShopCategoryRequest();
        ShopGetShopCategoryParam param = request.getParam();
        param.setCid(0L);
        ShopGetShopCategoryResponse response = request.execute(common.getAccessToken());
        log.info("response:{}", JsonUtil.toJson(response));
    }

    @Test
    @DisplayName("查询类目支持哪些发布规则")
    public void presellType() {
        ProductGetProductUpdateRuleRequest request = new ProductGetProductUpdateRuleRequest();
        ProductGetProductUpdateRuleParam param = request.getParam();
        param.setCategoryId(20219L);
        ProductGetProductUpdateRuleResponse response = request.execute(common.getAccessToken());
        log.info("response:{}", JsonUtil.toJson(response));

    }


    @Resource
    DouDianTemplateImpl douDianTemplate;

    @Test
    @DisplayName("erp抖店同步网关测试")
    public void gatewayTest() {
        douDianTemplate.productEdit(363472L, 3231L, 31);

//        System.out.println(douDianTemplate.getSpecsPrice(363429L));
    }


    @Resource
    NewGoodsBizService bizService;

    @Resource
    ThirdPlatformSyncBizService thirdPlatformSyncBizService;

    @Test
    @DisplayName("erp抖店同步接口测试")
    public void bizInterfaceTest() {
        DouDianSyncCmd cmd = new DouDianSyncCmd();
        cmd.setDouDianUrl("https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3572498637126770925&origin_type=604");
        cmd.setItemId(363442L);
        cmd.setItemCode("1000122");
        cmd.setSkuCount(1);
        cmd.setSyncType(31);
        bizService.douDianSync(ListUtil.of(cmd));
    }


    @Test
    @DisplayName("测试下抖店同步列表接口")
    public void douDianSyncList() {
        ThirdSyncPageQuery pageQuery = new ThirdSyncPageQuery();
        pageQuery.setPageSize(10);
        pageQuery.setPageIndex(0);
        PageResponse<ThirdSyncVO> thirdSyncVOPageResponse = thirdPlatformSyncBizService.syncList(pageQuery);
        System.out.println(JsonUtil.toJson(thirdSyncVOPageResponse));
    }

    @Test
    @DisplayName("创建素材中心文件夹")
    public void createFolder() {
        MaterialCreateFolderRequest request = new MaterialCreateFolderRequest();
        MaterialCreateFolderParam param = request.getParam();
        param.setName("erp同步图片");
        param.setParentFolderId("0");
        MaterialCreateFolderResponse response = request.execute(common.getAccessToken());
        System.out.println(JsonUtil.toJson(response));

    }


//    @Test
//    public void demon1(){
//        dateScriptService.fillComposeSkuCostProportion();
//    }

    @Resource
    DouDianSyncJob douDianSyncJob;
    @Resource
    CombinationItemBizService combinationItemBizService;

    @Test
    public void demo2() {
        SingleResponse<CombinationItemWithPriceVO> combinationItemWithPriceVOSingleResponse = combinationItemBizService.get(2507L, false);
        System.out.println(JsonUtil.toJson(combinationItemWithPriceVOSingleResponse));
    }

    @Resource
    CommonCalculator commonCalculator;

    @Test
    public void test2() {
        List<CommonCalculator.SkuPriceUnderSuiteNo> skuCostPriceUnderSuiteNo = commonCalculator.getSkuCostPriceUnderSuiteNo("666888", new BigDecimal("199.000000"));
        System.out.println(JSONUtil.toJsonStr(skuCostPriceUnderSuiteNo));
    }

    @Resource
    DouDianSyncJob syncJob;

    @Resource
    IThirdPlatformSyncLogService iThirdPlatformSyncLogService;

    @Test
    public void getByteUrlTest() {
//        String byteUrl = syncJob.getByteUrl(ThirdPlatformSyncExternalTaskType.DOUDIAN_DESC, 1670915751038L);
//        System.out.println(byteUrl);
        Optional<ThirdPlatformSyncLog> thirdPlatformSyncLog = iThirdPlatformSyncLogService.lambdaQuery().eq(ThirdPlatformSyncLog::getSyncId, 1670915751038L)
                .select().oneOpt();
        syncJob.syncItem(thirdPlatformSyncLog.get(), null);
    }

    @Autowired
    private ItemGateway itemGateway;

    @Test
    public void demo() {
        String ss = "{\n" +
                "                    \"attrId\": 592,\n" +
                "                    \"name\": \"规格\",\n" +
                "                    \"value\": \"110\"\n" +
                "                }";
        ItemAttrDto dto = JSONObject.parseObject(ss, ItemAttrDto.class);
        final ItemAttr itemAttr = buildItemAttr(dto, 688L, 363520L);
        long itemAttrId = itemGateway.saveItemAttrReturnId(itemAttr);
        System.out.println(itemAttrId);
    }

    private ItemAttr buildItemAttr(ItemAttrDto itemAttrDto, Long categoryId, Long itemId) {
        ItemAttr itemAttr = new ItemAttr();
        itemAttr.setItemId(itemId);
        itemAttr.setCategoryId(categoryId);
        itemAttr.setAttrId(itemAttrDto.getAttrId());
        itemAttr.setAttrValue(itemAttrDto.getValue());
        return itemAttr;
    }

    @Resource
    IItemSkuService iItemSkuService;

    //    60.176.122.141
    @Test
    public void testQueryProductCategory() {
//        syncParam();
//        syncJob.run();
//        testQueryProductCategory0();
        List<SkuRefundDo> nullPriceSkuCodes = new ArrayList<>();
        nullPriceSkuCodes.add(new SkuRefundDo());
        iItemSkuService.lambdaQuery()
                .in(ItemSku::getSkuCode, nullPriceSkuCodes).or().in(ItemSku::getProviderSpecifiedCode, nullPriceSkuCodes)
                .list();

    }


    public void syncParam() {
        Resp<DouDianSyncBO> resp = douDianTemplate.productEdit(363592L, 0L, 31);
        System.out.println(JsonUtil.toJson(resp));
    }

    public void testProductFormatNew() {
        AccessToken accessToken = common.getAccessToken();

        ProductEditV2Request request = new ProductEditV2Request();
        ProductEditV2Param param = request.getParam();
        param.setProductId(3432968689782237306L);
        String json = "{\"id\":15563,\"itemNo\":\"S015563\",\"itemImg\":\"https://cdn-test.daddylab.com/Upload/dpm/20231023/070729/n8vvwk7z2n7s049b.png\",\"name\":\"化妆品_儿童护肤品\",\"type\":1,\"brand\":\"无的\",\"partnerId\":3716,\"organizationId\":6525,\"organizationName\":\"企查查科技股份有限公司\",\"organizationNo\":\"91320594088140947F\",\"uName\":\"傅川\",\"mobile\":\"17764525729\",\"province\":\"北京市\",\"city\":\"市辖区\",\"area\":\"东城区\",\"addr\":\"123\",\"purchaseId\":0,\"purchaseName\":\"\",\"qc_ids\":[],\"qc_names\":[],\"skill_ids\":[],\"skill_names\":[],\"is_fast_up\":-1,\"moduleType\":0,\"first_category_id\":1,\"second_category_id\":22,\"product_filing_link\":[\"http://p.dlab.cn/#/cooperate/product/edit?type=1\\u0026sub_type=22\\u0026id=15563\",\"http://p.dlab.cn/#/cooperate/product/edit?type=1\\u0026sub_type=22\\u0026id=15564\"],\"product_filing_no\":\"备案或注册编号XXX\",\"product_filing_efficiency\":[\"04 防晒\"],\"sun_protection_exponent\":\"防晒指数很好\",\"pm_value\":\"1.2.1\",\"applicable_person\":[\"婴儿（0-3周岁，含3周岁）\",\"儿童（3-12周岁含12周岁）\"],\"applicable_skin_type\":\"敏感肌\",\"is_special_use_cosmetics\":\"否\",\"domestic_responsible_person\":\"负责人\",\"domestic_responsible_person_addr\":\"负责人地址\",\"item_standard\":[{\"name\":\"\",\"type\":1,\"standard_files\":[{\"name\":\"图片1.png\",\"url\":\"https://cdn-test.daddylab.com/Upload/dpm/20231023/100004/ik9pleoumog3jq60.png\"}]}],\"company_name\":\"企查查科技股份有限公司\",\"factory_address\":\"撒\",\"compositions\":[{\"spec_name\":\"默认规格\",\"composition_list\":[\"黄 11\"]}],\"compositions_info\":[{\"spec_name\":\"默认规格\",\"result\":[{\"name\":\"黄 11\",\"english_name\":\"YELLOW 11\",\"type\":\"normal\",\"sub_type\":\"\",\"prompt\":\"\"}]}]}";
        String s = douDianTemplate.productFormatNew("3432968689782237306", common.getAccessToken(), null, json, null);
        param.setProductFormatNew(s);
        param.setCommit(true);

        request.setParam(param);
        ProductEditV2Response response = request.execute(accessToken);
        System.out.println(JsonUtil.toJson(response));
    }

    public void testQueryProductCategory0() {
        AccessToken accessToken = common.getAccessToken();
        CategoryDetail categoryDetail = douDianTemplate.queryProductCategory("3432968689782237306", accessToken);
        System.out.println(JSONUtil.toJsonStr(categoryDetail));
        System.out.println("------------ ");

        Long firstCid = categoryDetail.getFirstCid();
        ProductQualificationConfigData productQualificationConfigData1 = douDianTemplate.qualificationConfigByCategoryId(firstCid, accessToken);
        System.out.println(firstCid);
        System.out.println(JsonUtil.toJson(productQualificationConfigData1));
        System.out.println("------------ ");

        Long secondCid = categoryDetail.getSecondCid();
        ProductQualificationConfigData productQualificationConfigData2 = douDianTemplate.qualificationConfigByCategoryId(secondCid, accessToken);
        System.out.println(secondCid);
        System.out.println(JsonUtil.toJson(productQualificationConfigData2));
        System.out.println("------------ ");

        Long thirdCid = categoryDetail.getThirdCid();
        ProductQualificationConfigData productQualificationConfigData3 = douDianTemplate.qualificationConfigByCategoryId(thirdCid, accessToken);
        System.out.println(thirdCid);
        System.out.println(JsonUtil.toJson(productQualificationConfigData3));
        System.out.println("------------ ");

        Long fourthCid = categoryDetail.getFourthCid();
        ProductQualificationConfigData productQualificationConfigData4 = douDianTemplate.qualificationConfigByCategoryId(fourthCid, accessToken);
        System.out.println(fourthCid);
        System.out.println(JsonUtil.toJson(productQualificationConfigData4));
        System.out.println("------------ ");

        System.out.println("------------ ");
        System.out.println(JsonUtil.toJson(douDianTemplate.queryCategoryAttributeList(firstCid, accessToken)));
        System.out.println("------------ ");
        System.out.println(JsonUtil.toJson(douDianTemplate.queryCategoryAttributeList(secondCid, accessToken)));
        System.out.println("------------ ");
        System.out.println(JsonUtil.toJson(douDianTemplate.queryCategoryAttributeList(thirdCid, accessToken)));
        System.out.println("------------ ");
        System.out.println(JsonUtil.toJson(douDianTemplate.queryCategoryAttributeList(fourthCid, accessToken)));
    }
    
    

}
