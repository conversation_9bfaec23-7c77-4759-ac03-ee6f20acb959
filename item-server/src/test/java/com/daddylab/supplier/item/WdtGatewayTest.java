package com.daddylab.supplier.item;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.daddylab.mall.wdtsdk.Pager;
import com.daddylab.mall.wdtsdk.WdtErpException;
import com.daddylab.mall.wdtsdk.api.goods.GoodsAPI;
import com.daddylab.mall.wdtsdk.api.goods.dto.PlatformGoodsSearchRequest;
import com.daddylab.mall.wdtsdk.api.goods.dto.PlatformGoodsSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RawRefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.RefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RawRefundSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.aftersales.refund.dto.RefundSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.goods.ApiGoodsAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.GoodsBrandAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.SuiteAPI;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.*;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsBrandSearchResponse.Detail;
import com.daddylab.mall.wdtsdk.apiv2.goods.dto.GoodsQueryWithSpecResponse.Goods;
import com.daddylab.mall.wdtsdk.apiv2.purchase.PurchaseOrderAPI;
import com.daddylab.mall.wdtsdk.apiv2.purchase.PurchaseReturnAPI;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseOrderQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseOrderQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseReturnQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.purchase.dto.PurchaseReturnQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.sales.LogisticsSyncAPI;
import com.daddylab.mall.wdtsdk.apiv2.sales.TradeQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.LogisticsSyncGetSyncListExtParams;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.LogisticsSyncGetSyncListExtResponse;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.sales.dto.TradeQueryQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.setting.PurchaseProviderAPI;
import com.daddylab.mall.wdtsdk.apiv2.setting.WarehouseAPI;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.*;
import com.daddylab.mall.wdtsdk.apiv2.setting.dto.WarehouseQueryWarehouseResponse.Details;
import com.daddylab.mall.wdtsdk.apiv2.wms.StockSpecAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.dto.*;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.PreStockinAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.StockinRefundAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinSearchParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.PreStockinSearchResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinRefundQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockin.dto.StockinRefundQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.StockoutOtherQueryAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.StockoutSalesAPI;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutOtherQueryQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutOtherQueryQueryWithDetailResponse;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryWithDetailParams;
import com.daddylab.mall.wdtsdk.apiv2.wms.stockout.dto.StockoutSalesQueryWithDetailResponse;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtConfig;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.wdt.WdtGatewayImpl;
import com.daddylab.supplier.item.infrastructure.qimen.QimenConfig;
import com.daddylab.supplier.item.infrastructure.qimen.wdt.QimenApiFactory;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import lombok.NonNull;
import lombok.SneakyThrows;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.Resource;

/**
 * <AUTHOR>
 * @since 2022/4/15
 */
public class WdtGatewayTest {
    
    @Test
    @DisplayName("平台商品查询测试")
    public void platformGoodsSearch() throws WdtErpException {
        final WdtGatewayImpl wdtGateway = getWdtGateway();
        
        String startTime = "2022-05-31 05:01:33";
        String endTime = "2022-05-31 05:01:33";
        
        final GoodsAPI goodsAPI = wdtGateway.goodsAPI();
        final PlatformGoodsSearchRequest request = new PlatformGoodsSearchRequest();
        //        request.setGoodsId("656539026856");
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        
        final Pager pager = new Pager(10, 0, true);
        final PlatformGoodsSearchResponse platformGoodsSearchResponse =
                goodsAPI.platformGoodsSearch(request, pager);
        assertNotNull(platformGoodsSearchResponse.getGoodsList());
        
        final GoodsAPI qimenGoodsAPI = wdtGateway.qimenGoodsAPI();
        final PlatformGoodsSearchResponse platformGoodsSearchResponse1 =
                qimenGoodsAPI.platformGoodsSearch(request, pager);
        assertNotNull(platformGoodsSearchResponse1.getGoodsList());
    }
    
    @Test
    @DisplayName("平台商品查询测试V2")
    public void platformGoodsSearchV2() throws WdtErpException {
        final WdtGatewayImpl wdtGateway = getWdtGateway(1);
        final ApiGoodsAPI apiGoodsAPI = wdtGateway.getQimenAPI(ApiGoodsAPI.class);
        
        final ApiGoodsSearchParams params = new ApiGoodsSearchParams();
        params.setGoodsId("656539026856");
        params.setStartTime(DateUtil.formatLocalDateTime(LocalDateTime.now().minusDays(30)));
        params.setEndTime(DateUtil.formatLocalDateTime(LocalDateTime.now()));
        
        final Pager pager = new Pager(10, 0, true);
        final ApiGoodsSearchResponse response = apiGoodsAPI.search(params, pager);
        System.out.println(JsonUtil.toJson(response));
    }
    
    @Test
    @DisplayName("订单查询测试")
    public void tradeQuery() throws WdtErpException {
        final WdtGatewayImpl wdtGateway = getWdtGateway(1);
        String endTime = "2022-04-01 10:00:00";
        String startTime = "2022-04-01 11:00:00";
        
        final Pager pager = new Pager(10, 0, true);
        
        final TradeQueryAPI tradeQueryAPI = wdtGateway.getAPI(TradeQueryAPI.class, 0, true);
        final TradeQueryQueryWithDetailParams tradeQueryQueryWithDetailParams =
                new TradeQueryQueryWithDetailParams();
        //        tradeQueryQueryWithDetailParams.setTradeNo("JY2022052722470");
        tradeQueryQueryWithDetailParams.setSrcTid("3905231329333630145");
        //        tradeQueryQueryWithDetailParams.setSrcTid("JY2022052722470");
        //        tradeQueryQueryWithDetailParams.setStartTime(startTime);
        //        tradeQueryQueryWithDetailParams.setEndTime(endTime);
        //        tradeQueryQueryWithDetailParams.setWarehouseNo("hzlb3-test");
        final TradeQueryQueryWithDetailResponse tradeQueryQueryWithDetailResponse =
                tradeQueryAPI.queryWithDetail(tradeQueryQueryWithDetailParams, pager);
        System.out.println(JsonUtil.toJson(tradeQueryQueryWithDetailResponse));
        assertNotNull(tradeQueryQueryWithDetailResponse.getOrder());
        
        final TradeQueryAPI tradeQueryQimenAPI = wdtGateway.getQimenAPI(TradeQueryAPI.class, 0, true);
        final TradeQueryQueryWithDetailResponse tradeQueryQueryWithDetailResponse1 =
                tradeQueryQimenAPI.queryWithDetail(tradeQueryQueryWithDetailParams, pager);
        System.out.println(JsonUtil.toJson(tradeQueryQueryWithDetailResponse1));
        assertNotNull(tradeQueryQueryWithDetailResponse1.getOrder());
        assertTrue(
                tradeQueryQueryWithDetailResponse1.getTotalCount()
                        >= tradeQueryQueryWithDetailResponse.getTotalCount(),
                "奇门返回的数据应该多于或者等于旺店通返回的数据");
    }
    
    @Test
    @DisplayName("退换单查询测试")
    public void refundQuery() throws WdtErpException {
        final WdtGatewayImpl wdtGateway = getWdtGateway(1);
        String endTime = "2022-04-01 11:00:00";
        String startTime = "2022-04-01 10:00:00";
        
        final Pager pager = new Pager(10, 0, true);
        
        final RefundAPI refundQueryAPI = wdtGateway.getAPI(RefundAPI.class, 0, true);
        final RefundSearchParams params = new RefundSearchParams();
        //        params.setModifiedFrom(startTime);
        //        params.setModifiedTo(endTime);
        params.setRefundNo("TK2205310946");
        
        final RefundSearchResponse response = refundQueryAPI.search(params, pager);
        System.out.println(JsonUtil.toJson(response));
        assertNotNull(response.getOrder());
        
        final RefundAPI tradeQueryQimenAPI = wdtGateway.getQimenAPI(RefundAPI.class, 0, true);
        final RefundSearchResponse qimenResponse = tradeQueryQimenAPI.search(params, pager);
        System.out.println(JsonUtil.toJson(qimenResponse));
        assertNotNull(qimenResponse.getOrder());
    }
    
    @NonNull
    private WdtGatewayImpl getWdtGateway() {
        return getWdtGateway(0);
    }
    
    @NonNull
    @SneakyThrows
    private WdtGatewayImpl getWdtGateway(int configId) {
        final YamlPropertySourceLoader yamlPropertySourceLoader = new YamlPropertySourceLoader();
        final DefaultResourceLoader defaultResourceLoader = new DefaultResourceLoader();
        final Resource resource = defaultResourceLoader.getResource("application.yml");
        final List<PropertySource<?>> propertySources =
                yamlPropertySourceLoader.load("test-properties", resource);
        final CompositePropertySource compositePropertySource =
                new CompositePropertySource("test-properties");
        propertySources.forEach(compositePropertySource::addPropertySource);
        final StandardEnvironment standardEnvironment = new StandardEnvironment();
        standardEnvironment.getPropertySources().addFirst(compositePropertySource);
        final Binder binder = Binder.get(standardEnvironment);
        final QimenConfig qimenConfig = binder.bindOrCreate("qimen", QimenConfig.class);
        final QimenApiFactory qimenApiFactory =
                new QimenApiFactory(qimenConfig.qimenClient(), qimenConfig);
        final WdtConfig wdtConfig = binder.bind("wdt", WdtConfig.class).get();
        final WdtConfig.Config config = wdtConfig.getConfigs().get(configId);
        final ArrayList<WdtConfig.Config> configs = new ArrayList<>();
        configs.add(config);
        wdtConfig.setConfigs(configs);
        return new WdtGatewayImpl(wdtConfig, qimenApiFactory);
    }
    
    @Test
    public void queryStock() throws WdtErpException {
        final StockSpecAPI stockAPI = getWdtGateway(1).getAPI(StockSpecAPI.class);
        final int pageSize = 10;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        final StockSpecSearchParams params = new StockSpecSearchParams();
        params.setSpecNos(Arrays.asList("100156401"));
        params.setWarehouseNo("CK001438");
        //        params.setStartTime("2024-02-28");
        //        params.setEndTime("2024-02-29");
        
        final StockSpecSearchResponse response = stockAPI.search(params, pager);
        System.out.println(JsonUtil.toJson(response));
    }
    
    @Test
    public void queryStock2() throws WdtErpException {
        final StockSpecAPI stockAPI = getWdtGateway(1).getAPI(StockSpecAPI.class);
        final int pageSize = 10;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        final StockSpecSearch2Params params = new StockSpecSearch2Params();
        //        params.setSpecNos(Arrays.asList("TE1000055009552"));
        params.setStartTime("2024-02-28");
        params.setEndTime("2024-02-29");
        
        final StockSpecSearch2Response response = stockAPI.search2(params, pager);
        System.out.println(JsonUtil.toJson(response));
    }
    
    @Test
    public void queryBrand() throws WdtErpException {
        final GoodsBrandAPI api = getWdtGateway().getAPI(GoodsBrandAPI.class);
        final int pageSize = 1000;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        final GoodsBrandSearchParams params = new GoodsBrandSearchParams();
        final GoodsBrandSearchResponse response = api.search(params, pager);
        System.out.println(
                response.getDetailList().stream()
                        .filter(v -> v.getIsDisabled() == 0)
                        .map(Detail::getBrandNo)
                        .collect(Collectors.toSet())
                        .size());
    }
    
    @Test
    public void queryProvider() throws WdtErpException {
        final PurchaseProviderAPI api = getWdtGateway().getAPI(PurchaseProviderAPI.class);
        final int pageSize = 1000;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        final PurchaseProviderQueryDetailParams params = new PurchaseProviderQueryDetailParams();
        
        final PurchaseProviderQueryDetailResponse response = api.queryDetail(params, pager);
        System.out.println(
                response.getDetails().stream()
                        .map(v -> "'" + v.getProviderNo() + "'")
                        .collect(Collectors.joining(",")));
    }
    
    @Test
    public void queryWarehouseEach() throws WdtErpException {
        for (int i = 1; i <= 3; i++) {
            final WarehouseAPI api = getWdtGateway(i).getAPI(WarehouseAPI.class);
            final int pageSize = 1000;
            int pageNo = 0;
            final Pager pager = new Pager(pageSize, pageNo, true);
            final WarehouseQueryWarehouseParams params = new WarehouseQueryWarehouseParams();
            params.setWarehouseNo("CK000372");
            
            final WarehouseQueryWarehouseResponse response = api.queryWarehouse(params, pager);
            System.out.println(JsonUtil.toJson(response));
            System.out.println(
                    response.getDetails().stream().map(Details::getType).collect(Collectors.toSet()));
        }
    }
    
    @Test
    public void queryWarehouse() throws WdtErpException {
        final WarehouseAPI api = getWdtGateway(4).getAPI(WarehouseAPI.class);
        final int pageSize = 1000;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        final WarehouseQueryWarehouseParams params = new WarehouseQueryWarehouseParams();
        //        params.setWarehouseNo("CK000372");
        
        final WarehouseQueryWarehouseResponse response = api.queryWarehouse(params, pager);
        System.out.println(JsonUtil.toJson(response));
        System.out.println(
                response.getDetails().stream().map(Details::getType).collect(Collectors.toSet()));
    }
    
    @Test
    public void queryWarehouseTotalCount() {
        final AtomicInteger totalCount = new AtomicInteger();
        Arrays.asList(1, 2, 3, 4)
                .forEach(
                        i -> {
                            final WarehouseAPI api = getWdtGateway(i).getAPI(WarehouseAPI.class);
                            final int pageSize = 1;
                            int pageNo = 0;
                            final Pager pager = new Pager(pageSize, pageNo, true);
                            final WarehouseQueryWarehouseParams params = new WarehouseQueryWarehouseParams();
                            params.setHideDelete(0);
                            final WarehouseQueryWarehouseResponse response;
                            try {
                                response = api.queryWarehouse(params, pager);
                                final Integer count = response.getTotalCount();
                                System.out.println(count);
                                totalCount.addAndGet(count);
                            } catch (WdtErpException e) {
                                e.printStackTrace();
                            }
                        });
        System.out.println(totalCount.get());
    }
    
    @Test
    public void queryOtherStockOutOrder() throws WdtErpException {
        final StockoutOtherQueryAPI api = getWdtGateway().getAPI(StockoutOtherQueryAPI.class);
        final int pageSize = 1000;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        final StockoutOtherQueryQueryWithDetailParams params =
                new StockoutOtherQueryQueryWithDetailParams();
        params.setStartTime("2022-01-26 15:21:07");
        params.setEndTime("2022-02-25 15:21:07");
        
        final StockoutOtherQueryQueryWithDetailResponse response = api.queryWithDetail(params, pager);
        System.out.println(JsonUtil.toJson(response));
    }
    
    @Test
    @DisplayName("查询采购入库单")
    public void queryPurchaseStockInOrder() throws WdtErpException {
        final PurchaseOrderAPI api = getWdtGateway(1).getAPI(PurchaseOrderAPI.class);
        final PurchaseOrderQueryWithDetailParams params = new PurchaseOrderQueryWithDetailParams();
        params.setPurchaseNo("CGRK2403000624");
        final Pager pager = new Pager(10, 0, true);
        final PurchaseOrderQueryWithDetailResponse resp = api.queryWithDetail(params, pager);
        System.out.println(JsonUtil.toJson(resp));
    }
    
    @Test
    @DisplayName("查询退料出库单")
    public void queryReturnStockOutOrder() throws WdtErpException {
        final PurchaseReturnAPI api = getWdtGateway().getAPI(PurchaseReturnAPI.class);
        
        final PurchaseReturnQueryWithDetailParams params = new PurchaseReturnQueryWithDetailParams();
        params.setReturnNo("CGRK2022062125");
        
        final Pager pager = new Pager(10, 0, true);
        final PurchaseReturnQueryWithDetailResponse response = api.queryWithDetail(params, pager);
        final List<PurchaseReturnQueryWithDetailResponse.Order> orders = response.getOrder();
        System.out.println(JsonUtil.toJson(orders));
    }
    
    @Test
    public void queryGoods() throws WdtErpException {
        final com.daddylab.mall.wdtsdk.apiv2.goods.GoodsAPI api =
                getWdtGateway(1).getAPI(com.daddylab.mall.wdtsdk.apiv2.goods.GoodsAPI.class);
        final int pageSize = 100;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        
        final GoodsQueryWithSpecParams params = new GoodsQueryWithSpecParams();
        params.setSpecNo("SG00006501");
        final GoodsQueryWithSpecResponse response = api.queryWithSpec(params, pager);
        System.out.println(JsonUtil.toJson(response));
        System.out.println(
                response.getGoodsList().stream().map(Goods::getGoodsType).collect(Collectors.toSet()));
    }
    
    @Test
    public void queryOrder() throws WdtErpException {
        //        final TradeQueryAPI api = getWdtGateway(1).getAPI(TradeQueryAPI.class);
        final TradeQueryAPI api = getWdtGateway(1).getQimenAPI(TradeQueryAPI.class);
        final int pageSize = 1000;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        
        final TradeQueryQueryWithDetailParams params = new TradeQueryQueryWithDetailParams();
        //        params.setSrcTid("20230803143247376572488576");
        //        params.setTradeNo("JY202405212529");
        params.setSrcTid("20240801508237352932835898");
        //        params.setStartTime("2023-08-20 10:46:42");
        //        params.setEndTime("2023-08-20 11:46:42");
        
        final TradeQueryQueryWithDetailResponse response = api.queryWithDetail(params, pager);
        System.out.println(JsonUtil.toJson(response));
    }
    
    @Test
    @DisplayName("销售出库单奇门查询")
    public void stockOutSalesQueryOne() throws WdtErpException {
        final StockoutSalesAPI api = getWdtGateway(1).getQimenAPI(StockoutSalesAPI.class);
        final int pageSize = 50;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        
        final StockoutSalesQueryWithDetailParams params = new StockoutSalesQueryWithDetailParams();
//            params.setSrcOrderNo("CK2025081210232");
        params.setStockoutNo("CK2025081210232");
//    params.setStartTime("2025-08-12 09:38:07");
//    params.setEndTime("2025-08-12 09:38:07");
//    params.setStatusType(3);
//    params.setStatus("5,10,50,51,52,53,54,58,60,61,63,65,70,73,75,77,79,90,110,-1");
        
        final StockoutSalesQueryWithDetailResponse response = api.queryWithDetail(params, pager);
        System.out.println(JsonUtil.toJson(response));
    }
    
    @Test
    @DisplayName("销售出库单奇门查询")
    public void stockOutSalesQuery() throws WdtErpException {
        final StockoutSalesAPI api = getWdtGateway(6).getQimenAPI(StockoutSalesAPI.class);
        final int pageSize = 10;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        
        final LocalDateTime now = LocalDateTime.now().truncatedTo(ChronoUnit.HOURS);
        final LocalDateTime minTime = now.minusDays(7);
        final DateTimeFormatter timeFormatter = DatePattern.NORM_DATETIME_FORMATTER;
        for (LocalDateTime timeCursor = now;
             timeCursor.isAfter(minTime);
             timeCursor = timeCursor.minusHours(1)) {
            
            final StockoutSalesQueryWithDetailParams params = new StockoutSalesQueryWithDetailParams();
            params.setStartTime(timeCursor.minusHours(1).format(timeFormatter));
            params.setEndTime(timeCursor.format(timeFormatter));
            
            final StockoutSalesQueryWithDetailResponse response = api.queryWithDetail(params, pager);
            System.out.println(JsonUtil.toJson(response));
            if (!response.getOrder().isEmpty()) {
                break;
            }
        }
    }
    
    @Test
    @DisplayName("退货入库单奇门查询")
    public void StockinRefundQueryWithDetail() throws WdtErpException {
        final WdtGatewayImpl wdtGateway = getWdtGateway(5);
        //        final WdtGatewayImpl wdtGateway = getWdtGateway(3);
        int pageSize = 100;
        final Pager pager = new Pager(pageSize, 0, true);
        final StockinRefundAPI api = wdtGateway.getQimenAPI(StockinRefundAPI.class);
        //        final StockinRefundAPI api = wdtGateway.getAPI(StockinRefundAPI.class);
        final StockinRefundQueryWithDetailParams params = new StockinRefundQueryWithDetailParams();
        params.setStockinNo("RK2203220042");
        //        params.setStartTime("2022-03-22 09:17:20");
        //        params.setEndTime("2022-03-22 10:17:20");
        // 10=已取消；20=编辑中；30=待审核；37=待质检；40=质检确认； 60=待结算；70=暂估结算；80=已完成
        params.setStatus("10,30,37,40,60,70,80");
        //        params.setStartTime("2022-03-22 10:17:20");
        //        params.setEndTime("2022-03-22 11:17:20");
        //        params.setWarehouseNo("CK000259");
        final StockinRefundQueryWithDetailResponse response = api.queryWithDetail(params, pager);
        System.out.println(JsonUtil.toJson(response));
    }
    
    @Test
    @DisplayName("退货预入库单奇门查询")
    public void preStockinSearch() throws WdtErpException {
        final PreStockinAPI api = getWdtGateway(5).getQimenAPI(PreStockinAPI.class);
        final int pageSize = 1000;
        int pageNo = 0;
        final Pager pager = new Pager(pageSize, pageNo, true);
        final PreStockinSearchParams params = new PreStockinSearchParams();
        params.setMtFrom("2022-06-01 00:00:00");
        params.setMtTo("2022-07-01 00:00:00");
        final PreStockinSearchResponse response = api.search(params, pager);
        System.out.println(JsonUtil.toJson(response));
    }
    
    @Test
    @DisplayName("原始退换单奇门查询")
    public void rawRefundSearch() throws WdtErpException {
        final RawRefundAPI qimenAPI = getWdtGateway(0).getAPI(RawRefundAPI.class);
        final RawRefundSearchParams params = new RawRefundSearchParams();
        final LocalDateTime endTime =
                com.daddylab.supplier.item.infrastructure.utils.DateUtil.now()
                        .plusDays(1)
                        .toLocalDate()
                        .atStartOfDay();
        final LocalDateTime startTime = endTime.minusDays(30);
        params.setStartTime(com.daddylab.supplier.item.infrastructure.utils.DateUtil.format(startTime));
        params.setEndTime(com.daddylab.supplier.item.infrastructure.utils.DateUtil.format(endTime));
        params.setTimeType(1);
        params.setTid("ceshi20221101001");
        
        final RawRefundSearchResponse searchResponse = qimenAPI.search(params, new Pager(10, 0, true));
        System.out.println(searchResponse);
    }
    
    @Test
    public void suiteSearch() throws WdtErpException {
        final SuiteAPI api = getWdtGateway(1).getAPI(SuiteAPI.class);
        final SuiteSearchParams params = new SuiteSearchParams();
        params.setSuiteNo("YMRJZZH2");
        
        final SuiteSearchResponse search = api.search(params, new Pager(10, 0, true));
        System.out.println(JsonUtil.toJson(search));
    }
    
    @Test
    public void stockChangeHistory() throws WdtErpException {
        final StockSpecAPI api = getWdtGateway(1).getAPI(StockSpecAPI.class);
        final Pager pager = new Pager(10, 0, true);
        final StockSpecQueryChangeHistoryParams params = new StockSpecQueryChangeHistoryParams();
        params.setStartDate("2024-03-01 17:00:00");
        params.setEndDate("2024-03-01 18:00:00");
        //        params.setSpecNo("");
        //        params.setType(0);
        //        params.setDefect(false);
        //        params.setWarehouseNo("");
        
        final StockSpecQueryChangeHistoryResponse stockSpecQueryChangeHistoryResponse =
                api.queryChangeHistory(params, pager);
        System.out.println(JsonUtil.toJson(stockSpecQueryChangeHistoryResponse));
    }
    
    @Test
    public void logstisticQuery() throws WdtErpException {
        final LogisticsSyncAPI api = getWdtGateway(8).getAPI(LogisticsSyncAPI.class);
        final LogisticsSyncGetSyncListExtParams params = new LogisticsSyncGetSyncListExtParams();
//    params.setShopNo("K1375");
        params.setIsOwnPlatform(true);
        
        final Pager pager = new Pager(100, 0, true);
        final LogisticsSyncGetSyncListExtResponse syncListExt = api.getSyncListExt(params, pager);
        System.out.println(JsonUtil.toJson(syncListExt));
    }
    
}
