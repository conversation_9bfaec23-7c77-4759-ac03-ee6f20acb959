package com.daddylab.supplier.item.domain.itemSync;

import com.daddylab.supplier.item.domain.common.enums.Platform;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 平台商品ID提取器测试类
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
public class PlatformItemIdExtractorTest {

    @Test
    public void testExtractTaobaoItemId() {
        // 测试淘宝链接ID提取
        String taobaoLink = "https://detail.tmall.com/item.htm?id=123456789";
        String itemId = PlatformItemIdExtractor.extractItemId(Platform.TAOBAO, taobaoLink);
        assertEquals("123456789", itemId);
        
        // 测试无效淘宝链接
        String invalidLink = "https://invalid.link.com";
        String emptyId = PlatformItemIdExtractor.extractItemId(Platform.TAOBAO, invalidLink);
        assertEquals("", emptyId);
    }

    @Test
    public void testExtractDouDianItemId() {
        // 测试抖店链接ID提取
        String douLink = "https://haohuo.jinritemai.com/ecommerce/trade/detail/index.html?id=3581979923847470754&origin_type=604";
        String itemId = PlatformItemIdExtractor.extractItemId(Platform.DOUYIN, douLink);
        assertEquals("3581979923847470754", itemId);
        
        // 测试无效抖店链接
        String invalidLink = "https://invalid.link.com";
        String emptyId = PlatformItemIdExtractor.extractItemId(Platform.DOUYIN, invalidLink);
        assertEquals("", emptyId);
    }

    @Test
    public void testExtractXiaoHongShuItemId() {
        // 测试小红书链接ID提取
        String xhsLink = "https://www.xiaohongshu.com/goods-detail/647f39d8896a160001f86b62?xhsshare=WeixinSession&appuid=598e914450c4b42eaebe1400&apptime=1700205021";
        String itemId = PlatformItemIdExtractor.extractItemId(Platform.XIAOHONGSHU, xhsLink);
        assertEquals("647f39d8896a160001f86b62", itemId);
        
        // 测试无效小红书链接
        String invalidLink = "https://invalid.link.com";
        String emptyId = PlatformItemIdExtractor.extractItemId(Platform.XIAOHONGSHU, invalidLink);
        assertEquals("", emptyId);
    }

    @Test
    public void testUnsupportedPlatform() {
        // 测试不支持的平台
        String link = "https://example.com/item/123";
        String itemId = PlatformItemIdExtractor.extractItemId(Platform.YOUZAN, link);
        assertEquals("", itemId);
    }

    @Test
    public void testNullOrEmptyParams() {
        // 测试空参数
        String itemId1 = PlatformItemIdExtractor.extractItemId(null, "https://example.com");
        assertEquals("", itemId1);
        
        String itemId2 = PlatformItemIdExtractor.extractItemId(Platform.TAOBAO, null);
        assertEquals("", itemId2);
        
        String itemId3 = PlatformItemIdExtractor.extractItemId(Platform.TAOBAO, "");
        assertEquals("", itemId3);
    }
}
