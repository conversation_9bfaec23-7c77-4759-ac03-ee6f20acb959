package com.daddylab.supplier.item.application.stockWarn;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * 库存告警应用服务测试
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "spring.profiles.active:test"
})
public class StockWarnApplicationServiceTest {

    @Autowired
    private StockWarnApplicationService stockWarnApplicationService;

    @Test
    public void testManualTriggerStockWarnCheck() {
        log.info("开始测试手动触发库存告警检查");
        try {
            Integer count = stockWarnApplicationService.manualTriggerStockWarnCheck();
            log.info("库存告警检查完成，生成 {} 条告警记录", count);
        } catch (Exception e) {
            log.error("库存告警检查失败", e);
        }
    }

    @Test
    public void testManualTriggerStockWarnEmailSend() {
        log.info("开始测试手动触发库存告警邮件发送");
        try {
            Integer count = stockWarnApplicationService.manualTriggerStockWarnEmailSend();
            log.info("库存告警邮件发送完成，发送 {} 封邮件", count);
        } catch (Exception e) {
            log.error("库存告警邮件发送失败", e);
        }
    }
}
