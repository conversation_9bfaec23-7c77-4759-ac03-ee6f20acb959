package com.daddylab.supplier.item.domain.stockWarn.service.impl;

import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnInfo;
import com.daddylab.supplier.item.domain.stockWarn.entity.StockWarnSummary;
import com.daddylab.supplier.item.domain.auth.gateway.UserContextGateway;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.entity.*;
import com.daddylab.supplier.item.infrastructure.gatewayimpl.db.service.*;
import com.daddylab.supplier.item.infrastructure.oss.OssGateway;
import com.daddylab.supplier.item.infrastructure.email.EmailService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 库存告警领域服务边界情况和异常情况测试
 *
 * <AUTHOR>
 * @date 2025/01/29
 */
@ExtendWith(MockitoExtension.class)
class StockWarnDomainServiceImplEdgeCaseTest {

    @Mock
    private IWdtStockSpecService wdtStockSpecService;

    @Mock
    private ICategoryWarnStockService categoryWarnStockService;

    @Mock
    private IWarehouseService warehouseService;

    @Mock
    private ICategoryService categoryService;

    @Mock
    private IItemService itemService;

    @Mock
    private UserContextGateway userContextGateway;

    @Mock
    private IDadStaffService dadStaffService;

    @Mock
    private OssGateway ossGateway;

    @Mock
    private EmailService emailService;

    @InjectMocks
    private StockWarnDomainServiceImpl stockWarnDomainService;

    /**
     * 测试检查库存告警 - 数据异常情况
     */
    @Test
    void testCheckStockWarn_DataAnomalies() {
        // Given - 创建包含异常数据的测试数据
        List<CategoryWarnStock> categoryWarnStocks = Arrays.asList(
                createCategoryWarnStock(1L, new BigDecimal("10"))
        );

        List<WdtStockSpec> stockSpecs = Arrays.asList(
                // 正常数据
                createWdtStockSpec("ITEM001", "正常商品", "WH001", 1L, new BigDecimal("5")),
                // 商品编号为空
                createWdtStockSpec("", "空编号商品", "WH001", 1L, new BigDecimal("3")),
                // 仓库编号为空
                createWdtStockSpec("ITEM002", "空仓库商品", "", 1L, new BigDecimal("3")),
                // 库存为null
                createWdtStockSpec("ITEM003", "空库存商品", "WH001", 1L, null),
                // 库存为负数
                createWdtStockSpec("ITEM004", "负库存商品", "WH001", 1L, new BigDecimal("-5"))
        );

        List<Category> categories = Arrays.asList(
                createCategory(1L, "电子产品")
        );

        List<Warehouse> warehouses = Arrays.asList(
                createWarehouse("WH001", "主仓库", "1001")
        );

        List<Item> items = Arrays.asList(
                createItem("ITEM001", 1L),
                createItem("ITEM002", 1L),
                createItem("ITEM003", 1L),
                createItem("ITEM004", 1L)
        );

        when(categoryWarnStockService.list()).thenReturn(categoryWarnStocks);
        when(wdtStockSpecService.list()).thenReturn(stockSpecs);
        when(categoryService.list()).thenReturn(categories);
        when(warehouseService.list()).thenReturn(warehouses);
        when(itemService.list()).thenReturn(items);

        // When
        List<StockWarnInfo> result = stockWarnDomainService.checkStockWarn();

        // Then
        assertNotNull(result);
        // 只有正常数据应该被处理，异常数据应该被过滤掉
        assertEquals(1, result.size());
        assertEquals("ITEM001", result.get(0).getItemCode());
    }

    /**
     * 测试检查库存告警 - 类目信息缺失
     */
    @Test
    void testCheckStockWarn_MissingCategoryInfo() {
        // Given
        List<CategoryWarnStock> categoryWarnStocks = Arrays.asList(
                createCategoryWarnStock(1L, new BigDecimal("10"))
        );

        List<WdtStockSpec> stockSpecs = Arrays.asList(
                createWdtStockSpec("ITEM001", "测试商品", "WH001", 1L, new BigDecimal("5"))
        );

        // 类目信息为空
        when(categoryWarnStockService.list()).thenReturn(categoryWarnStocks);
        when(wdtStockSpecService.list()).thenReturn(stockSpecs);
        when(categoryService.list()).thenReturn(Collections.emptyList());
        when(warehouseService.list()).thenReturn(Collections.emptyList());
        when(itemService.list()).thenReturn(Collections.emptyList());

        // When
        List<StockWarnInfo> result = stockWarnDomainService.checkStockWarn();

        // Then
        assertNotNull(result);
        // 由于类目信息缺失，应该返回空列表
        assertTrue(result.isEmpty());
    }

    /**
     * 测试检查库存告警 - 商品信息缺失
     */
    @Test
    void testCheckStockWarn_MissingItemInfo() {
        // Given
        List<CategoryWarnStock> categoryWarnStocks = Arrays.asList(
                createCategoryWarnStock(1L, new BigDecimal("10"))
        );

        List<WdtStockSpec> stockSpecs = Arrays.asList(
                createWdtStockSpec("ITEM001", "测试商品", "WH001", 1L, new BigDecimal("5"))
        );

        List<Category> categories = Arrays.asList(
                createCategory(1L, "电子产品")
        );

        List<Warehouse> warehouses = Arrays.asList(
                createWarehouse("WH001", "主仓库", "1001")
        );

        // 商品信息为空
        when(categoryWarnStockService.list()).thenReturn(categoryWarnStocks);
        when(wdtStockSpecService.list()).thenReturn(stockSpecs);
        when(categoryService.list()).thenReturn(categories);
        when(warehouseService.list()).thenReturn(warehouses);
        when(itemService.list()).thenReturn(Collections.emptyList());

        // When
        List<StockWarnInfo> result = stockWarnDomainService.checkStockWarn();

        // Then
        assertNotNull(result);
        // 由于商品信息缺失，应该返回空列表
        assertTrue(result.isEmpty());
    }

    /**
     * 测试按订单员汇总 - 订单员ID为空
     */
    @Test
    void testSummarizeWarnByOrderPersonnel_EmptyOrderPersonnelIds() {
        // Given
        List<StockWarnInfo> warnInfos = new ArrayList<>();
        StockWarnInfo warnInfo = new StockWarnInfo();
        warnInfo.setItemCode("ITEM001");
        warnInfo.setItemName("测试商品");
        warnInfo.setOrderPersonnelIds(Collections.emptyList()); // 空的订单员ID列表
        warnInfos.add(warnInfo);

        // When
        List<StockWarnSummary> result = stockWarnDomainService.summarizeWarnByOrderPersonnel(warnInfos);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试按订单员汇总 - 订单员ID为null
     */
    @Test
    void testSummarizeWarnByOrderPersonnel_NullOrderPersonnelIds() {
        // Given
        List<StockWarnInfo> warnInfos = new ArrayList<>();
        StockWarnInfo warnInfo = new StockWarnInfo();
        warnInfo.setItemCode("ITEM001");
        warnInfo.setItemName("测试商品");
        warnInfo.setOrderPersonnelIds(null); // null的订单员ID列表
        warnInfos.add(warnInfo);

        // When
        List<StockWarnSummary> result = stockWarnDomainService.summarizeWarnByOrderPersonnel(warnInfos);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试生成Excel - 大量数据
     */
    @Test
    void testGenerateAndUploadExcel_LargeDataSet() {
        // Given - 创建大量告警商品数据
        List<StockWarnInfo> largeWarnItems = new ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            StockWarnInfo warnInfo = new StockWarnInfo();
            warnInfo.setItemCode("ITEM" + String.format("%04d", i));
            warnInfo.setItemName("测试商品" + i);
            warnInfo.setCategoryName("电子产品");
            warnInfo.setCurrentStock(new BigDecimal("5"));
            warnInfo.setWarnStock(new BigDecimal("10"));
            warnInfo.setWarehouseName("主仓库");
            largeWarnItems.add(warnInfo);
        }

        StockWarnSummary summary = new StockWarnSummary();
        summary.setOrderPersonnelId(1001L);
        summary.setOrderPersonnelName("张三");
        summary.setOrderPersonnelEmail("<EMAIL>");
        summary.setWarnItems(largeWarnItems);
        summary.setWarnItemCount(1000);

        String expectedOssUrl = "https://oss.example.com/stock-warn/张三_20250129_143000.xlsx";
        when(ossGateway.put(eq(false), anyString(), any(ByteArrayInputStream.class)))
                .thenReturn(expectedOssUrl);

        // When
        String result = stockWarnDomainService.generateAndUploadExcel(summary);

        // Then
        assertNotNull(result);
        assertEquals(expectedOssUrl, result);
        verify(ossGateway).put(eq(false), anyString(), any(ByteArrayInputStream.class));
    }

    /**
     * 测试生成Excel - 特殊字符文件名
     */
    @Test
    void testGenerateAndUploadExcel_SpecialCharactersInName() {
        // Given
        StockWarnSummary summary = new StockWarnSummary();
        summary.setOrderPersonnelId(1001L);
        summary.setOrderPersonnelName("张三/李四\\王五:测试*?\"<>|"); // 包含特殊字符
        summary.setOrderPersonnelEmail("<EMAIL>");
        summary.setWarnItems(createTestWarnItems());

        String expectedOssUrl = "https://oss.example.com/stock-warn/张三_李四_王五_测试_20250129_143000.xlsx";
        when(ossGateway.put(eq(false), anyString(), any(ByteArrayInputStream.class)))
                .thenReturn(expectedOssUrl);

        // When
        String result = stockWarnDomainService.generateAndUploadExcel(summary);

        // Then
        assertNotNull(result);
        assertEquals(expectedOssUrl, result);
        // 验证文件名中的特殊字符被正确处理
        verify(ossGateway).put(eq(false), contains("张三_李四_王五_测试_"), any(ByteArrayInputStream.class));
    }

    /**
     * 测试发送邮件 - 空邮箱地址
     */
    @Test
    void testSendWarnEmail_EmptyEmailAddress() {
        // Given
        StockWarnSummary summary = new StockWarnSummary();
        summary.setOrderPersonnelId(1001L);
        summary.setOrderPersonnelName("张三");
        summary.setOrderPersonnelEmail(""); // 空邮箱地址
        summary.setWarnItems(createTestWarnItems());

        String ossUrl = "https://oss.example.com/stock-warn/test.xlsx";
        doNothing().when(emailService).sendSimpleMail(anyString(), anyString(), anyString());

        // When
        assertDoesNotThrow(() -> stockWarnDomainService.sendWarnEmail(summary, ossUrl));

        // Then
        verify(emailService).sendSimpleMail(
                eq(""), // 空邮箱地址
                contains("仓库商品库存告警"),
                contains("仓库负责人")
        );
    }

    /**
     * 测试发送邮件 - null邮箱地址
     */
    @Test
    void testSendWarnEmail_NullEmailAddress() {
        // Given
        StockWarnSummary summary = new StockWarnSummary();
        summary.setOrderPersonnelId(1001L);
        summary.setOrderPersonnelName("张三");
        summary.setOrderPersonnelEmail(null); // null邮箱地址
        summary.setWarnItems(createTestWarnItems());

        String ossUrl = "https://oss.example.com/stock-warn/test.xlsx";
        doNothing().when(emailService).sendSimpleMail(anyString(), anyString(), anyString());

        // When
        assertDoesNotThrow(() -> stockWarnDomainService.sendWarnEmail(summary, ossUrl));

        // Then
        verify(emailService).sendSimpleMail(
                eq(null), // null邮箱地址
                contains("仓库商品库存告警"),
                contains("仓库负责人")
        );
    }

    /**
     * 测试检查库存告警 - 服务异常
     */
    @Test
    void testCheckStockWarn_ServiceException() {
        // Given
        when(categoryWarnStockService.list()).thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then
        assertThrows(RuntimeException.class, () -> 
                stockWarnDomainService.checkStockWarn());
    }

    /**
     * 测试按订单员汇总 - 服务异常
     */
    @Test
    void testSummarizeWarnByOrderPersonnel_ServiceException() {
        // Given
        List<StockWarnInfo> warnInfos = createTestWarnInfos();
        when(dadStaffService.list()).thenThrow(new RuntimeException("员工服务异常"));

        // When & Then
        assertThrows(RuntimeException.class, () -> 
                stockWarnDomainService.summarizeWarnByOrderPersonnel(warnInfos));
    }

    /**
     * 创建测试告警信息列表
     */
    private List<StockWarnInfo> createTestWarnInfos() {
        List<StockWarnInfo> warnInfos = new ArrayList<>();

        StockWarnInfo warnInfo = new StockWarnInfo();
        warnInfo.setItemCode("ITEM001");
        warnInfo.setItemName("测试商品");
        warnInfo.setOrderPersonnelIds(Arrays.asList(1001L));
        warnInfos.add(warnInfo);

        return warnInfos;
    }

    /**
     * 创建测试告警商品列表
     */
    private List<StockWarnInfo> createTestWarnItems() {
        List<StockWarnInfo> warnItems = new ArrayList<>();

        StockWarnInfo warnInfo = new StockWarnInfo();
        warnInfo.setItemCode("ITEM001");
        warnInfo.setItemName("测试商品");
        warnInfo.setCategoryName("电子产品");
        warnInfo.setCurrentStock(new BigDecimal("5"));
        warnInfo.setWarnStock(new BigDecimal("10"));
        warnInfo.setWarehouseName("主仓库");
        warnItems.add(warnInfo);

        return warnItems;
    }

    /**
     * 创建类目警戒库存配置
     */
    private CategoryWarnStock createCategoryWarnStock(Long categoryId, BigDecimal warnStock) {
        CategoryWarnStock categoryWarnStock = new CategoryWarnStock();
        categoryWarnStock.setCategoryId(categoryId);
        categoryWarnStock.setWarnStock(warnStock);
        return categoryWarnStock;
    }

    /**
     * 创建旺店通库存规格
     */
    private WdtStockSpec createWdtStockSpec(String goodsNo, String goodsName, String warehouseNo, 
                                          Long warehouseId, BigDecimal availableStock) {
        WdtStockSpec stockSpec = new WdtStockSpec();
        stockSpec.setGoodsNo(goodsNo);
        stockSpec.setGoodsName(goodsName);
        stockSpec.setWarehouseNo(warehouseNo);
        stockSpec.setWarehouseId(warehouseId);
        stockSpec.setAvailableStock(availableStock);
        stockSpec.setSpecNo(goodsNo + "_SPEC");
        return stockSpec;
    }

    /**
     * 创建类目
     */
    private Category createCategory(Long id, String name) {
        Category category = new Category();
        category.setId(id);
        category.setName(name);
        return category;
    }

    /**
     * 创建仓库
     */
    private Warehouse createWarehouse(String no, String name, String orderPersonnel) {
        Warehouse warehouse = new Warehouse();
        warehouse.setNo(no);
        warehouse.setName(name);
        warehouse.setOrderPersonnel(orderPersonnel);
        return warehouse;
    }

    /**
     * 创建商品
     */
    private Item createItem(String code, Long categoryId) {
        Item item = new Item();
        item.setCode(code);
        item.setCategoryId(categoryId);
        return item;
    }
}
