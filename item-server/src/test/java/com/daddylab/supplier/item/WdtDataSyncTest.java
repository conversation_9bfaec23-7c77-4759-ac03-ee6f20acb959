package com.daddylab.supplier.item;

import com.daddylab.supplier.item.application.item.ItemFetcher;
import com.daddylab.supplier.item.application.item.tasks.WdtItemSyncTask;
import com.daddylab.supplier.item.application.platformItem.tasks.WdtPlatformItemFetcher;
import com.daddylab.supplier.item.application.shop.tasks.ShopSyncTask;
import com.daddylab.supplier.item.infrastructure.utils.ApplicationContextUtil;
import com.daddylab.supplier.item.infrastructure.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.jupiter.api.TestMethodOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

//@SpringBootTest(classes = {
//        NacosConfigAutoConfiguration.class,
//        DataSourceAutoConfiguration.class,
//        DynamicDataSourceAutoConfiguration.class,
//        SpringUtil.class,
//        MybatisPlusAutoConfiguration.class,
//        MybatisPlusConfig.class,
//        WdtDataSyncTest.TestConfig.class,
//        WdtGatewayImpl.class,
//        WdtConfig.class,
//        ShopGatewayImpl.class,
//        ShopSyncTask.class,
//        WdtPlatformItemFetchTask.class,
//        PlatformItemSyncTask.class
//})
@SpringBootTest(properties = {
        "spring.profiles.active:test"
})
@Slf4j
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class WdtDataSyncTest {

    ShopSyncTask shopSyncTask;
    WdtPlatformItemFetcher wdtPlatformItemFetcher;
    WdtItemSyncTask wdtItemSyncTask;

    @Autowired
    public WdtDataSyncTest(ShopSyncTask shopSyncTask,
            WdtPlatformItemFetcher wdtPlatformItemFetcher,
            WdtItemSyncTask wdtItemSyncTask
    ) {
        this.shopSyncTask = shopSyncTask;
        this.wdtPlatformItemFetcher = wdtPlatformItemFetcher;
        this.wdtItemSyncTask = wdtItemSyncTask;
    }

    @Test
    @Order(1)
    @DisplayName("店铺数据同步")
    public void syncShopData() {
        shopSyncTask.doTask();
    }

    @Test
    @Order(2)
    @DisplayName("从旺店通拉取数据")
    public void fetchFromWdt() {
//        wdtPlatformItemFetcher.doTask();
    }

    @Test
    @Order(3)
    @DisplayName("同步平台数据")
    public void syncPlatformItem() {
//        platformItemSyncTask.doTask();
    }

    @Test
    @Order(4)
    @DisplayName("同步后端商品数据")
    public void syncItem() {
//        wdtItemSyncTask.doTask();
    }

    @Test
    @Order(5)
    @DisplayName("同步后端商品数据2")
    public void itemFetcher() {
        ApplicationContextUtil.getBean(ItemFetcher.class).fetch(
                DateUtil.parse("2021-09-04 11:36:45"),
                DateUtil.parse("2021-09-04 11:36:45"),
                1, 100,
                null
        );
    }

    //@Configuration
    //@ComponentScan(basePackages = {
    //        "com.daddylab.supplier.item.infrastructure.gatewayimpl.db"
    //})
    //static class TestConfig {
    //
    //}


}
