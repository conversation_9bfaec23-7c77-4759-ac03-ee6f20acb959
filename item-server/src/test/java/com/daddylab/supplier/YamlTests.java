package com.daddylab.supplier;

import cn.hutool.core.lang.Dict;
import com.daddylab.supplier.item.application.oa.LiveManageConfig;
import com.daddylab.supplier.item.infrastructure.utils.JsonUtil;
import com.google.common.collect.ImmutableMap;
import org.junit.jupiter.api.Test;
import org.springframework.boot.context.properties.bind.BindResult;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.env.YamlPropertySourceLoader;
import org.springframework.core.env.PropertySource;
import org.springframework.core.env.StandardEnvironment;
import org.springframework.core.io.ClassPathResource;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

public class YamlTests {
  @Test
  public void test() throws IOException {
    // Spring Boot方式解析
    ClassPathResource classPathResource = new ClassPathResource("application-local.yaml");
//    YamlPropertySourceLoader yamlPropertySourceLoader = new YamlPropertySourceLoader();
//    List<PropertySource<?>> testPropertySources =
//        yamlPropertySourceLoader.load("test", classPathResource);
//    StandardEnvironment environment = new StandardEnvironment();
//    testPropertySources.forEach(environment.getPropertySources()::addFirst);
    Dict configMap = new Yaml().loadAs(classPathResource.getInputStream(), Dict.class);
    System.out.println("生产配置:\n" + JsonUtil.toJson(configMap.getByPath("live-manage.const-dict-config")));
  }
}
