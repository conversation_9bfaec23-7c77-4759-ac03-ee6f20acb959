<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.daddylab.supplier</groupId>
        <artifactId>jaina</artifactId>
        <version>1.0-SNAPSHOT</version>

    </parent>
    <artifactId>item-server</artifactId>

    <properties>
        <ark-sailor-item-api.version>2024.01.01-SNAPSHOT</ark-sailor-item-api.version>
        <merchant-api.version>4.10.0-SNAPSHOT</merchant-api.version>
        <flowable.version>6.8.0</flowable.version>
        <computervision.version>1.0.9-beta</computervision.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>4.6</version>
        </dependency>
        <dependency>
            <groupId>com.daddylab.supplier</groupId>
            <artifactId>item-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.18.1</version>
        </dependency>

        <!-- logback.xml 支持条件表达式 -->
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.1.8</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>commons-compiler</artifactId>
            <version>3.1.8</version>
        </dependency>

        <dependency>
            <groupId>com.graphql-java</groupId>
            <artifactId>java-dataloader</artifactId>
            <version>3.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.yulichang</groupId>
            <artifactId>mybatis-plus-join</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.baomidou</groupId>
                    <artifactId>mybatis-plus-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>



        <!--<dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.3.0</version>
        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>

        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/commons-collections/commons-collections -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>


        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
        </dependency>
        <!--<dependency>-->
        <!--    <groupId>org.casbin</groupId>-->
        <!--    <artifactId>jcasbin</artifactId>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--    <groupId>org.casbin</groupId>-->
        <!--    <artifactId>casbin-spring-boot-starter</artifactId>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.spring4all</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>io.github.swagger2markup</groupId>
            <artifactId>swagger2markup</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.mail</groupId>
                    <artifactId>mailapi</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.dev33</groupId>
            <artifactId>sa-token-dao-redis-jackson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>

        <!--            金蝶依赖 start-->
        <dependency>
            <groupId>com.daddylab</groupId>
            <artifactId>kingdee-start</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!--            金蝶依赖 end-->


        <dependency>
            <groupId>com.upyun</groupId>
            <artifactId>java-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.inject</groupId>
            <artifactId>javax.inject</artifactId>
            <version>1</version>
        </dependency>
        <dependency>
            <groupId>org.javers</groupId>
            <artifactId>javers-core</artifactId>
            <version>6.6.5</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.4</version>
        </dependency>

        <dependency>
            <groupId>com.github.promeg</groupId>
            <artifactId>tinypinyin</artifactId>
            <version>2.0.3</version>
        </dependency>

        <!--        旺店通SDK-->
        <dependency>
            <groupId>com.daddylab.mall</groupId>
            <artifactId>wdtsdk</artifactId>
            <version>2.1.0-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5-fluent</artifactId>
            <version>5.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.diffplug.durian</groupId>
            <artifactId>durian</artifactId>
            <version>3.4.0</version>
        </dependency>

        <dependency>
            <groupId>org.reflections</groupId>
            <artifactId>reflections</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-openfeign-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba.cloud</groupId>-->
        <!--            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>org.springframework.cloud</groupId>-->
        <!--                    <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.78</version>
        </dependency>

        <!--这个依赖可以在编译时自动处理@ConfigurationProperties注解来生成spring-configuration-metadata.json-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!--淘宝（奇门）SDK-->
        <dependency>
            <groupId>com.daddylab</groupId>
            <artifactId>taobao-sdk-java</artifactId>
            <version>1.0.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.0</version>
        </dependency>

        <!--<dependency>-->
        <!--    <groupId>io.opentracing.contrib</groupId>-->
        <!--    <artifactId>opentracing-spring-cloud-starter</artifactId>-->
        <!--    <version>0.5.9</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--    <groupId>io.jaegertracing</groupId>-->
        <!--    <artifactId>jaeger-client</artifactId>-->
        <!--    <version>1.6.0</version>-->
        <!--</dependency>-->
        <!--<dependency>-->
        <!--    <groupId>io.opentracing.contrib</groupId>-->
        <!--    <artifactId>opentracing-spring-jaeger-starter</artifactId>-->
        <!--    <version>3.3.1</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>8.10.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-trace</artifactId>
            <version>8.10.0</version>
        </dependency>

        <!--调度中心SDK-->
        <dependency>
            <groupId>com.daddylab</groupId>
            <artifactId>daddylab-job-core</artifactId>
            <version>1.2.0-SNAPSHOT</version>
        </dependency>

        <!--炎黄盈动sdk -->
        <dependency>
            <groupId>com.daddylab</groupId>
            <artifactId>aws-api-client</artifactId>
            <version>0.0.1</version>
        </dependency>

        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>2.2.220</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-cp</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bytedance</groupId>
            <artifactId>doudian</artifactId>
            <version>1.1.0-20250917140130</version>
        </dependency>

        <dependency>
            <groupId>org.apdplat</groupId>
            <artifactId>word</artifactId>
        </dependency>
        <dependency>
            <groupId>com.microsoft.azure.cognitiveservices</groupId>
            <artifactId>azure-cognitiveservices-computervision</artifactId>
            <version>${computervision.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.akarnokd</groupId>
            <artifactId>rxjava3-interop</artifactId>
            <version>3.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.daddylab.third.banniu</groupId>
            <artifactId>banniu-spring-boot-starter</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.lmax</groupId>
            <artifactId>disruptor</artifactId>
            <version>3.4.4</version>
        </dependency>

        <!--suppress VulnerableLibrariesLocal -->
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process</artifactId>
            <version>${flowable.version}</version>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-rest</artifactId>
            <version>${flowable.version}</version>
        </dependency>


        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.17.1</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <version>1.10.5</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-batch</artifactId>
        </dependency>

        <dependency>
            <groupId>com.daddylab.ark</groupId>
            <artifactId>ark-sailor-item-api</artifactId>
            <version>${ark-sailor-item-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.daddylab.ark</groupId>
            <artifactId>ark-sailor-item-common-base</artifactId>
            <version>4.12.0</version>
        </dependency>
        <dependency>
            <groupId>com.daddylab.ark</groupId>
            <artifactId>ark-sailor-trade-api</artifactId>
            <version>${merchant-api.version}</version>
            <!--            <exclusions>-->
            <!--                <exclusion>-->
            <!--                    <groupId>com.github.pagehelper</groupId>-->
            <!--                    <artifactId>pagehelper</artifactId>-->
            <!--                </exclusion>-->
            <!--            </exclusions>-->
        </dependency>

        <dependency>
            <groupId>com.daddylab.third</groupId>
            <artifactId>kuaishou-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.daddylab.third</groupId>
            <artifactId>pdd-sdk</artifactId>
            <version>1.17.39-all</version>
            <classifier>all</classifier>
        </dependency>

        <dependency>
            <groupId>com.nuonuo</groupId>
            <artifactId>open-sdk</artifactId>
            <version>1.0.5.2</version>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.19.2</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>

        <!-- Jackson version override for compatibility -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.13.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.13.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.13.3</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.fasterxml.jackson.module</groupId>-->
<!--            <artifactId>jackson-module-kotlin</artifactId>-->
<!--            <version>2.13.3</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.13.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
            <version>2.13.3</version>
        </dependency>

        <dependency>
            <groupId>com.daddylab.third</groupId>
            <artifactId>redbook-sdk</artifactId>
        </dependency>

        <dependency>
            <groupId>com.douyin.openapi</groupId>
            <artifactId>sdk</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>oceanengine-mapi-java-client</artifactId>
            <version>[0.0.1,)</version>
        </dependency>
        <dependency>
            <groupId>org.checkerframework</groupId>
            <artifactId>checker-qual</artifactId>
            <version>3.49.0</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <directory>${project.basedir}/target</directory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M6</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ftl</nonFilteredFileExtension>
                        <nonFilteredFileExtension>html</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>docx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>doc</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.7.RELEASE</version>
                <configuration>
                    <mainClass>com.daddylab.supplier.item.ItemApplication</mainClass>
                    <!-- 将SystemScope配置的依赖打包到构建包中，或者通过配置resource来处理，见下resources配置 -->
                    <includeSystemScope>true</includeSystemScope>
                    <excludes>
                        <!--官方文档建议在打包时排除掉这个依赖-->
                        <exclude>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <copy file="${project.build.directory}/${project.build.finalName}.jar"
                                      todir="${project.basedir}/../target"
                                      overwrite="true"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>

        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </testResource>
            <testResource>
                <directory>src/main/resources/processes</directory>
                <includes>
                    <include>*.xml</include>
                </includes>
            </testResource>
        </testResources>
    </build>

    <profiles>
        <profile>
            <id>local-azu</id>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <profileActive>local</profileActive>
                <nacos.server>172.16.12.151:8848,172.16.12.193:8848,172.16.12.172:8848</nacos.server>
                <nacos.username>nacos</nacos.username>
                <nacos.password>82CVXbpDsTabVS</nacos.password>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <finalName>server</finalName>
                <directory>${project.basedir}/target</directory>
            </build>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <profileActive>dev</profileActive>
                <nacos.server>172.16.12.151:8848,172.16.12.193:8848,172.16.12.172:8848</nacos.server>
                <nacos.username>nacos</nacos.username>
                <nacos.password>82CVXbpDsTabVS</nacos.password>
            </properties>
            <build>
                <finalName>server</finalName>
                <directory>${project.basedir}/target</directory>
            </build>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
                <nacos.server>172.16.12.151:8848,172.16.12.193:8848,172.16.12.172:8848</nacos.server>
                <nacos.username>nacos</nacos.username>
                <nacos.password>82CVXbpDsTabVS</nacos.password>
            </properties>
            <build>
                <finalName>server</finalName>
                <directory>${project.basedir}/target</directory>
            </build>

        </profile>

        <!--pre环境已废弃-->
        <profile>
            <id>pre</id>
            <properties>
                <profileActive>pre</profileActive>
            </properties>
            <build>
                <finalName>server</finalName>
                <directory>${project.basedir}/target</directory>
            </build>

        </profile>

        <profile>
            <id>gray</id>
            <properties>
                <profileActive>gray</profileActive>
                <nacos.server>192.168.1.101:8848,192.168.1.102:8848,192.168.1.103:8848</nacos.server>
                <!--<nacos.server>https://nacos.daddylab.com</nacos.server>-->
                <nacos.username>dlab_developer</nacos.username>
                <nacos.password>zvcPEuqmcXsxQ6Fw</nacos.password>
            </properties>
            <build>
                <finalName>server</finalName>
                <directory>${project.basedir}/target</directory>
            </build>

        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profileActive>prod</profileActive>
                <nacos.server>192.168.1.101:8848,192.168.1.102:8848,192.168.1.103:8848</nacos.server>
                <!--<nacos.server>https://nacos.daddylab.com</nacos.server>-->
                <nacos.username>dlab_developer</nacos.username>
                <nacos.password>zvcPEuqmcXsxQ6Fw</nacos.password>
            </properties>
            <build>
                <finalName>server</finalName>
                <directory>${project.basedir}/target</directory>
            </build>

        </profile>
    </profiles>
</project>
