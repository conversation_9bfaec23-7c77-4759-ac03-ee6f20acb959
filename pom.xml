<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <packaging>pom</packaging>

    <groupId>com.daddylab.supplier</groupId>
    <artifactId>jaina</artifactId>
    <version>1.0-SNAPSHOT</version>

    <modules>
        <module>item-api</module>
        <module>item-server</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <cola.components.version>4.0.1</cola.components.version>

        <mybatis-plus.version>3.4.0</mybatis-plus.version>
        <mybatis-plus-join.version>1.2.1</mybatis-plus-join.version>
        <dubbo.version>3.0.1</dubbo.version>
        <guava.version>30.1.1-jre</guava.version>
        <mapstruct.version>1.4.1.Final</mapstruct.version>
        <redisson.version>3.16.1</redisson.version>
        <hutool.version>5.8.20</hutool.version>
        <nacos-config.version>0.2.10</nacos-config.version>
        <easyexcel.version>3.0.2</easyexcel.version>
        <!--        <commons-pool2.version>2.10.0</commons-pool2.version>-->
        <freemarker.version>2.3.28</freemarker.version>
        <protostuff.version>1.7.2</protostuff.version>
        <open-feign.version>2.2.7.RELEASE</open-feign.version>
        <swagger.version>1.9.0.RELEASE</swagger.version>
        <sa-token.version>1.26.0</sa-token.version>
        <!--        <powerjob-worker.version>4.0.1</powerjob-worker.version>-->
        <upyun.version>4.2.2</upyun.version>
        <caffeine.version>2.9.9</caffeine.version>
        <rocketmq.version>2.2.1</rocketmq.version>

        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <spring-cloud-alibaba.version>2.2.8.RELEASE</spring-cloud-alibaba.version>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <wx.version>4.3.0</wx.version>
        <redbook.version>1.0.0</redbook.version>
        <kuaishou.version>1.0.0</kuaishou.version>
        <gson-version>2.9.1</gson-version>
        <okhttp-version>4.10.0</okhttp-version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-domain-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cola</groupId>
            <artifactId>cola-component-catchlog-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--基础架构依赖-->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--基础架构依赖 end -->


            <!--COLA Components-->
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-domain-starter</artifactId>
                <version>${cola.components.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-exception</artifactId>
                <version>${cola.components.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-dto</artifactId>
                <version>${cola.components.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cola</groupId>
                <artifactId>cola-component-catchlog-starter</artifactId>
                <version>${cola.components.version}</version>
            </dependency>
            <!--COLA Components End-->

            <!--            orm start-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join</artifactId>
                <version>${mybatis-plus-join.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>${freemarker.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!--            orm end-->

            <!--            java lib start -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <!--            java lib end-->

            <!--            bean mapping start-->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <!--            bean mapping end-->

            <!--            redisson start-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <!--            redisson end-->

            <!--            tool start-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!--            tool end-->

            <!--            excel start-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <!--            excel end-->

            <!--            api doc start-->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.21</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.21</version>
            </dependency>
            <dependency>
                <groupId>com.spring4all</groupId>
                <artifactId>swagger-spring-boot-starter</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            <!--            生成离线文档-->
            <dependency>
                <groupId>io.github.swagger2markup</groupId>
                <artifactId>swagger2markup</artifactId>
                <version>1.3.3</version>
            </dependency>
            <!--            api doc end-->

            <!--            登录，权限 start-->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-dao-redis-jackson</artifactId>
                <version>${sa-token.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>org.casbin</groupId>-->
<!--                <artifactId>jcasbin</artifactId>-->
<!--                <version>1.19.0</version>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.casbin</groupId>-->
<!--                <artifactId>casbin-spring-boot-starter</artifactId>-->
<!--                <version>0.1.2</version>-->
<!--            </dependency>-->
            <!--            登录，权限 end-->

            <!--            修坑依赖。别动！！！-->
            <dependency>
                <groupId>com.alibaba.spring</groupId>
                <artifactId>spring-context-support</artifactId>
                <version>1.0.11</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.13</version>
            </dependency>

            <dependency>
                <groupId>com.upyun</groupId>
                <artifactId>java-sdk</artifactId>
                <version>${upyun.version}</version>
            </dependency>

            <!--            缓存-->
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.9.0</version>

            </dependency>

            <dependency>
                <groupId>org.reflections</groupId>
                <artifactId>reflections</artifactId>
                <version>0.10.2</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpmime</artifactId>
                <version>4.3.5</version>
            </dependency>

            <dependency>
                <groupId>org.apache.rocketmq</groupId>
                <artifactId>rocketmq-spring-boot-starter</artifactId>
                <version>${rocketmq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-cp</artifactId>
                <version>${wx.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apdplat</groupId>
                <artifactId>word</artifactId>
                <version>1.3</version>
            </dependency>

            <dependency>
                <groupId>com.daddylab.third</groupId>
                <artifactId>redbook-sdk</artifactId>
                <version>${redbook.version}</version>
            </dependency>

            <dependency>
                <groupId>com.daddylab.third</groupId>
                <artifactId>kuaishou-sdk</artifactId>
                <version>${kuaishou.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-batch</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson-version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp-version}</version>
            </dependency>
            <!-- 添加 logging-interceptor 依赖并指定与 okhttp 相同的版本 -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp-version}</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <directory>${project.basedir}/target</directory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.7.RELEASE</version>
                <configuration>
                    <mainClass>ItemApplication</mainClass>
                    <!-- 将SystemScope配置的依赖打包到构建包中，或者通过配置resource来处理，见下resources配置 -->
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <executions>
                    <execution>
                        <id>repackage</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>daddylab-pubic</id>
            <name>daddylab public</name>
            <url>http://nexus.dlab.cn/repository/daddylab-public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
            <layout>default</layout>
        </repository>
        <repository>
            <id>douyin-openapi-repo</id>
            <url>https://artifacts-cn-beijing.volces.com/repository/douyin-openapi/</url>
        </repository>
        <repository>
            <id>OceanengineOpenApi</id>
            <name>ad_open_sdk_java</name>
            <layout>default</layout>
            <url>https://artifact.bytedance.com/repository/releases/</url>
        </repository>
    </repositories>

</project>
